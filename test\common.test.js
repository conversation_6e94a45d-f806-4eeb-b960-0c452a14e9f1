/* eslint-disable no-console */
/* eslint-disable no-undef */

const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 9500,
    app_key: 'iyunxiao_kbplat180305',
};
 
/* const localEnv = {
    host: 'kb-api.yunxiao.com',
    port: 80,
    app_key: 'iyunxiao_kbplat180305',
}; */

describe('资源获取', function () {
    this.timeout(90000);
    it.only('/kb_api/v2/resources/', async function () {
        let pathname = '/kb_api/v2/resources/';
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            collection: 'exampaper',
            query: { _id: 3397870788 },
            project: { block: 1, period: 1, subject: 1, name: 1, from: 1, use_type:1 },
            limit: 3,
            offset: 0
        };
        let result = await request.post(url).send(data);
        console.log(result.body);
        assert.ok(Array.isArray(result.body));
        assert.ok(result.body.length > 0);
    });
});

describe('资源修改', function () {
    this.timeout(90000);
    it('/kb_api/v2/resources/', async function () {
        let pathname = '/kb_api/v2/resources/';
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            collection: 'exampaper',
            query: { _id: 3397870788 },
            update: { $set: { from: 'mkp', use_type: 'ctb' } },
            options: {}
        };
        let result = await request.put(url).send(data);
        console.log(result.body);
        assert.ok(result.body.hasOwnProperty('matchedCount'));
        assert.ok(result.body.hasOwnProperty('modifiedCount'));
    });
});