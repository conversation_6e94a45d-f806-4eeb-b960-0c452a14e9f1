// 知识点相关内容
const knowledge_tree = require('../../../modules/kb_api/v2/knowledge_tree');
const knowledge_tree_batch = require('../../../modules/kb_api/v2/knowledge_tree/batch');
const knowledge = require('../../../modules/kb_api/v2/knowledge');
const question = require('../../../modules/kb_api/v2/question');
const knowledgeMap = require('../../../modules/kb_api/v2/knowledge_map');
const dmp_question = require('../../../modules/kb_api/v2/question/dmp_question');
const knowMethod = require('../../../modules/kb_api/v2/know_method');
const target = require('../../../modules/kb_api/v2/target');
const knowModule = require('../../../modules/kb_api/v2/module');
const knowMethodGroup = require('../../../modules/kb_api/v2/know_method_group');
const targetGroup = require('../../../modules/kb_api/v2/target_group');
const category_knowledge = require('../../../modules/kb_api/v2/dmp_resource/category_knowledge');
const knowledgeTag = require('../../../modules/kb_api/v2/knowledge_tag');
const knowledgeInfo = require('../../../modules/kb_api/v2/knowledge_info');
const knowledgeExamTime = require('../../../modules/kb_api/v2/knowledge_exam_time');
const knowAnalysis = require('../../../modules/kb_api/v2/knowledge_analysis');

module.exports = function (router) {

    // 根据试卷，分析知识点
    router.post('/knowledges/analysis', knowAnalysis.getKnowledgeAnalysis);

    // 请求知识树信息（打包信息
    router.get('/knowledge_trees/', knowledge_tree.knowledge_trees);

    // 检查知识树是否冲突
    router.get('/knowledge_trees/check/', knowledge_tree.check_tree_conflict);
    // 通过id请求某知识树下所有节点及其知识点集
    router.get('/knowledge_trees/detail', knowledge_tree_batch.batchGetKnowledgeTreeDetail);
    router.get('/knowledge_trees/:knowledge_tree_id', knowledge_tree.knowledge_tree);
    router.get('/knowledges/by_tree/:knowledge_tree_id', knowledge_tree.getKnowledgesByKnowledgeTree);

    // 请求某知识树下所有节点及其知识点集
    router.get('/knowledge_trees/knowledge_nodes/knowledges', knowledge_tree.knowledge_tree_by_name);
    // 获取知识树章节
    router.get('/knowledge_trees/:knowledge_tree_id/struct', knowledge_tree.knowledge_tree_struct);

    // 获取知识树章节信息
    router.get('/knowledge_tree_chapters/:tree_chapter_id/', knowledge_tree.tree_chapter);
    // 同步知识树chapter
    router.post('/knowledge_tree_chapters/', knowledge_tree.post_tree_chapters);
    // 同步教材结构
    router.post('/knowledge_trees/struct/', knowledge_tree.post_tree_struct);
    //调整教材编目顺序
    router.put('/knowledge_tree_catalogs/', knowledge_tree.put_tree_catalog);

    // 删除知识树内容
    router.delete('/knowledge_trees/:id', knowledge_tree.delete_tree);

    router.get('/knowledges/batch/', knowledge.batch_get_knowledge);
    router.get('/knowledges/:knowledge_ids/videos', knowledge.getKnowledgesVideos);
    // 根据多个知识点id，获取知识点关联视频信息
    router.get('/knowledges/:knowledge_ids/videos/relation', knowledge.getKnowledgesVideosRelaton);
    // 修改前后置知识点
    router.put('/knowledges/:knowledge_id/pre_next', knowledge.updatePreNextKnowledge);
    // 基于知识点id，请求知识点关联视频详细列表
    router.get('/knowledges/:knowledge_ids/videos/details', knowledge.getKnowledgesVideosDetails);
    // 知识点与视频之间操作：关联/外显/取消关联/取消外显
    router.put('/knowledges/:knowledge_id/videos/:video_id', knowledge.updateKnowledgesVideos);

    // 提供给hfs使用的接口: 获取全量知识点的score和chance信息
    router.get('/knowledges/score_chance', knowledge.knowledge_score_chance);

    // 验证这个学科，学段，这个名称知识点是否存在
    router.get('/knowledges/existing', knowledge.checkKnowledgeExits);
    // 基于知识点id，请求知识点内容信息
    router.get('/knowledges/:knowledge_id', knowledge.knowledge);

    // 基于关键词模糊查询知识点信息
    router.get('/search/knowledges/:key_word', knowledge.search_knowledges);

    // 修改知识点内容
    router.put('/knowledges/:knowledge_id', knowledge.modify_knowledge);
    // 新增知识点内容
    router.post('/knowledges/', knowledge.insert_knowledge);

    // 请求某知识点下试题信息
    router.get('/knowledges/:knowledge_ids/questions', question.knowledge_questions);
    
    router.post('/knowledges/:knowledge_ids/questions3', dmp_question.searchQuestions);
    // 请求某知识点下试题信息
    router.get('/knowledges/:knowledge_ids/questions/samples', knowledge.getKnowledgesQuestionsSamples);
    
    // 请求某知识点下试题信息
    router.get('/knowledges/:knowledge_ids/questions_count', knowledge.getKnowledgesQuestionsCount);
    // 请求某知识点下考法和对象等
    router.post('/knowledges/batch/relation', knowledge.getBatchRelation);
    
    // dmp 根据知识点推荐试题
    router.post('/knowledges/question_suggest', dmp_question.suggestQuestions);
    router.post('/knowledges/question_suggest_by_se', dmp_question.suggestQuestionsForMultiSupport);
    router.post('/knowledges/true_questions', dmp_question.getTrueQuestionsByKnowledgeIds);

    // dmp 知识点卡片资源相关
    router.get('/category_knowledge/batch', category_knowledge.getCategoryKnowledge);
    router.post('/category_knowledge/batch', category_knowledge.saveBatchCategoryKnowledge);
    router.put('/category_knowledge/batch', category_knowledge.setBatchCategoryKnowledge);

    router.get('/knowledges/names/:names', knowledge.getKnowledgeByNames);

    router.get('/knowledges/:knowledge_ids/card_img/', knowledge.getKnowledgeCardImage);
    // 支持辅导
    router.get('/knowledges/external/fudao', knowledge.getKnowledgeForFuDao);
    // 辅导：获取末级知识点的父级知识点
    router.get('/knowledges/:knowledge_ids/parent', knowledge.getKnowledgeParent);

    /* 知识点映射相关 */
    // 更新映射
    router.put('/knowledge_map/:out_id', knowledgeMap.updateKnowledgeMap);
    // 待映射知识点列表
    router.get('/knowledge_map/unmap/list', knowledgeMap.getKnowledgeUnmapList);
    // 已映射知识点列表
    router.get('/knowledge_map/mapped/list', knowledgeMap.getKnowledgeMappedList);

    /**************   细分相关    *************/
    // 判断细分类别是否存在
    router.get('/know_methods_categorys/exist', knowMethod.validateKnowMethodCategoryExist);
    // 判断细分是否存在
    router.get('/know_methods/exist', knowMethod.validateKnowMethodExist);
    // 获取细分类别
    router.get('/know_methods_categorys', knowMethod.getKnowMethodList);
    // 获取细分类别详情
    router.get('/know_methods_categorys/:id', knowMethod.getKnowMethodDetail);
    // 新增细分类别
    router.post('/know_methods_categorys', knowMethod.postKnowMethod);
    // 修改细分类别
    router.put('/know_methods_categorys/:id', knowMethod.updateKnowMethod);
    // 删除细分类别
    router.delete('/know_methods_categorys/:id', knowMethod.deleteKnowMethod);
    router.post('/know_methods_categorys/submit/is_repeat_name', knowMethod.isRepeatName);


    /**************   对象相关    *************/
    // 判断对象类别是否存在
    router.get('/targets_categorys/exist', target.validateTargetCategoryExist);
    // 判断对象是否存在
    router.get('/targets/exist', target.validateTargetExist);
    // 获取对象类别
    router.get('/targets_categorys', target.getTargetCategoryList);
    // 获取对象详情
    router.get('/targets_categorys/:id', target.getTargetCategoryDetail);
    // 新增对象类别
    router.post('/targets_categorys', target.postTargetCategory);
    // 修改对象类别
    router.put('/targets_categorys/:id', target.updateTargetCategory);
    // 删除对象类别
    router.delete('/targets_categorys/:id', target.deleteTargetCategory);
    router.post('/targets_categorys/submit/is_repeat_name', target.isRepeatName);

    /* 模块相关  */
    // 获取模块详情，根据学段学科
    router.get('/modules/list', knowModule.getModulesList);
    // 获取模块详情，根据id
    router.get('/modules/:id', knowModule.getModuleById);
    // kbp审核通过，推送过来，更新
    router.put('/modules/:id', knowModule.updateModule);
    // kbp审核通过，推送过来，添加
    router.post('/modules', knowModule.postModule);

    /* 细分(考法)组相关  */
    // 获取列表
    router.get('/know_method_groups/list', knowMethodGroup.getGroupsList);
    // 检查组名称是否重复
    router.get('/know_method_groups/repeat', knowMethodGroup.isGroupRepeat);
    // 获取模块详情，根据id
    router.get('/know_method_groups/:id', knowMethodGroup.getGroupById);
    // kbp审核通过，推送过来，更新
    router.put('/know_method_groups/:id', knowMethodGroup.updateGroup);
    // kbp审核通过，推送过来，添加
    router.post('/know_method_groups', knowMethodGroup.postGroup);
    // kbp审核通过，推送过来，删除
    router.delete('/know_method_groups/:id', knowMethodGroup.deleteGroup);
    

    /* 对象组相关  */
    // 获取列表
    router.get('/target_groups/list', targetGroup.getGroupsList);
    // 检查组名称是否重复
    router.get('/target_groups/repeat', targetGroup.isGroupRepeat);
    // 获取详情，根据id
    router.get('/target_groups/:id', targetGroup.getGroupById);
    // kbp审核通过，推送过来，更新
    router.put('/target_groups/:id', targetGroup.updateGroup);
    // kbp审核通过，推送过来，添加
    router.post('/target_groups', targetGroup.postGroup);
    // kbp审核通过，推送过来，删除
    router.delete('/target_groups/:id', targetGroup.deleteGroup);

    /* 知识点标签相关  */
    // 获取列表
    router.get('/knowledge_tags/list', knowledgeTag.getKnowledgeTagsList);
    // 检查标签组名称是否可以编辑
    //router.get('/knowledge_tags/canedit/:type', knowledgeTag.isKnowledgeTagRepeat);
    // 获取详情，根据id
    router.get('/knowledge_tags/:id', knowledgeTag.getKnowledgeTagById);
    // 修改接口
    router.put('/knowledge_tags/:id', knowledgeTag.updateKnowledgeTag);
    // 判断标签是否重复
    router.get('/knowledge_tags/tags/repeat/:name', knowledgeTag.repeatKnowledgeTag);
    // 判断是否可以删除
    router.get('/knowledge_tags/tags/candelete/:name', knowledgeTag.candelKnowledgeTag);
    
    /* 知识点统计信息  */
    // 获取知识点统计
    router.get('/knowledge_info/:knowledge_id', knowledgeInfo.getKnowledgeInfo);
    router.get('/knowledge_info/batch/:knowledge_ids', knowledgeInfo.getKnowledgeBatchInfos);
    // 根据批量知识点id，获取批量知识点本地考频数据
    router.post('/knowledge_exam_times/batch', knowledgeExamTime.getKnowledgeExamTimes);
};
