var Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
var utils = require('../../common/utils/utils.js');
var params_utils = require('../../common/utils/params.js');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../common/utils/rediser');
const cache = rediser.getCache();
var kb_api = require('../config.js').kb_api['v2'];

/*
 * DESC:
 * 		请求全部地域资源
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/regions/
 * Method:
 * 		GET
 * Author:
 * 		zhangjun
 * Date:
 * 		2016-08-26
 */
function regions(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	var params = null;
	try {
		var fun_name = arguments.callee.name;
		params = params_utils.create_params(req, kb_api[fun_name]);

		// 数据查询 ----------------------------
		// 查询试卷
		var cache_key = params['cache_key'];
		rediser.get(cache_key, function (err, item) {
			if (err) {
				return responseWrapper.error('PARAMETERS_ERROR', err.message);
			}
			if (!item) {
				var level = params['level'];
				var coll = db.collection('region');
				// 查询条件
				var cond = {};
				// 提取出的字段
				var proj = {};
				// 查询
				coll.find(cond).project(proj).toArray(function (err, items) {
					if (err) {
						return responseWrapper.error('PARAMETERS_ERROR', err.message);
					}
					if (items.length > 0) {
						for (var i in items) {
							var province = items[i];
							norm_region(province, level);
							var cities = province['children'];
							for (var j in cities) {
								var city = cities[j];
								norm_region(city, level);
								var coutries = city['children'];
								for (var k in coutries) {
									var country = coutries[k];
									norm_region(country, level);
								}
							}
						}
						var result = {
							'region': {
								'children': items,
							}
						};
						rediser.set(cache_key, result);
						return responseWrapper.succ(result);
					}
					return responseWrapper.error('NULL_ERROR');
				});
			} else {
				return responseWrapper.succ(item);
			}
		});
	} catch (e) {
		Logger.error(e);
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	// }).then(function(cont, result) {
	// 	if (result) {
	// 		return responseWrapper.succ(result);
	// 	} else {
	// 		return responseWrapper.error('NULL_ERROR');
	// 	}
	// }).fail(function(cont, error) {
	// 	Logger.error(error);
	// 	return responseWrapper.error('HANDLE_ERROR');
	// });
}

/*
 * DESC:
 * 		请求全部地域资源
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/regions/simple/
 * Method:
 * 		GET
 * Author:
 * 		zhangjun
 * Date:
 * 		2017-03-20
 */
function simple_regions(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	var params = null;
	try {
		var fun_name = arguments.callee.name;
		params = params_utils.create_params(req, kb_api[fun_name]);

		// 两个版本的 region 一个增加了 【全国】【全部地区】【全部城市】
		const version = +req.query.version || 1;
		let coll = version === 1 ? db.collection('region_simple') : db.collection('region_simple_v2');

		// 数据查询 ----------------------------
		// 查询试卷
		var cache_key = `${params['cache_key']}:v${version}`;
		rediser.get(cache_key, function (err, item) {
			if (err) {
				return responseWrapper.error('PARAMETERS_ERROR', err.message);
			}
			if (!item) {
				var level = null;
				// 查询条件
				var cond = {};
				// 提取出的字段
				var proj = { order_no: 0 };
				// 提取出的字段
				var sort_by = { order_no: 1 };
				// 查询
				coll.find(cond).project(proj).sort(sort_by).toArray(function (err, items) {
					if (err) {
						return responseWrapper.error('PARAMETERS_ERROR', err.message);
					}
					if (items.length > 0) {
						for (var i in items) {
							var province = items[i];
							norm_region(province, level);
							var cities = province['children'];
							for (var j in cities) {
								var city = cities[j];
								norm_region(city, level);
							}
						}
						var result = {
							'region': {
								'children': items,
							}
						};
						rediser.set(cache_key, result, 60 * 60 * 24);
						return responseWrapper.succ(result);
					}
					return responseWrapper.error('NULL_ERROR');
				});
			} else {
				return responseWrapper.succ(item);
			}
		});
	} catch (e) {
		Logger.error(e);
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
}

function norm_region(json, level) {
	if (!json) {
		return;
	}
	delete json['_id'];
	if (!level || (level && json['level'] < level)) {
		json['children'] = json['subs'];
	}
	delete json['subs'];
}

module.exports = {
	regions: regions,
	simple_regions: simple_regions,
}
