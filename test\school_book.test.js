/* eslint-disable no-undef */
const config = require('config');
const expect = require('chai').expect;
const superagent = require('superagent');
const url = require('url');
const host = url.format({
    protocol: 'http',
    hostname: 'localhost',//kb.gotiku.com
    port: 9500
});
const apikey = config.get('app_keys_private')[0];
const school_id = 2136735743;
describe('获取学校列表 /school_books/schools', function() {
    this.timeout(900000);
    it.only('正例测试:获取学校列表', (done) => {
        let url = [host, '/kb_api/v2/school_books/schools'].join('');
        let data = {
            app_key: apikey,
            query: '韩', // [必选]搜索关键字。
            offset: 0, // [可选]分页参数，起始条目，从0开始  默认0                         
            limit: 10, // [可选]分页参数，每页条目数量 默认10
        };
        superagent.get(url).query(data).end(function (err, res) {
            expect(err).to.be.an('null');
            expect(res).to.not.be.an('null');
            expect(res.status).to.be.equal(200);
            let ret = res.body;
            expect(ret.code).to.be.equal(0);
            expect(ret.data.schools).to.be.an('array');
            if (ret.data.schools.length > 0) {
                for (let school of ret.data.schools) {
                    expect(school).to.have.keys('sch_id', 'sch_name');
                }
            }
            done();
        });
    });
});

describe('查询学校教材详情 /school_books/schools/{sch_id}', () => {
    it('正例测试:查询学校教材详情', (done) => {
        let url = [host, '/kb_api/v2/school_books/schools/', school_id].join('');
        let data = { app_key: apikey, cache: 'no' };
        superagent.get(url).query(data).end(function (err, res) {
            expect(err).to.be.an('null');
            expect(res).to.not.be.an('null');
            expect(res.status).to.be.equal(200);
            console.log(res.text);
            let ret = JSON.parse(res.text);
            expect(ret.code).to.be.equal(0);
            expect(ret.data.school).to.be.an('object');
            expect(ret.data.school).to.have.keys('sch_id', 'sch_name', 'province', 'city', 'country');
            expect(ret.data.books).to.be.an('array');
            if (ret.data.books.length > 0) {
                for (let data1 of ret.data.books) {
                    expect(data1).to.have.keys('name', 'grades');
                    expect(data1.grades).to.be.an('array');
                    if (data1.grades.length > 0) {
                        for (let data2 of data1.grades) {
                            expect(data2).to.have.keys('name', 'subjects');
                            expect(data2.subjects).to.be.an('array');
                            if (data2.subjects.length > 0) {
                                for (let data3 of data2.subjects) {
                                    expect(data3).to.have.keys('name', 'press_version');
                                }
                            }
                        }
                    }
                }
            }
            done();
        });
    });
});

describe('添加学校 /school_books/schools', () => {
    it('正例测试', (done) => {
        let url = [host, '/kb_api/v2/school_books/schools'].join('');
        let body = {
            id: school_id,
            name: '吴金锋学校1',
            'province': '安徽',
            'city': '阜阳',
            'country': '太和',
            periods: [{
                name: '高中',
                grades: [
                    { name: '高一', subjects: [] },
                    { name: '高二', subjects: [] },
                    { name: '高三', subjects: [] }
                ]
            }]
        };
        let data = { app_key: apikey };
        superagent.post(url).query(data).set('Content-Type', 'application/json').send(body).end(function (err, res) {
            expect(err).to.be.an('null');
            expect(res).to.not.be.an('null');
            expect(res.status).to.be.equal(200);
            let ret = res.body;
            console.log(ret);
            expect(ret).to.be.an('object');
            expect(ret.code).to.be.equal(0);
            done();
        });
    });
});

describe('更新学校 /school_books/schools/:sch_id', () => {
    it('正例测试', (done) => {
        let url = [host, '/kb_api/v2/school_books/schools/' + school_id].join('');
        let body = {
            name: '吴金锋学校2',
            'province': '安徽',
            'city': '阜阳',
            'country': '太和',
            periods: [{
                name: '高中',
                grades: [
                    { name: '高一', subjects: [] },
                    { name: '高二', subjects: [] },
                    { name: '高三', subjects: [] }
                ]
            }]
        };
        let data = { app_key: apikey };
        superagent.put(url).query(data).set('Content-Type', 'application/json').send(body).end(function (err, res) {
            expect(err).to.be.an('null');
            expect(res).to.not.be.an('null');
            expect(res.status).to.be.equal(200);
            let ret = res.body;
            console.log(ret);
            expect(ret).to.be.an('object');
            expect(ret.code).to.be.equal(0);
            done();
        });
    });
});

describe('设置学校区域配置 /school_books/schools/{sch_id}/region', () => {
    it('正例测试:设置学校区域配置', (done) => {
        let url = [host, '/kb_api/v2/school_books/schools/' + school_id + '/region',
        ].join('');
        let body = {
            name: '吴金锋学校',
            'province': '安徽',
            'city': '阜阳',
            'country': '太和'
        };
        let data = { app_key: apikey };
        superagent.put(url).query(data).set('Content-Type', 'application/json').send(body).end(function (err, res) {
            expect(err).to.be.an('null');
            expect(res).to.not.be.an('null');
            expect(res.status).to.be.equal(200);
            console.log(res.text);
            let ret = res.body;
            expect(ret).to.be.an('object');
            expect(ret.code).to.be.equal(0);
            done();
        });
    });
});

describe('添加、编辑、移除 教材 /school_books/schools/{sch_id}/}/books', () => {
    it('正例测试:添加、编辑、移除 教材', (done) => {
        let url = [host, '/kb_api/v2/school_books/schools/' + school_id + '/books'].join('');
        let body = {
            'periods': [
                {
                    'grades': [
                        {
                            'subjects': [
                                {
                                    'press_version': '人教版',
                                    'name': '物理'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '生物'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '英语'
                                },
                                {
                                    'press_version': '新人教版',
                                    'name': '数学'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '化学'
                                },
                                {
                                    'press_version': '浙教版',
                                    'name': '科学'
                                }
                            ],
                            'name': '七年级'
                        },
                        {
                            'subjects': [
                                {
                                    'press_version': '人教版',
                                    'name': '物理'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '生物'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '英语'
                                },
                                {
                                    'press_version': '新人教版',
                                    'name': '数学'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '化学'
                                },
                                {
                                    'press_version': '浙教版',
                                    'name': '科学'
                                }
                            ],
                            'name': '八年级'
                        },
                        {
                            'subjects': [
                                {
                                    'press_version': '人教版',
                                    'name': '物理'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '生物'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '英语'
                                },
                                {
                                    'press_version': '新人教版',
                                    'name': '数学'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '化学'
                                },
                                {
                                    'press_version': '浙教版',
                                    'name': '科学'
                                }
                            ],
                            'name': '九年级'
                        }
                    ],
                    'name': '初中'
                }
            ]
        };
        let data = { app_key: apikey };
        superagent.put(url).query(data).set('Content-Type', 'application/json').send(body).end(function (err, res) {
            expect(err).to.be.an('null');
            expect(res).to.not.be.an('null');
            expect(res.status).to.be.equal(200);
            let ret = JSON.parse(res.text);
            expect(ret).to.be.an('object');
            expect(ret.code).to.be.equal(0);
            done();
        });
    });
    it('反例测试:添加、编辑、移除 教材  破坏已有学段 年级', (done) => {
        let url = [host, '/kb_api/v2/school_books/schools/' + school_id + '/books',
        ].join('');
        let body = {
            'periods': [
                {
                    'grades': [
                        {
                            'subjects': [
                                {
                                    'press_version': '人教版',
                                    'name': '物理'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '生物'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '英语'
                                },
                                {
                                    'press_version': '新人教版',
                                    'name': '数学'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '化学'
                                },
                                {
                                    'press_version': '浙教版',
                                    'name': '科学'
                                }
                            ],
                            'name': '七年级1'
                        },
                        {
                            'subjects': [
                                {
                                    'press_version': '人教版',
                                    'name': '物理'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '生物'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '英语'
                                },
                                {
                                    'press_version': '新人教版',
                                    'name': '数学'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '化学'
                                },
                                {
                                    'press_version': '浙教版',
                                    'name': '科学'
                                }
                            ],
                            'name': '八年级'
                        },
                        {
                            'subjects': [
                                {
                                    'press_version': '人教版',
                                    'name': '物理'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '生物'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '英语'
                                },
                                {
                                    'press_version': '新人教版',
                                    'name': '数学'
                                },
                                {
                                    'press_version': '人教版',
                                    'name': '化学'
                                },
                                {
                                    'press_version': '浙教版',
                                    'name': '科学'
                                }
                            ],
                            'name': '九年级'
                        }
                    ],
                    'name': '初中1'
                }
            ]
        };
        superagent
            .put(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(4);
                done();
            });
    });
});


describe('删除学校 /school_books/schools/:sch_id', () => {
    it('正例测试:设置学校区域配置', (done) => {
        let url = [host, '/kb_api/v2/school_books/schools/' + school_id].join('');
        let body = {};
        let data = { app_key: apikey };
        superagent.delete(url).query(data).set('Content-Type', 'application/json').send(body).end(function (err, res) {
            expect(err).to.be.an('null');
            expect(res).to.not.be.an('null');
            expect(res.status).to.be.equal(200);
            let ret = res.body;
            console.log(ret);
            expect(ret).to.be.an('object');
            expect(ret.code).to.be.equal(0);
            done();
        });
    });
});
