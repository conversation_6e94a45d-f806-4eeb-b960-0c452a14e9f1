const Thenjs = require('thenjs');

const Logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const params_utils = require('../../../common/utils/params.js');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../../common/utils/rediser');
const kb_api = require('../../config.js').kb_api['v2'];
const cols_to_tree = require('../utils.js').cols_to_tree;
const get_sign_url = require('../../../common/utils/ks3.js').get_sign_url;
const _ = require('underscore');
const counter = require('../../../common/utils/counter');
/*
 * Desc:
 *   internal method, get multi videos from ids
 */
function _videos(ids, proj, callback) {
    let filter = { _id: { $in: ids } };
    let cursor = db.collection('video').find(filter, proj);
    cursor.toArray(function (err, items) {
        if (err) {
            return callback(err);
        }
        if (items.length === 0) {
            return callback(null, null);
        }
        try {
            let order_d = {};
            for (let i in ids) {
                order_d[ids[i]] = parseInt(i);
            }
            for (let i in items) {
                let item = items[i];
                item['id'] = item['_id'];
                if ('view_times' in proj && !item.view_times) {
                    item.view_times = 1;
                }
                if ('knowledges' in proj && !item.knowledges) {
                    item.knowledges = [];
                }
                delete item['_id'];
                delete item['from'];
            }
            // 按序返回
            items.sort(function (x, y) {
                return order_d[x.id] - order_d[y.id];
            });
            return callback(null, items);
        } catch (err) {
            return callback(err);
        }
    });
}

/*
 * DESC:
 * 		获取视频分类信息
 * URL: 
 * 		http://kboe.yunxiao.com/kb_api/v2/videos/categorys/	
 * Method: 
 * 		GET
 * Author: 
 * 		zhangjun
 * Date:
 * 		2016-08-23
 */
function video_cats(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    // 数据查询 ----------------------------
    Thenjs(function (cont) {
        let cache_key = params['cache_key'];
        rediser.get(cache_key, function (err, items) {
            if (err) {
                return cont(err);
            }
            if (items) {
                return cont(null, items);
            }
            let coll = db.collection('video_category');
            // 查询条件
            let cond = {};
            let keys = ['period', 'subject'];
            for (let i in keys) {
                let k = keys[i];
                if (params[k]) {
                    cond[k] = params[k];
                }
            }
            // 查询
            coll.find(cond).toArray(function (err, items) {
                if (err) {
                    return cont(err);
                }
                if (!items || items.length === 0) {
                    return cont(null, null);
                }
                items = items.filter(function (item) {
                    if (!item.acl || item.acl === 'public') {
                        return true;
                    }
                    return false;
                });
                try {
                    let keys = [
                        ['period', 'period'],
                        ['subject', 'subject'],
                        ['grade', 'grade'],
                        ['type', 'type'],
                    ];
                    items = cols_to_tree('category', items, keys, '_id');
                } catch (e) {
                    return cont(e);
                }
                rediser.set(cache_key, items, 60 * 30);
                return cont(null, items);
            });
        });
    }).then(function (cont, result) {
        if (result) {
            return responseWrapper.succ(result);
        }
        return responseWrapper.error('NULL_ERROR');
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

/*
 * DESC:
 * 		通过视频分类id获取视频列表	
 * URL: 
 * 		http://kboe.yunxiao.com/kb_api/v2/videos/categorys/{category_id}/
 * Method: 
 * 		GET
 * Author: 
 * 		zhangjun
 * Date:
 * 		2016-08-23
 */
function video_category(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    const queryDbs = new Set((req.query.dbs || 'basic').split(','));
    // 数据查询 ----------------------------
    Thenjs(function (cont) {
        // 获取category信息
        let coll = db.collection('video_category');
        let cond = { '_id': params['category_id'] };
        coll.findOne(cond, {}, function (err, item) {
            if (err) {
                return cont(err);
            }
            if (!item) {
                return cont(null, null);
            }
            // 视频专辑 权限过滤
            item = item.filter(video_c => (video_c.dbs || ['basic']).some(v => queryDbs.has(v)));
            return cont(null, item);
        });
    }).then(function (cont, item) {
        let video_ids = item['videos'].map(x => x.id);
        let proj = {
            name: 1,
            //data_url: 1,
            view_times: 1,
            from: 1,
            dbs: 1
        };
        _videos(video_ids, proj, function (err, videos) {
            if (err) {
                return cont(err);
            }
            if (!videos) {
                videos = []; // 处理视频专辑ID搜索时，该视频专辑下没有视频也返回专辑信息
                // return responseWrapper.error('NULL_ERROR');
            }
            // 视频 权限过滤
            item = item.filter(video_c => (video_c.dbs || ['basic']).some(v => queryDbs.has(v)));
            let limit = params['limit'];
            let offset = params['offset'];
            item['total_num'] = videos.length;
            item['view_times'] = videos.reduce(function (pre, cur) {
                return pre + cur.view_times;
            }, 0);
            item['videos'] = videos.slice(offset, offset + limit);
            return responseWrapper.succ(item);
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

/*
 * DESC: 
 *     获取多个视频专辑
 * URL:
 *     http://kb.yunxiao.com/kb_api/v2/videos/
 * METHOD:
 *     GET
 * Author:
 *     guochanghui
 * Date:
 *     2017-08-26
 */
function videos(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let ids = req.query.ids.split(',');
    try {
        ids = ids.map(function (x) {
            return parseInt(x.trim());
        });
    } catch (err) {
        return responseWrapper.error('PARAMETERS_ERROR', err.message);
    }
    let proj = {
        name: 1,
        period: 1,
        subject: 1,
        data_url: 1,
        knowledges: 1,
        view_times: 1,
        from: 1,
        file_name: 1,
        ctime: 1,
        duration: 1,
        teachers: 1,
        dbs: 1,
    };
    _videos(ids, proj, function (err, items) {
        if (err) {
            return responseWrapper.succ(items);
        }
        // 视频 权限过滤
        const queryDbs = new Set((req.query.dbs || 'basic').split(','));
        items = items.filter(video => (video.dbs || ['basic']).some(item => queryDbs.has(item)));
        return responseWrapper.succ(items);
    });
}

/*
 * DESC:
 * 		根据视频id取视频信息
 * URL: 
 * 		http://kboe.yunxiao.com/kb_api/v2/videos/{video_id}/
 * Method: 
 * 		GET
 * Author: 
 * 		zhangjun
 * Date:
 * 		2016-08-23
 */
function video(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    // 数据查询 ----------------------------
    Thenjs(function (cont) {
        let coll = db.collection('video');
        let filter = { '_id': params['video_id'] };
        let update = { $inc: { view_times: 1 } };
        let options = {
            projection: {
                source_url: 0,
                from: 0,
            },
        };
        coll.findOneAndUpdate(filter, update, options, function (err, item) {
            if (err) {
                cont(err);
            }
            cont(null, item);
        });
    }).then(function (cont, item) {
        let video = item.value;
        if (!video) {
            return responseWrapper.error('NULL_ERROR');
        }
        // 视频 权限过滤
        const queryDbs = new Set((req.query.dbs || 'basic').split(','));
        if (!(video.dbs || ['basic']).some(item => queryDbs.has(item))) {
            return responseWrapper.error('NULL_ERROR', '未找到数据');
        }

        if (video['acl'] === 'private') {
            video['data_url'] = get_sign_url(video['data_url'], 2400);
        }
        let _video = {
            id: video._id,
            name: video.name,
            description: video.description || '',
            duration: video.duration || -1,
            data_url: video.data_url,
            period: video.period,
            subject: video.subject,
            teachers: video.teachers || [],
            rate: video.rate || 8.0,
            view_times: video.view_times || 1,
            knowledges: video.knowledges || [],
            file_name: video.file_name || '',
            description_files: video.description_files || [],
            qrcode_url: video.qrcode_url || '',
        };
        return responseWrapper.succ(_video);
    }).fail(function (cont, err) {
        return responseWrapper.error('HANDLE_ERROR', err.message);
    });
}

/*
 * DESC:
 * 		根据视频id取视频信息
 * URL: 
 * 		http://kboe.yunxiao.com/kb_api/v2/knowledges/{knowledge_ids}/videos/
 * Method: 
 * 		GET
 * Author: 
 * 		zhangjun
 * Date:
 * 		2016-11-02
 */
function knowledge_videos(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    const queryDbs = new Set((req.query.dbs || 'basic').split(','));
    // 数据查询 ----------------------------
    Thenjs(function (cont) {
        let kn_ids = params['knowledge_ids'];
        // 查询知识点
        let coll = db.collection('knowledge');
        // 查询条件
        let cond = { '_id': { '$in': kn_ids } };
        // 提取出的字段
        let proj = { 'videos': 1, 'dbs': 1 };
        // 查询
        coll.find(cond).project(proj).toArray(function (err, items) {
            if (err) {
                return cont(err);
            }
            // 知识点 权限过滤
            items = items.filter(knowledge => (knowledge.dbs || ['basic']).some(item => queryDbs.has(item)));
            let d = {};
            for (let i in items) {
                let item = items[i];
                let kid = item['_id'];
                let vs = item['videos'];
                let vids = [];
                for (let j in vs) {
                    vids.push(vs[j]['id']);
                }
                d[kid] = vids;
            }
            let video_ids = [];
            for (let i in kn_ids) {
                let kid = kn_ids[i];
                let vs = d[kid];
                if (vs == null) {
                    continue;
                }
                for (let j in vs) {
                    video_ids.push(vs[j]);
                }
            }
            if (video_ids.length === 0) {
                return responseWrapper.error('NULL_ERROR');
            }
            return cont(null, video_ids);
        });
    }).then(function (cont, video_ids) {
        let proj = {
            '_id': 1,
            'name': 1,
            'data_url': 1,
            'from': 1,
            'dbs': 1,
        };
        _videos(video_ids, proj, function (err, items) {
            if (err) {
                return cont(err);
            }
            if (!items) {
                return cont(null, null);
            }
            // 视频 权限过滤
            items = items.filter(video => (video.dbs || ['basic']).some(item => queryDbs.has(item)));
            return cont(null, { 'videos': items });
        });
    }).then(function (cont, result) {
        if (result) {
            return responseWrapper.succ(result);
        }
        return responseWrapper.error('NULL_ERROR');
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}
/**
 * 查询视频专辑列表数据
*/
function get_video_categories(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    Thenjs(function (cont) {
        let cond = {
        };
        if (params.period && params.period !== 'all') {
            cond.period = params.period;
        }
        if (params.subject && params.subject !== 'all') {
            cond.subject = params.subject;
        }

        // 关键字搜索请求，匹配字段：
        // 专辑标题type 专辑描述description 包含的视频name 讲师name
        if (req.query.keywords) {
            let queryPattern = new RegExp(req.query.keywords, 'i');
            cond['$or'] = [
                { type: queryPattern },
                { 'teachers.name': queryPattern },
                { description: queryPattern },
                { 'videos.name': queryPattern }
            ];
        }
        //视频专辑按标签筛选
        let tagsand = [];
        if (req.query.tags) {
            let tags = JSON.parse(req.query.tags);
            for (let tag of tags) {
                let tagand = [];
                for (let val of tag.values) {
                    tagand.push(val.name);
                }
                let tagscond = {
                    tags: {
                        $elemMatch: {
                            id: tag.id,
                            'values.name': {
                                $in: tagand
                            }
                        }
                    }
                };
                tagsand.push(tagscond);
            }
        }
        if (tagsand.length !== 0) {
            cond['$and'] = tagsand;
        }
        console.log(JSON.stringify(cond));
        let options = {};
        let retrun_data = {
            total_num: 0,
            categories: []
        };
        db.collection('video_category').count(cond, (err, result_count) => {
            if (err) {
                Logger.error('get_video_categorys: count :' + err);
                return responseWrapper.error('HANDLE_ERROR');
            }
            if (isNaN(result_count)) {
                Logger.error('get_video_categorys: count return result not Number');
                return responseWrapper.error('HANDLE_ERROR');
            }
            retrun_data.total_num = result_count;
            db.collection('video_category').find(cond).sort({ ctime: -1 }).limit(params.limit).skip(params.offset).toArray((err, r_array_data) => {
                if (err) {
                    return cont(err);
                }
                if (r_array_data == null || !(r_array_data instanceof Array) || r_array_data.length == 0) {
                    return responseWrapper.succ(retrun_data);
                }
                try {
                    for (let i in r_array_data) {
                        let category = r_array_data[i];
                        retrun_data.categories.push({
                            id: category._id,
                            period: category.period,                             // 学段
                            subject: category.subject,                            // 学科
                            type: category.type,                               // 专辑名称
                            description: category.description,                        // 视频专辑简介
                            teachers: category.teachers ? category.teachers : [],
                            profile_url: category.profile_url,                        // 专辑图片
                            author_name: category.author_name || '',                    //创建者名称
                            tags: category.tags || [],
                        })
                    }
                } catch (err) {
                    return cont(err);
                }
                let returnobj = retrun_data;
                return responseWrapper.succ(returnobj);
            });
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}
/**
 * 查询视频专辑信息
*/
function get_video_category(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    Thenjs(function (cont) {
        let cond = { _id: params.category_id, };
        let options = { sort: { _id: -1 } };
        let retrun_data = { category: {} };
        db.collection('video_category').findOne(cond, options, function (err, result_data) {
            if (err) {
                return cont(err);
            }
            if (!(result_data instanceof Object)) {
                return cont('get_video_category :findOne : 没有查询到数据。');
            }
            const queryDbs = new Set((req.query.dbs || 'basic').split(','));
            if (!(result_data.dbs || ['basic']).some(item => queryDbs.has(item))) {
                return responseWrapper.error('NULL_ERROR', '未查询到数据');
            }

            try {
                let category = result_data;
                retrun_data.category = {
                    period: category.period,                             // 学段
                    subject: category.subject,                            // 学科
                    type: category.type,                               // 专辑名称
                    description: category.description,                        // 视频专辑简介
                    teachers: category.teachers,
                    profile_url: category.profile_url,                        // 专辑图片
                    from: category.from,                               // 来源
                    acl: category.acl,                          // 访问控制，public:公有；private：私有；
                    author_name: category.author_name || '',     //创建者名称
                    videos: category.videos,
                    tags: category.tags || []
                };
                cont(null, retrun_data);
            } catch (err) {
                return cont(err);
            }
        });
    }).then(function (cont, video_category_wrap) {
        let videos = video_category_wrap.category.videos || [];
        let ids = videos.map(v => v.id);
        let pro = { data_url: 1, period: 1, subject: 1, duration: 1, teachers: 1, ctime: 1, file_name: 1, name: 1 };
        _videos(ids, pro, function (err, videos) {
            try {
                if (err || !videos) {
                    videos = [];
                }
                let video_buf = {};
                for (let v of videos) {
                    video_buf[v.id] = v;
                }
                let ret_video = [];
                for (let item of videos) {
                    let v = video_buf[item.id];
                    ret_video.push(v);
                }
                video_category_wrap.category.videos = ret_video;
                return responseWrapper.succ(video_category_wrap);
            } catch (err) {
                return cont(err);
            }
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}

/**
 * 查询视频专辑视频列表
*/
function get_video_category_videos(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }
    Thenjs(function (cont) {
        let cond = { _id: params.category_id, };
        let options = { sort: { ctime: -1 } };
        db.collection('video_category').findOne(cond, options, function (err, result_data) {
            if (err) {
                return cont(err);
            }
            if (!(result_data instanceof Object)) {
                return cont('get_video_category_videos   : video_category findOne : 没有查询到数据。');
            }
            try {
                let category = result_data;
                cont(null, category);
            } catch (err) {
                return cont(err);
            }
        });
    }).then(function (cont, result_category) {
        if (!result_category || !result_category.videos) {
            return cont('get_video_category_videos ： result_category:' + result_category);
        }
        //返回数据
        let retrun_data = {
            total_num: 0,
            videos: []
        };
        if (result_category.videos.length === 0) {
            return responseWrapper.succ(retrun_data);
        }
        //处理查询内容
        let videos = [];
        for (let i in result_category.videos) {
            if (i >= params.offset && i < (params.offset + params.limit)) {
                videos.push(result_category.videos[i]);
            }
        }
        let cond = {
            _id: { $in: _.map(videos, 'id'), }
        };
        retrun_data.total_num = result_category.videos.length;
        db.collection('video').find(cond).toArray((err, r_array_data) => {
            if (err) {
                return cont(err);
            }
            if (r_array_data == null || !(r_array_data instanceof Array) || r_array_data.length == 0) {
                return responseWrapper.succ(retrun_data);
            }
            try {
                for (let j in videos) {
                    for (let i in r_array_data) {
                        let video = r_array_data[i];
                        if (videos[j]['id'] === video['_id']) {
                            if (video['acl'] === 'private') {
                                video['data_url'] = get_sign_url(video['data_url'], 3600 * 12);
                            }
                            retrun_data.videos.push({
                                id: video._id, // 视频id
                                name: video.name, // 视频名称   
                                data_url: video.data_url, // 视频地址
                                period: video.period, //学段
                                subject: video.subject, //学科
                                duration: video.duration, // 视频时长；
                                teachers: video.teachers, // 老师的姓名；
                                c_time: video.ctime, //创建时间                      // 专辑图片
                                file_name: video.file_name || ''
                            })
                        }
                    }
                }
                return responseWrapper.succ(retrun_data);
            } catch (err) {
                return cont(err);
            }
        });
    }).fail((cont, error) => {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally((cont, error) => {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}
/**
 * 查询视频信息
*/
function get_video_category_video(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }
    Thenjs(function (cont) {
        let cond = { _id: params.video_id, };
        let options = { sort: { _id: -1 } };
        let retrun_data = {};
        db.collection('video').findOne(cond, options, function (err, result_data) {
            if (err) {
                return cont(err);
            }
            if (!(result_data instanceof Object)) {
                return cont('get_video_category_video :findOne : 没有查询到数据。');
            }
            try {
                let video = result_data;
                retrun_data = {
                    id: video._id,//  视频
                    name: video.name,           // 视频名称
                    data_url: video.data_url,       // 视频地址
                    period: video.period,         // 学段
                    subject: video.subject,        // 学科
                    description: video.description,     // 视频介绍，没有为空字符串；
                    duration: video.duration || -1,           // 视频时长,
                    teachers: video.teachers || [],
                    knowledges: video.knowledges || [],
                    recorder: video.recorder || '',//录制人
                    label: video.label || [],// 视频标签
                    type: video.type || '',//视频类型 同步，专题
                    from: video.from || '',//视频来源    
                    file_name: video.file_name || '', //视频名称  
                    tags: video.tags || [],           //视频标签  
                    description_files: video.description_files || [], // 视频文件描述    
                    qrcode_url: video.qrcode_url || ''
                };
                return responseWrapper.succ(retrun_data);
            } catch (err) {
                return cont(err);
            }
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}
/**
 * 添加视频专辑
*/
function add_video_category(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    let doc = params['category'];
    Thenjs(function (cont) {
        if (doc.id) {
            let _id = Number(doc.id);
            if (_id) {
                delete doc.id;
                return cont(null, [_id]);
            }
        }
        counter.getNextSequenceValue('video_category', 1, function (err, seq) {
            if (err) {
                return cont(err);
            }
            cont(null, seq);
        });
    }).then(function (cont, seq) {
        doc['_id'] = seq[0];
        doc['ctime'] = new Date();
        doc['grade'] = '';
        doc['advantage'] = '';
        doc['rate'] = 8;

        // 插入，how do
        db.collection('video_category').insertOne(doc, function (err, result) {
            if (err) {
                Logger.error('add_video_category: insertOne :' + err);
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }
            if (!result || !(result instanceof Object) || result.insertedCount != 1 || !((result.ops instanceof Array) && result.ops.length > 0)) {
                Logger.error('add_video_category:' + result);
                return responseWrapper.error('HANDLE_ERROR');
            }
            return responseWrapper.succ({ id: seq[0] });
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}

/**
 * 编辑视频专辑
*/
function put_video_category(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    Thenjs(function (cont) {
        //更新数据
        let filter = { '_id': params['category_id'] };
        let fields = params['category'];
        fields['utime'] = new Date();
        let update = { $set: fields, };
        let options = {};
        db.collection('video_category').updateOne(filter, update, options, function (err, result) {
            if (err) {
                Logger.error('put_video_category: updateOne :' + err);
                return responseWrapper.error('HANDLE_ERROR', err);
            }
            if (!result || !result.result || result.result.ok != 1) {
                Logger.error('put_video_category:' + result);
                return responseWrapper.error('HANDLE_ERROR', result);
            }
            return responseWrapper.succ({});
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}
/**
 * 添加视频专辑视频
*/
function add_video_category_videos(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    Thenjs(function (cont) {
        counter.getNextSequenceValue('video', 1, function (err, seq) {
            if (err) {
                return cont(err);
            }
            let doc = {
                name: params['name'],
                period: params['period'],
                subject: params['subject'],
                description: params['description'],     // 视频介绍，没有为空字符串；
                duration: params['duration'] || -1,           // 视频时长； -1表示不存在
                data_url: params['data_url'],
                source_url: params['source_url'] || '',
                rate: params['rate'] || 8,             // 视频评分
                view_times: params['view_times'] || 0,         // 视频观看次数
                label: params['label'] || [],               // 视频标签
                acl: params['acl'] || 'private',             // 访问控制，public:为公开读；private：为受限访问；
                recorder: params['recorder'] || '',        // 录制人
                type: params['type'],            // 同步或专题
                from: params['from'],
                teachers: params['teachers'] || [],
                knowledges: params['knowledges'] || [],
                file_name: params['file_name'] || '',
                description_files: params['description_files'] || [],
            };
            doc['_id'] = seq[0];
            doc['ctime'] = new Date();
            // 插入，how do
            db.collection('video').insertOne(doc, function (err, result) {
                if (err) {
                    Logger.error('add_video_category_videos: insertOne :' + err);
                    return responseWrapper.error('HANDLE_ERROR');
                }
                if (!result || !(result instanceof Object) || result.insertedCount != 1 || !((result.ops instanceof Array) && result.ops.length > 0)) {
                    Logger.error('add_video_category_videos:' + result);
                    return responseWrapper.error('HANDLE_ERROR');
                }
                //更新数据
                let filter = { '_id': params['category_id'] };
                let fields = {};
                fields['utime'] = new Date();
                let update = {
                    $set: fields,
                    $push: {
                        videos: {
                            id: doc['_id'],
                            name: doc['name']
                        }
                    }
                };
                let options = {};
                db.collection('video_category').updateOne(filter, update, options, function (err, result) {
                    if (err) {
                        Logger.error('add_video_category_videos: video_category  updateOne :' + err);
                        return responseWrapper.error('HANDLE_ERROR', err);
                    }
                    if (!result || !result.result || result.result.ok !== 1) {
                        Logger.error('add_video_category_videos: video_category upondeOne' + result);
                        return responseWrapper.error('HANDLE_ERROR', result);
                    }
                    return responseWrapper.succ({ id: seq[0] });
                });
            });
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}

function syncKnowledgesAndVideos(videoId, newKnowledges, cb) {
    let oldKnowIds = [], newKnowIds = _.map(newKnowledges, o => o.id), currVideo = {};
    Thenjs(cont => {
        db.collection('video').findOne({ _id: videoId }, { knowledges: 1, name: 1, from: 1 }, (err, video) => {
            if (err || !video) return cb(err, null);
            currVideo = video;
            oldKnowIds = _.map(video.knowledges || [], o => o.id);
            let knows = newKnowledges.concat(video.knowledges || []);
            cont(null, _.uniq(knows.map(o => o.id)));
        });
    }).then((cont, knowIds) => {
        db.collection('knowledge').find({ _id: { $in: knowIds } }, { videos: 1 }).toArray((err, knowledges) => {
            if (err) return cb(err, null);
            let needs = [];
            knowledges.forEach(know => {
                know.videos = know.videos || [];
                if (!newKnowIds.includes(know._id) && oldKnowIds.includes(know._id)) {// 移除该视频id
                    let videos = [];
                    know.videos.forEach(v => { if (v.id !== videoId) return videos.push(v); });
                    know.videos = videos;
                    needs.push(know);
                } else if (newKnowIds.includes(know._id) && !oldKnowIds.includes(know._id)) {// 新增该视频id
                    if (!_.map(know.videos, o => o.id).includes(videoId)) {
                        currVideo.id = videoId;
                        delete currVideo._id;
                        delete currVideo.knowledges;
                        know.videos.push(currVideo);
                        needs.push(know);
                    }
                } else if (newKnowIds.includes(know._id) && oldKnowIds.includes(know._id)) {// 同步老数据
                    if (!_.map(know.videos, o => o.id).includes(videoId)) {
                        currVideo.id = videoId;
                        delete currVideo._id;
                        delete currVideo.knowledges;
                        know.videos.push(currVideo);
                        needs.push(know);
                    }
                }
            });
            if (_.isEmpty(needs)) return cb(null, null);
            cont(err, needs);
        });
    }).eachLimit(null, (cont, knowledge) => {
        db.collection('knowledge').update({ _id: knowledge._id }, { $set: { videos: knowledge.videos } }, (err, ret) => {
            if (err) return cb(err, ret);
            cont(err, knowledge._id);
        });
    }, 10).fin((cont, err, ret) => {
        cb(err, ret);
    });
}

/**
 * 编辑视频
*/
function put_video(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    Thenjs(function (cont) {
        syncKnowledgesAndVideos(params['video_id'], params['knowledges'] || [], (err, ret) => {
            if (err) {
                Logger.error(`syncKnowledgesAndVideos is error: ${err}`);
                return responseWrapper.error('HANDLE_ERROR', err);
            }
            cont(err, null);
        });
    }).then((cont, arg) => {
        //更新数据
        let filter = { '_id': params['video_id'] };
        let fields = params;
        fields['utime'] = new Date();
        let update = { $set: fields, };
        let options = {};
        db.collection('video').updateOne(filter, update, options, function (err, result) {
            if (err) {
                Logger.error('put_video: updateOne :' + err);
                return responseWrapper.error('HANDLE_ERROR', err);
            }
            if (!result || !result.result || result.result.ok != 1) {
                Logger.error('put_video: updateOne :' + result);
                return responseWrapper.error('HANDLE_ERROR', result);
            }
            return responseWrapper.succ({});
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}

/**
 * 视频专辑移除视频
*/
function delete_video_category_video(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    Thenjs(function (cont) {
        //更新数据
        let filter = { '_id': params['category_id'] };
        let fields = {};
        fields['utime'] = new Date();
        let update = {
            $set: fields,
            $pull: {
                videos: {
                    id: params['video_id'],
                }
            }
        }
        let options = {};
        db.collection('video_category').updateOne(filter, update, options, function (err, result) {
            if (err) {
                Logger.error('delete_video_category_video: video_category  updateOne :' + err);
                return responseWrapper.error('HANDLE_ERROR', err);
            }
            if (!result || !result.result || result.result.ok != 1) {
                Logger.error('delete_video_category_video: video_category updateOne' + result);
                return responseWrapper.error('HANDLE_ERROR', result);
            }
            return responseWrapper.succ({});
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}
/**
 * 添加视频专辑视频
*/
function put_video_category_existing_video(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    Thenjs(function (cont) {
        let cond = { _id: params.video_id, };
        let options = { sort: { _id: -1 } };
        db.collection('video').findOne(cond, options, function (err, result_data) {
            if (err) {
                return cont(err);
            }
            if (!(result_data instanceof Object)) {
                return cont('put_video_category_existing_video :findOne : 没有查询到数据。');
            }
            //更新数据
            let filter = { '_id': params['category_id'] };
            let fields = {};
            fields['utime'] = new Date();
            let update = {
                $set: fields,
                $addToSet: {
                    videos: {
                        id: result_data._id,
                        name: result_data.name,
                    }
                }
            };
            let options = {};
            db.collection('video_category').updateOne(filter, update, options, function (err, result) {
                if (err) {
                    Logger.error('put_video_category_existing_video: video_category  updateOne :' + err);
                    return responseWrapper.error('HANDLE_ERROR', err);
                }
                if (!result || !result.result || result.result.ok != 1) {
                    Logger.error("put_video_category_existing_video: video_category upondeOne" + result);
                    return responseWrapper.error('HANDLE_ERROR', result);
                }
                return responseWrapper.succ({});
            });
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}
/**
 * 查询视频专辑列表数据
*/
function get_video_list(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    Thenjs(function (cont) {
        let cond = {};
        if (params.period && params.period !== 'all') {
            cond.period = params.period;
        }
        if (params.subject && params.subject !== 'all') {
            cond.subject = params.subject;
        }
        if (params.teacher && params.teacher !== '') {
            cond['teachers.name'] = { '$regex': new RegExp(params.teacher), };
        }
        if (params.name && params.name !== '') {
            cond.name = { '$regex': new RegExp(params.name), };
        }
        let retrun_data = {
            total_num: 0,
            videos: []
        };
        db.collection('video').count(cond, { sort: { ctime: -1 } }, (err, result_count) => {
            if (err) {
                Logger.error('get_video_list: count :' + err);
                return responseWrapper.error('HANDLE_ERROR');
            }
            if (isNaN(result_count)) {
                Logger.error('get_video_list: count return result not Number');
                return responseWrapper.error('HANDLE_ERROR');
            }
            retrun_data.total_num = result_count;
            db.collection('video').find(cond).sort({ ctime: -1 }).limit(params.limit).skip(params.offset).toArray((err, r_array_data) => {
                if (err) {
                    return cont(err);
                }
                if (r_array_data == null || !(r_array_data instanceof Array) || r_array_data.length === 0) {
                    return responseWrapper.succ(retrun_data);
                }
                try {
                    for (let i in r_array_data) {
                        let video = r_array_data[i];
                        retrun_data.videos.push({
                            id: video._id, // 视频id
                            name: video.name, // 视频名称   
                            data_url: video.data_url, // 视频地址
                            period: video.period, //学段
                            subject: video.subject, //学科
                            duration: video.duration, // 视频时长；
                            teachers: video.teachers, // 老师的姓名；
                            c_time: video.ctime, //创建时间                      // 专辑图片
                            file_name: video.file_name || ''
                        })
                    }
                } catch (err) {
                    return cont(err);
                }
                return responseWrapper.succ(retrun_data);
            });
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}
/**
 * 添加视频专辑视频
*/
function add_video(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    Thenjs(function (cont) {
        counter.getNextSequenceValue('video', 1, function (err, seq) {
            if (err) {
                return cont(err);
            }
            let doc = {
                name: params['name'],
                period: params['period'],
                subject: params['subject'],
                description: params['description'] || '',     // 视频介绍，没有为空字符串；
                duration: params['duration'] || -1,           // 视频时长； -1表示不存在
                data_url: params['data_url'],
                source_url: params['source_url'] || '',
                rate: params['rate'] || 8,             // 视频评分
                view_times: params['view_times'] || 0,         // 视频观看次数
                label: params['label'] || [],               // 视频标签
                acl: params['acl'] || 'private',             // 访问控制，public:为公开读；private：为受限访问；
                recorder: params['recorder'] || '',        // 录制人
                type: params['type'] || '',            // 同步或专题
                from: params['from'] || '',
                teachers: params['teachers'] || [],
                knowledges: params['knowledges'] || [],
                file_name: params['file_name'] || '',
                tags: params['tags'] || [],
                description_files: params.description_files || [],
            };
            doc['_id'] = seq[0];
            doc['ctime'] = new Date();
            // 插入，how do
            db.collection('video').insertOne(doc, function (err, result) {
                if (err) {
                    Logger.error('add_video: insertOne :' + err);
                    return responseWrapper.error('HANDLE_ERROR');
                }
                if (!result || !(result instanceof Object) || result.insertedCount != 1 || !((result.ops instanceof Array) && result.ops.length > 0)) {
                    Logger.error('add_video:' + result);
                    return responseWrapper.error('HANDLE_ERROR');
                }
                return cont(null, seq[0]);
            });
        });
    }).then((cont, videoId) => {
        syncKnowledgesAndVideos(videoId, params['knowledges'] || [], (err, ret) => {
            if (err) {
                Logger.error(`syncKnowledgesAndVideos is error: ${err}`);
                return responseWrapper.error('HANDLE_ERROR');
            }
            return responseWrapper.succ({ id: videoId });
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}

/**
 * 移除视频专辑
 */
function delete_video_category(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }

    Thenjs(function (cont) {
        let cond = { _id: params.category_id, };
        db.collection('video_category').remove(cond, function (err, result_data) {
            if (err) {
                return cont(err);
            }
            responseWrapper.succ({});
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    });
}
//根据id获取视频专辑详情
async function getcategories(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let id = parseInt(req.params.video_id);
        let cond = {
            'videos.id': id
        };
        let result = [];
        let data = await db.collection('video_category').findOne(cond);
        if (data) {
            data.id = data._id;
            delete data._id;
            result.push(data);
        }
        return responseWrapper.succ(result);
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }

}

//根据id获取视频专辑详情
function cryptoUrl(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let url = req.query.url;
        let result = get_sign_url(url, 3600 * 12);
        return responseWrapper.succ(result);
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }

}

module.exports = {
    video_cats: video_cats,
    video_category: video_category,
    videos: videos,
    video: video,
    knowledge_videos: knowledge_videos,
    get_video_categories: get_video_categories,
    get_video_category: get_video_category,
    get_video_category_videos: get_video_category_videos,
    get_video_category_video: get_video_category_video,
    add_video_category: add_video_category,
    put_video_category: put_video_category,
    add_video_category_videos: add_video_category_videos,
    put_video: put_video,
    delete_video_category_video: delete_video_category_video,
    put_video_category_existing_video: put_video_category_existing_video,
    get_video_list: get_video_list,
    add_video: add_video,
    delete_video_category: delete_video_category,
    getcategories,
    cryptoUrl,
};
