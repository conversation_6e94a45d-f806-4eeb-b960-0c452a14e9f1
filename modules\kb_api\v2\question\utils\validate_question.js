const _ = require('lodash');

/**
 *
 * 验证并补充blocks各字段个数，以及过滤
 */


const _setAnswerByType = (type) => {
    if (['填空题', '多选题'].includes(type)) {
        return [];
    }

    return '';
};

const _setStemByType = (type) => {
    const stemObject = {
        stem: ''
    };
    if (['填空题', '解答题'].includes(type)) {
        return stemObject;
    }
    stemObject['options'] = {};
    return stemObject;
};

const _repairAnswersOrStems = (types, data, field) => {
    if (!data) {
        data = [];
    }
    if (types.length === data.length) {
        return data;
    }

    while (data.length < types.length) {
        let basicData = null;
        const type = types[data.length];
        if (field === 'answer') {
            basicData = _setAnswerByType(type);
        } else {
            basicData = _setStemByType(type);
        }
        data.push(basicData);
    }

    return data;
};

const _repairBasic = (data, maxLength, str = '') => {
    if (!data) {
        data = [];
    }
    if (data.length === maxLength) {
        return data;
    }

    while (data.length < maxLength) {
        data.push(str);
    }

    return data;
};

const _repairData = (blocks, maxLength) => {
    // 补充types
    const types = _repairBasic(blocks.types, maxLength, '选择题');
    blocks.types = types;
    // 补充stems
    blocks.stems = _repairAnswersOrStems(types, blocks.stems, 'stem');
    // 补充 answers
    blocks.answers = _repairAnswersOrStems(types, blocks.answers, 'answer');
    // 补充 explanations
    blocks.explanations = _repairBasic(blocks.explanations, maxLength);
    // 补充 solutions
    blocks.solutions = _repairBasic(blocks.solutions, maxLength);
    // 补充 knowledges
    blocks.knowledges = _repairBasic(blocks.knowledges, maxLength, []);
    // 补充 core_knowledges
    blocks.core_knowledges = _repairBasic(blocks.core_knowledges, maxLength, []);
    blocks.elements = _repairBasic(blocks.elements, maxLength, []);
    return blocks;
};

const validateQuestion = (blocks) => {
    if (typeof blocks !== 'object') {
        return {};
    }
    let max = Array.isArray(blocks.types) ? blocks.types.length : 0;
    let hasAbnormalData = false; // 记录是否有异常的数据
    const fields = ['stems', 'answers', 'types', 'explanations', 'solutions', 'knowledges', 'core_knowledges'];
    for (const key of fields) {
        const element = Array.isArray(blocks[key]) ? blocks[key] : [];
        if (element.length !== max) {
            hasAbnormalData = true;
        }
        max = max > element.length ? max : element.length;
    }

    if (!hasAbnormalData) {
        return blocks;
    }

    return _repairData(blocks, max);
};

function mergeArr(oldArr, newArr) { // 此函数只用于合并符合[{id: '', name}]结构的数组（知识点，对象，细分， 叠加要素等）
    if (!Array.isArray(oldArr) || oldArr.length === 0) {
        return Array.isArray(newArr) ? newArr : [];
    }
    if (!Array.isArray(newArr) || newArr.length === 0) {
        return Array.isArray(oldArr) ? oldArr : [];
    }
    let currentArr = _.cloneDeep(oldArr);
    newArr.forEach(item => {
        let index = currentArr.findIndex(current => item.id === current.id);
        if (index === -1) {
            currentArr.push(item);
        }
    });
    return currentArr;
}
/* 集合所有小题知识点 */
function flattenKnowledges(arr) {
    let currentArr = [];
    arr.forEach(_arr => {
        _arr.forEach(know => {
            let index = currentArr.findIndex(item => item.id === know.id); // 当前知识点在新数组中的位置
            if (index === -1) { // 当前知识点在新数组中不存在，则push
                let cloneKnow = _.cloneDeep(know);
                currentArr.push(cloneKnow);
            } else { // 当前知识点在新数组中存在，则合并
                currentArr[index].know_methods = mergeArr(currentArr[index].know_methods, know.know_methods);
                currentArr[index].targets = mergeArr(currentArr[index].targets, know.targets);
            }
        });
    });
    return currentArr;
}
/* 集合所有小题叠加要素 */
function flattenElements(arr) {
    let currentArr = [];
    if (Array.isArray(arr)) {
        arr.forEach(_arr => {
            if (Array.isArray(_arr)) {
                _arr.forEach(element => {
                    let index = currentArr.findIndex(item => item.id === element.id); // 当前要素在新数组中的位置
                    if (index === -1) { // 当前要素在新数组中不存在，则push
                        currentArr.push(element);
                    }
                });
            }
        });
    }
    return currentArr;
}
module.exports = {
    validateQuestion,
    flattenKnowledges,
    flattenElements
};
