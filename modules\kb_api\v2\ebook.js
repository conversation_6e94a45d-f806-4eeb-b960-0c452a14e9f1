var Thenjs = require('thenjs');

var Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
var params_utils = require('../../common/utils/params.js');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
var kb_api = require('../config.js').kb_api['v2'];
var loosen_tree = require('./utils.js').loosen_tree;

/*
 * DESC:
 * 		请求某学科下图书列表	
 * URL: 
 * 		http://kboe.yunxiao.com/kb_api/v2/ebooks/
 * Method: 
 * 		GET
 * Author: 
 * 		zhangjun
 * Date:
 * 		2016-08-23
 */
function ebooks(req, res){
    var responseWrapper = new ResponseWrapper(res);
    var params = null;
    try {
        var fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    // 数据查询 ----------------------------
    Thenjs(function(cont) {
        var coll = db.collection('ebook');
        // 查询条件
        var cond = {
            access_control: {$ne: -1},
        };
        if (params['subject']) {
            cond['subject'] = params['subject'];
        }
        // 提取出的字段
        var proj = {
            '_id': 1,
            'name': 1,
            'rate': 1,
            'subject': 1,
            'profile_url': 1,
            'download_times': 1,
            'view_times': 1,
            'host': 1,
        };
        var limit = params['limit'];
        var offset = params['offset'];
        // 查询总数量
        coll.count(cond).then(function(total_num){
            coll.find(cond).project(proj).toArray(function(err, items) {
                if (err) {
                    return cont(err);
                }
                var r = null;
                try {
                    var ebooks = items.slice(offset, offset+limit);
                    for (var i in ebooks) {
                        var ebook = ebooks[i];
                        var host = ebook['host'];
                        delete ebook['host'];
                        ebook['id'] = ebook['_id'];
                        delete ebook['_id'];
                        ebook['profile_url'] = host + ebook['profile_url'];
                    }
                    r = {
                        'total_num': total_num,
                        'ebooks': ebooks,
                    };
                } catch(e) {
                    return cont(e);
                }
                return cont(null, r);
            });
        });
    }).then(function(cont, result) {
        if (result) {
            return  responseWrapper.succ(result);
        } else {
            return responseWrapper.error('NULL_ERROR');
        }
    }).fail(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

/*
 * DESC:
 * 		根据图书id号获取图书数据资源
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/ebooks/{ebook_id}/
 * Method:
 * 		GET
 * Author:
 * 		zhangjun
 * Date:
 * 		2016-08-23
 */
function ebook(req, res){
    var responseWrapper = new ResponseWrapper(res);
    var params = null;
    try {
        var fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    // 数据查询 ----------------------------
    Thenjs(function(cont) {
        // 更新view_times
        var coll = db.collection('ebook');
        // 查询条件
        var cond = {
            '_id':params['ebook_id'],
            access_control: {$ne: -1},
        };
        // 字段更新
        var update_fields = {
            '$inc' : {'view_times': 1},
        };
        // 查询
        coll.updateOne(cond, update_fields, function(err, result) {
            if (err || result.result.n != 1) {
                return cont(err);
            }
            return cont(null, null);
        });
    }).then(function(cont, result) {
        var coll = db.collection('ebook');
        // 查询条件
        var cond = {
            "_id": params["ebook_id"],
            access_control: {$ne: -1},
        };
        // 提取出的字段
        var proj = params['fields_type'];
        // 查询
        coll.find(cond).project(proj).toArray(function(err, items) {
            if (err) {
                return cont(err);
            }
            if (items.length > 0) {
                var ebook = items[0];
                ebook['id'] = ebook['_id'];
                delete ebook['_id'];
                var host = ebook['host'];
                delete ebook['host'];
                ebook['profile_url'] = host + ebook['profile_url'];
                ebook['book_url'] = host + ebook['book_url'];
                var limit = params['limit'];
                var offset = params['offset'];
                if (ebook['pages_url'] != null && ebook['pages_url'].length>0) {
                    var pages = ebook['pages_url'].slice(offset, offset+limit);
                    for (var i in pages) {
                        pages[i] = host + pages[i];
                    }
                    ebook['pages_url'] = pages;
                }
                var keys = {
                    'catalogs': 'catalogs',
                };
                var cats = {'catalogs': ebook['catalogs']};
                loosen_tree(cats, keys);
                ebook['catalogs'] = cats;
                return cont(null, ebook);
            } else {
                return responseWrapper.error('NULL_ERROR');
            }
        });
    }).then(function(cont, result) {
        if (result) {
            return  responseWrapper.succ(result);
        } else {
            return responseWrapper.error('NULL_ERROR');
        }
    }).fail(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

/*
 * DESC:
 * 		根据图书id号下载图书资源
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/ebooks/{ebook_id}/download/
 * Method:
 * 		GET
 * Author:
 * 		zhangjun
 * Date:
 * 		2016-08-23
 */
function ebook_download(req, res){
    var responseWrapper = new ResponseWrapper(res);
    var params = null;
    try {
        var fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    // 数据查询 ----------------------------
    Thenjs(function(cont) {
        // 查询试卷
        var coll = db.collection('ebook');
        // 查询条件
        var cond = {
            '_id':params['ebook_id'],
            access_control: {$ne: -1},
        };
        // 字段更新
        var update_fields = {
            '$inc' : {'download_times': 1},
        };
        // 查询
        coll.updateOne(cond, update_fields, function(err, result) {
            if (err) {
                return cont(err);
            }
            if (result.result.n != 1) {
                return cont('更新试题错误');
            }
            return  responseWrapper.succ({'code':0, 'msg':'ok'});
        });
    }).fail(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

/*
 * DESC:
 * 		获取热门书单
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/ebooks/popularity/
 * Method:
 * 		GET
 * Author:
 * 		zhangjun
 * Date:
 * 		2016-08-23
 */
function popularity(req, res){
    var responseWrapper = new ResponseWrapper(res);
    var params = null;
    try {
        var fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    // 数据查询 ----------------------------
    Thenjs(function(cont) {
        var coll = db.collection('ebook');
        // 查询条件
        var cond = {
            access_control: {$ne: -1},
        };
        if (params['subject']) {
            cond['subject'] = params['subject'];
        }
        // 提取出的字段
        var proj = {
            '_id': 1,
            'name': 1,
            'rate': 1,
            'author': 1,
            'subject': 1,
            'profile_url': 1,
            'download_times': 1,
            'view_times': 1,
            'host': 1,
        };
        var limit = params['limit'];
        var sort_by = params['sort_by'];
        // 查询总数量
        coll.find(cond).project(proj).sort(sort_by).limit(limit).project(proj).toArray(function(err, items) {
            if (err) {
                return cont(err);
            }
            if (items.length > 0) {
                var ebooks = items;
                for (var i in ebooks) {
                    var ebook = ebooks[i];
                    ebook['id'] = ebook['_id'];
                    delete ebook['_id'];
                    var host = ebook['host'];
                    delete ebook['host'];
                    ebook['profile_url'] = host + ebook['profile_url'];
                }
                return cont(null, ebooks);
            } else {
                return responseWrapper.error('NULL_ERROR');
            }
        });
    }).then(function(cont, result) {
        if (result.length > 0) {
            return  responseWrapper.succ(result);
        } else {
            return responseWrapper.error('NULL_ERROR');
        }
    }).fail(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

module.exports = {
    ebooks: ebooks,
    ebook: ebook,
    ebook_download: ebook_download,
    popularity: popularity,
}
