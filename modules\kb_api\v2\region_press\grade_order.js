const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const { ObjectID } = require('mongodb');
const db = mongodber.use('KB');
const grade_order = db.collection('grade_order');
const region_press_version = db.collection('region_press_version');

//教材顺序列表
const getGradeOrderList = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let city = req.query.city;
    let period = req.query.period;
    let province = req.query.province;
    if (!city || !period || !province) {
        return resWrapper.error('PARAMETERS_ERROR', '参数不能为空');
    }
    let cond = {
        province: province, city: city, country: 'all'
    };
    try {
        let subjects;
        let regionResult = await region_press_version.findOne(cond);
        if (!regionResult) {
            return resWrapper.succ({});
        }
        regionResult.periods.forEach(value => {
            if (value && value.name === period) {
                subjects = value.grades[0].subjects;
            }
        });

        let data = {
            region_press_id: regionResult._id, period: period
        };
        let list = await grade_order.find(data).toArray();
        list.forEach((value) => {
            if (value) {
                value.id = value._id;
                delete value._id;
                subjects.forEach(element => {
                    if (element && value.subject === element.name) {
                        if (!element.press_version || value.press_version !== element.press_version) {
                            value.press_version = element.press_version;
                        }
                    }
                });
            }
        });
        return resWrapper.succ({ list: list });
    } catch (error) {
        return resWrapper.error('HANDLE_ERROR', error.message);
    }
};

// 获取教材顺序信息
const getGradeOrderById = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let id = req.params.grade_order_id.toString();
    if (!id) {
        return resWrapper.error('PARAMETERS_ERROR', '参数不能为空');
    }
    let cond = { _id: ObjectID(id) };
    try {
        let subjects;
        let result = await grade_order.findOne(cond);
        if (!result) {
            return resWrapper.succ({});
        }
        result.id = result._id;
        delete result._id;
        let regionResult = await region_press_version.findOne({ _id: result.region_press_id });
        regionResult.periods.forEach(value => {
            if (value && value.name === result.period) {
                subjects = value.grades[0].subjects;
            }
        });
        subjects.forEach(value => {
            if (value && result.subject === value.name) {
                if (!value.press_version || result.press_version !== value.press_version) {
                    result.press_version = value.press_version;
                }
            }
        });
        return resWrapper.succ(result);
    } catch (error) {
        return resWrapper.error('HANDLE_ERROR', error.message);
    }
};

// 通过id获取省市
const getRegionPressVersionById = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let id = Number(req.params.id);
    if (!id) {
        return resWrapper.error('PARAMETERS_ERROR', '参数不能为空');
    }
    let cond = { _id: id };
    try {
        let result = await region_press_version.findOne(cond);
        if (result) {
            result.id = result._id;
            delete result._id;
        }
        return resWrapper.succ(result);
    } catch (error) {
        return resWrapper.error('HANDLE_ERROR', error.message);
    }

};

// 修改教材顺序信息
const updateGradeOrder = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let id = req.params.grade_order_id.toString();
    let grades = req.body;
    if (!id || !grades || grades.length === 0) {
        return resWrapper.error('PARAMETERS_ERROR', '参数不能为空');
    }
    let cond = {
        _id: ObjectID(id),
    };
    let data = {
        grades: grades,
        utime: new Date()
    };
    let records = await grade_order.updateOne(cond, { '$set': data });
    return resWrapper.succ({ id: records });
};

module.exports = {
    getGradeOrderList,
    getGradeOrderById,
    updateGradeOrder,
    getRegionPressVersionById
};
