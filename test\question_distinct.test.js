/**
 * 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 9500,
    app_key: 'iyunxiao_kbplat180305',
};

describe('试题去重', function () {
    it('/kb_api/v2/question_distinct', async function () {
        let pathname = `/kb_api/v2/question_distinct`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            'parent': 2136276991,
            'children': [349107916700]
        };
        let result = await request.put(url).send(data);
        console.log(result.body);
        //assert.ok(!!result.body.id);
    });
});

