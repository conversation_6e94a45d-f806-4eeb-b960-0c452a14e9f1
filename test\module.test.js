/**
 * 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 8888,
    app_key: 'iyunxiao_kbplat180305',
};


describe('模块计数', function () {
    it('/kb_api/v2/modules', async function () {
        let pathname = `/kb_api/v2/counter`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            collection: 'module'
        }
        let result = await request.get(url).query(data);
        console.log(result.body);
        //assert.ok(!!result.body.id);
    });
});

describe('获取知识的考法和对象', function () {
    it.only('/kb_api/v2/knowledges/batch/relation', async function () {
        let pathname = `/kb_api/v2/knowledges/batch/relation`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            ids: [1475018751,1833893887 ] //1833893887
        }
        let result = await request.post(url).send(data);
        console.log(result.body);
        assert.ok(result.body.length>=0);
    });
});

describe('模块列表', function () {
    it('/kb_api/v2/modules', async function () {
        let pathname = `/kb_api/v2/modules/list`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            subject: '语文',
            period: '高中',
            attach: ''
        }
        let result = await request.get(url).query(data);
        console.log(JSON.stringify(result.body));
        assert.ok(result.body.length>=0);
    });
});

describe('模块详情byId', function () {
    it('/kb_api/v2/modules/:id', async function () {
        let id = 2147418111;
        let pathname = `/kb_api/v2/modules/${id}`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            attach: 'category'
        }
        let result = await request.get(url).query(data);
        console.log(JSON.stringify(result.body));
        assert.ok(result.body);
    });
});

describe('模块添加', function () {
    it('/kb_api/v2/modules', async function () {
        let pathname = `/kb_api/v2/modules`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            subject: '语文',
            period: '高中',
            children: [{
                id: 1,
                name: '模块1',
                knowledges: [{
                    id: 1475018751,
                    name: '边塞诗',
                    targets: [{
                        id: 1,
                        name: 'target1'
                    }],
                    know_method: [{
                        id: 44,
                        name: 'know_method44'
                    }]
                }]
            }, {
                id: 2,
                name: '模块2',
                knowledges: [{
                    id: 1833893887,
                    name: '排列句子顺序',
                    targets: [{
                        id: 2,
                        name: 'target1'
                    }],
                    know_method: [{
                        id: 44,
                        name: 'know_method44'
                    }]
                }]
            }]
        };
        let result = await request.post(url).send(data);
        console.log(result.body); // {}
        //assert.ok(!!result.body.id);
    });
});

describe('模块更新', function () {
    it('/kb_api/v2/modules/:id', async function () {
        let id = 2147418111;
        let pathname = `/kb_api/v2/modules/${id}`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            subject: '语文',
            period: '高中',
            children: [{
                id: 1,
                name: '模块2',
                knowledges: [{
                    id: 1475084287,
                    name: '古诗词比较阅读',
                    targets: [{
                        id: 1111,
                        name: 'target1111'
                    }],
                    know_method: [{
                        id: 2222,
                        name: 'know_method2222'
                    }]
                }]
            }]
        };
        let result = await request.put(url).send(data);
        console.log(result.body);
        //assert.ok(!!result.body.id);
    });
});
