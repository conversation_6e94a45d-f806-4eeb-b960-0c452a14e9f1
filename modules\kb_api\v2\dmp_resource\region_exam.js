const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');

const resourceCol = db.collection('region_exam_info');


const _parseQuery = (query) => {
    let {
        period,             // 学段，如：初中、高中，可不传（与grade重复）
        subject,            // 学科，如：数学、英语
        province,           // 省份，如：河南、山东
        city,             // 地区，如：郑州、济南
        grade,              // 初一、初二、初三、初四、高一、高二、高三（高二文/理、高三文/理）
     
        type,           // 考试类型，可选：'期末考试'，'期中考试'，'月考'，'全部'
        month,              // 月考试卷需要传，如：1，2，3，。。。，12
        exam_time,           // 期末考试/期中考试需要传，如：2019上半年；月考传：2019
        sch_id           // 学校ID
     
    } = query;
    let cond = {};
    period && (cond.period = period);
    subject && (cond.subject = subject);
    province && (cond.province = province);
    if (city === 0 || city)  cond.city = city;
    grade && (cond.grade = grade);
    type && (cond.type = type);
    if (month >=0 && month <= 12) cond.month = month;
    exam_time && (cond.exam_time = exam_time);
    if (!isNaN(sch_id)) cond.sch_id = sch_id;
    return cond;
}

// 获取区域考试分析基本信息统计
const getRegionExamStatistic = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    
    let cond = _parseQuery(req.body);

    let showField = {
        basic_info: 1, 'ques_analy.ques_type_analy': 1, 'ques_analy.ques_diff_analy': 1
    };
    let records = await resourceCol.find(
        cond
    ).project(showField).toArray();

    return resWrapper.succ({result: records});
};

// 获取区域考试分析知识点分析
const getRegionExamKnowledge = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let cond = _parseQuery(req.body);

    let showField = {
        know_analy: 1
    };
    let records = await resourceCol.find(
        cond
    ).project(showField).toArray();

       
    return resWrapper.succ({result: records});
};

// 获取区域考试分析试题分析
const getRegionExamQuestion = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let cond = _parseQuery(req.body);

    let records = await resourceCol.find(
        cond
    ).project({ques_analy: 1}).toArray();

   
    return resWrapper.succ({result: records});
};

// 获取新考试分析试题答案
const getRegionExamQuestionAnswer = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let qIds = req.query.qIds; 
    qIds = qIds.split(',');
    let cond = {_id: {$in:qIds} };

    let records = await db.collection('region_info_ques_answer').find(
        cond
    ).toArray();

   
    return resWrapper.succ({result: records});
};


// 获取区域考试分析学校列表
const getRegionExamSchool = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let cond = _parseQuery(req.body);

    if (cond.city === 0) {
        delete cond.city;
    }
    if (cond.type === '全部') delete cond.type;
    cond.sch_id = {$ne: 0};

    let ret = await resourceCol.aggregate([{
            $match: cond
        }, {
            $project: {school: 1, sch_id: 1}
        }, {
            $group: {_id: {school_id: '$sch_id', school_name: '$school'}}
        }]).toArray();



    return resWrapper.succ({result: ret});
};


module.exports = {
    getRegionExamStatistic,
    getRegionExamKnowledge,
    getRegionExamQuestion,
    getRegionExamQuestionAnswer,
    getRegionExamSchool
};
