/*
 * Desc: redis wrapper
 * Author: guochanghui
 */

const Redis = require('ioredis');

function Rediser() {
    this.redis = null;
    this._status = false;
}

Rediser.prototype.init = function (conf, callback) {
    let redis = new Redis({
        port: conf.port,
        host: conf.host,
        db: conf.db,
        password: conf.password,
        retryStrategy(times) {
            return Math.min(times * 10, 2000);      // delay in ms
        },
    });
    redis.on('connect', () => {
        this._status = true;
        this.redis = redis;
        callback(null);
    });
    redis.on('error', (err) => {
        this._status = false;
        callback(err);
    });
};

Rediser.prototype.get = function (key, callback) {
    callback = callback || new Function();
    return new Promise((resolve, reject) => {
        this.redis.get(key, (err, value) => {
            if (err) {
                reject(err);
                return callback(err, null);
            }
            if (!value) {
                resolve(null);
                return callback(null, null);
            }
            try {
                value = JSON.parse(value);
            } catch (err) {
                reject(err);
                return callback(err, null);
            }
            resolve(value);
            return callback(null, value);
        });
    });
};

Rediser.prototype.set = function (key, value, time, callback) {
    if (typeof time === 'function') {
        callback = time;
        time = null;
    }
    if (!value) {
        return callback(null, null);
    }

    callback = callback || new Function();

    return new Promise((resolve, reject) => {
        try {
            value = JSON.stringify(value);
        } catch (e) {
            reject(e);
            return callback(e, null);
        }
        if (!time) {
            this.redis.set(key, value, () => {
                resolve();
                callback();
            });
        } else {
            this.redis.setex(key, time, value, () => {
                resolve();
                callback();
            });
        }
    });
};

Rediser.prototype.del = function (key, callback) {
    callback = callback || new Function();
    return new Promise((resolve) => {
        this.redis.del(key, () => {
            resolve();
            callback();
        });
    });
};

Rediser.prototype.getCache = function () {
    return this.redis;
};

Rediser.prototype.hmget = function (key, fields) {
    return new Promise(((resolve, reject) => {
        this.redis.hmget(key, fields).then((values) => {
            try {
                resolve(values.map(value => JSON.parse(value)));
            } catch (e) {
                resolve(values);
            }
        }).catch(reject);
    }));
};

Rediser.prototype.hmset = function (key, map) {
    return new Promise(((resolve, reject) => {
        this.redis.hmset(key, map, (err, data) => {
            if (err) {
                reject(err);
                return;
            }
            resolve(data);
        });
    }));
};

module.exports = new Rediser();
