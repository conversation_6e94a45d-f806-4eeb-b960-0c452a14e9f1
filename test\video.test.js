/* eslint-disable no-undef */
const config = require('config');
const expect = require('chai').expect;
const superagent = require('superagent');
const url = require('url');
const host = url.format({
    protocol: 'http',
    hostname: 'localhost',//kb.gotiku.com
    port: 8835
});
const apikey = config.get('app_keys_private')[0];
//测试视频专辑编号
const test_category_id = 2135949311;
const test_video_id = 1818099711;
const test_edit_video_id = 1818034175;

describe('视频专辑列表 /videos/categories', () => {
    it('正例测试:视频专辑列表', (done) => {
        let url = [host, '/kb_api/v2/videos/categories',
        ].join('');
        superagent
            .get(url)
            .query({
                app_key: apikey,
                peroid: '初中',// [可选] 学段   
                subject: '数学', // [可选] 学科。
                offset: 0, // [可选]分页参数，起始条目，从0开始  默认0                        
                limit: 20, // [可选]分页参数，每页条目数量 默认20
            })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);

                expect(ret.data.categories).to.be.an('array');

                if (ret.data.categories.length > 0) {
                    for (let i in ret.data.categories) {
                        let data1 = ret.data.categories[i];
                        let key1 = ['id',                                  // 视频专辑id  
                            'period',                             // 学段
                            'subject',                            // 学科
                            'type',                               // 专辑名称
                            'description',                        // 视频专辑简介
                            'teachers',
                            'profile_url',                    // 分类图片
                        ]
                        expect(data1).to.have.any.keys(key1);
                        expect(data1.teachers).to.be.an('array');
                        if (data1.teachers.length > 0) {
                            for (let i in data1.teachers) {
                                let data2 = data1.teachers[i];
                                expect(data2).to.have.any.keys('name', 'description');

                            }
                        }

                    }
                }
                done();
            });
    })

})

describe('查询视频专辑信息 /videos/categories/{category_id}', () => {
    it('正例测试:查询区域教材统计信息', (done) => {
        let url = [host, '/kb_api/v2/region_books/regions/',
        ].join('');
        superagent
            .get(url)
            .query({
                app_key: apikey,
                'province': '安徽',
                'city': 'all',
                'country': 'all'
            })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.region).to.be.an('object');
                expect(ret.data.region).to.have.any.keys('id', 'province', 'city', 'country');
                expect(ret.data.books).to.be.an('array');
                if (ret.data.books.length > 0) {
                    for (let i in ret.data.books) {
                        let data1 = ret.data.books[i];
                        expect(data1).to.have.any.keys('name', 'grades');
                        expect(data1.grades).to.be.an('array');
                        if (data1.grades.length > 0) {
                            for (let i in data1.grades) {
                                let data2 = data1.grades[i];
                                expect(data2).to.have.any.keys('name', 'subjects');
                                expect(data2.subjects).to.be.an('array');
                                if (data2.subjects.length > 0) {
                                    for (let i in data2.subjects) {
                                        let data3 = data2.subjects[i];
                                        expect(data3).to.have.any.keys('name', 'press_version');

                                    }
                                }
                            }
                        }

                    }
                }
                done();
            });
    })

})

describe('创建专辑 videos/categories', () => {


    it('正例测试:创建专辑', (done) => {
        let url = [host, '/kb_api/v2/videos/categories',
        ].join('');
        var body = {
            'category': {
                'period': '初中',
                'subject': '数学',
                'type': '索罗中考复习测试数据' + Math.random() * 10,
                'description': '',
                'teachers': [
                    {
                        'name': '力强',
                        'description': '测教师哪里来的'
                    }
                ],
                'profile_url': 'http://yx-kbs-ks3.haofenshu.com/images/f342d56791f9fdbcd1c9b51b91e85e52.jpg',
                'from': 'suoluo',
                'acl': 'public'
            }
        };
        superagent
            .post(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(0);
                done();
            });
    })
    it('反例测试:创建专辑  period 不存在', (done) => {
        let url = [host, '/kb_api/v2/videos/categories',
        ].join('');
        var body = {
            'category': {

                'subject': '数学',
                'type': '索罗中考复习测试数据' + Math.random() * 10,
                'description': '',
                'teachers': [
                    {
                        'name': '力强',
                        'description': '测教师哪里来的'
                    }
                ],
                'profile_url': 'http://yx-kbs-ks3.haofenshu.com/images/f342d56791f9fdbcd1c9b51b91e85e52.jpg',
                'from': 'suoluo',
                'acl': 'public'
            }
        };
        superagent
            .post(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(3);
                done();
            });
    })

})

describe('编辑视频专辑 videos/categories/{category_id}', () => {


    it('正例测试:编辑视频专辑', (done) => {
        let url = [host, '/kb_api/v2/videos/categories/' + test_category_id,
        ].join('');
        var body = {
            'category': {

                'type': '索罗中考复习测试数据' + Math.random() * 10,
                'description': '',
                'teachers': [
                    {
                        'name': '力强',
                        'description': '测教师哪里来的'
                    }
                ],
                'profile_url': 'http://yx-kbs-ks3.haofenshu.com/images/f342d56791f9fdbcd1c9b51b91e85e52.jpg',

            }
        };
        superagent
            .put(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(0);
                done();
            });
    })
    it('反例测试:编辑视频专辑  添加错误的修改字段', (done) => {
        let url = [host, '/kb_api/v2/videos/categories/' + test_category_id,
        ].join('');
        var body = {
            'category': {

                'subject1': '数学',
                'type': '索罗中考复习测试数据' + Math.random() * 10,
                'description': '',
                'teachers': [
                    {
                        'name': '力强',
                        'description': '测教师哪里来的'
                    }
                ],
                'profile_url': 'http://yx-kbs-ks3.haofenshu.com/images/f342d56791f9fdbcd1c9b51b91e85e52.jpg',
                'from': 'suoluo',
                'acl': 'public'
            }
        };
        superagent
            .put(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(3);
                done();
            });
    })

})
describe('视频专辑添加已有视频 videos/categories/{category_id}/videos_existing/{video_id}', () => {


    it('正例测试:视频专辑添加已有视频', (done) => {
        let url = [host, '/kb_api/v2/videos/categories/' + test_category_id + '/videos_existing/' + test_video_id,
        ].join('');
        superagent
            .put(url)
            .query({
                app_key: apikey,

            })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);

                expect(ret.code).to.be.equal(0);



                done();
            });
    })


})

describe('视频专辑添加视频  videos/categories/{category_id}/videos', () => {


    it('正例测试:视频专辑添加视频', (done) => {
        let url = [host, '/kb_api/v2/videos/categories/' + test_category_id + '/videos',
        ].join('');
        var body = {
            'name': '【知识点】实数(2)',
            'data_url': 'http://yx-kbs-ks3.haofenshu.com/videos/bba2aa61f31e12cd21cedaeccc86b2eb.mp4',
            'period': '初中',
            'subject': '数学',

            'from': 'suoluo',
            'duration': 1,
            'type': '专题',
            'description': '等等',
            'teachers': [
                {
                    'name': '力强',
                    'description': '测教师哪里来的'
                }
            ],
            'recorder': '你',
            'knowledges': [{
                'id': 0,
                'name': 'ceshi '
            }]


        }
        superagent
            .post(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(0);
                done();
            });
    })
    it('反例测试:视频专辑添加视频 缺少name字段', (done) => {
        let url = [host, '/kb_api/v2/videos/categories/' + test_category_id + '/videos',
        ].join('');
        var body = {
            'name1': '【知识点】实数(2)',
            'data_url': 'http://yx-kbs-ks3.haofenshu.com/videos/bba2aa61f31e12cd21cedaeccc86b2eb.mp4',
            'period': '初中',
            'subject': '数学',

            'from': 'suoluo',
            'duration': 1,
            'type': '专题',
            'description': '等等',
            'teachers': [
                {
                    'name': '力强',
                    'description': '测教师哪里来的'
                }
            ],
            'recorder': '你',
            'knowledges': [{
                'id': 0,
                'name': 'ceshi '
            }]


        }
        superagent
            .post(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(3);
                done();
            });
    })

})
describe('视频专辑查询视频列表  videos/categories/{category_id}/videos', () => {


    it('正例测试:视频专辑添查询视频列表', (done) => {
        let url = [host, '/kb_api/v2/videos/categories/' + test_category_id + '/videos',
        ].join('');
        superagent
            .get(url)
            .query({
                app_key: apikey,
                offset: 0, // [可选]分页参数，起始条目，从0开始  默认0                        
                limit: 20, // [可选]分页参数，每页条目数量 默认20
            })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(0);

                expect(ret.data.videos).to.be.an('array');

                if (ret.data.videos.length > 0) {
                    for (let i in ret.data.videos) {
                        let data1 = ret.data.videos[i];
                        let key1 = ['id',
                            'name',
                            'data_url',
                            'type',
                            'period',
                            'subject',
                            'duration',
                            'teachers',
                            'c_time',
                        ]
                        expect(data1).to.have.any.keys(key1);
                        expect(data1.teachers).to.be.an('array');
                        if (data1.teachers.length > 0) {
                            for (let i in data1.teachers) {
                                let data2 = data1.teachers[i];
                                expect(data2).to.have.any.keys('name', 'description');

                            }
                        }
                    }
                }
                done();
            });
    })


})

describe('视频专辑查询视频信息  videos/categories/{category_id}/videos/{video_id}', () => {


    it('正例测试:视频专辑添查询视频列表', (done) => {
        let url = [host, '/kb_api/v2/videos/categories/' + test_category_id + '/videos/' + test_video_id,
        ].join('');
        superagent
            .get(url)
            .query({
                app_key: apikey,

            })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(0);


                let data1 = ret.data;
                let key1 = ['id',//  视频
                    'name',           // 视频名称
                    'data_url',       // 视频地址
                    'period',         // 学段
                    'subject',        // 学科
                    'description',     // 视频介绍，没有为空字符串；
                    'duration',           // 视频时长,
                    'teachers',
                    'knowledges',
                    'recorder',//录制人
                    'label',// 视频标签
                    'type',//视频类型 同步，专题
                    'from',//视频来源              
                ]
                expect(data1).to.have.any.keys(key1);
                expect(data1.teachers).to.be.an('array');
                if (data1.teachers.length > 0) {
                    for (let i in data1.teachers) {
                        let data2 = data1.teachers[i];
                        expect(data2).to.have.any.keys('name', 'description');

                    }
                }
                expect(data1.knowledges).to.be.an('array');
                if (data1.knowledges.length > 0) {
                    for (let i in data1.knowledges) {
                        let data2 = data1.knowledges[i];
                        expect(data2).to.have.any.keys('name', 'id');

                    }
                }
                expect(data1.label).to.be.an('array');

                done();
            });
    })


})

describe('视频专辑移除视频  videos/categories/{category_id}/videos/{video_id}', () => {


    it('正例测试:视频专辑移除视频', (done) => {
        let url = [host, '/kb_api/v2/videos/categories/' + test_category_id + '/videos/' + test_video_id,
        ].join('');
        superagent
            .del(url)
            .query({
                app_key: apikey,

            })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(0);


                done();
            });
    })


})


describe('编辑视频  videos/{video_id}', () => {


    it('正例测试:编辑视频', (done) => {
        let url = [host, '/kb_api/v2/videos/' + test_edit_video_id,
        ].join('');
        var body = {
            'name': '【知识点】实数(2)',
            'data_url': 'http://yx-kbs-ks3.haofenshu.com/videos/bba2aa61f31e12cd21cedaeccc86b2eb.mp4',
            'period': '初中',
            'subject': '数学',
            'from': 'suoluo',
            'duration': 1,
            'type': '专题',
            'description': '等等' + Math.random(),
            'teachers': [
                {
                    'name': '力强',
                    'description': '测教师哪里来的'
                }
            ],
            'recorder': '你',
            'knowledges': [{
                'id': 0,
                'name': 'ceshi '
            }]


        }
        superagent
            .put(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(0);
                done();
            });
    })
    it('反例测试:编辑视频 duration 不是数字', (done) => {
        let url = [host, '/kb_api/v2/videos/' + test_edit_video_id,
        ].join('');
        var body = {
            'name': '【知识点】实数(2)',
            'data_url': 'http://yx-kbs-ks3.haofenshu.com/videos/bba2aa61f31e12cd21cedaeccc86b2eb.mp4',
            'period': '初中',
            'subject': '数学',

            'from': 'suoluo',
            'duration': '22下',
            'type': '专题',
            'description': '等等',
            'teachers': [
                {
                    'name': '力强',
                    'description': '测教师哪里来的'
                }
            ],
            'recorder': '你',
            'knowledges': [{
                'id': 0,
                'name': 'ceshi '
            }]


        }
        superagent
            .put(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(3);
                done();
            });
    })

})

describe('查询视频  /videos/video_list', () => {


    it('正例测试:查询视频', (done) => {
        let url = [host, '/kb_api/v2/videos/video_list',
        ].join('');
        superagent
            .get(url)
            .query({
                app_key: apikey,
                offset: 0, // [可选]分页参数，起始条目，从0开始  默认0                        
                limit: 20, // [可选]分页参数，每页条目数量 默认20
                name: '知识点',// [可选] 视频名称

            })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(0);

                expect(ret.data.videos).to.be.an('array');

                if (ret.data.videos.length > 0) {
                    for (let i in ret.data.videos) {
                        let data1 = ret.data.videos[i];
                        let key1 = ['id',
                            'name',
                            'data_url',
                            'type',
                            'period',
                            'subject',
                            'duration',
                            'teachers',
                            'c_time',
                        ]
                        expect(data1).to.have.any.keys(key1);
                        expect(data1.teachers).to.be.an('array');
                        if (data1.teachers.length > 0) {
                            for (let i in data1.teachers) {
                                let data2 = data1.teachers[i];
                                expect(data2).to.have.any.keys('name', 'description');

                            }
                        }
                    }
                }
                done();
            });
    })


});

describe('添加视频  videos', () => {


    it('正例测试:添加视频', (done) => {
        let url = [host, '/kb_api/v2/videos',
        ].join('');
        var body = {
            'name': '【知识点】实数(2)',
            'data_url': 'http://yx-kbs-ks3.haofenshu.com/videos/bba2aa61f31e12cd21cedaeccc86b2eb.mp4',
            'period': '初中',
            'subject': '数学',

            'from': 'suoluo',
            'duration': 1,
            'type': '专题',
            'description': '等等',
            'teachers': [
                {
                    'name': '力强',
                    'description': '测教师哪里来的'
                }
            ],
            'recorder': '你',
            'knowledges': [{
                'id': 0,
                'name': 'ceshi '
            }]


        }
        superagent
            .post(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(0);
                done();
            });
    })
    it('反例测试:视频专辑添加视频 缺少name字段', (done) => {
        let url = [host, '/kb_api/v2/videos',
        ].join('');
        var body = {
            'name1': '【知识点】实数(2)',
            'data_url': 'http://yx-kbs-ks3.haofenshu.com/videos/bba2aa61f31e12cd21cedaeccc86b2eb.mp4',
            'period': '初中',
            'subject': '数学',
            'from': 'suoluo',
            'duration': 1,
            'type': '专题',
            'description': '等等',
            'teachers': [
                {
                    'name': '力强',
                    'description': '测教师哪里来的'
                }
            ],
            'recorder': '你',
            'knowledges': [{
                'id': 0,
                'name': 'ceshi '
            }]


        }
        superagent
            .post(url)
            .query({
                app_key: apikey,
            })
            .set('Content-Type', 'application/json')
            .send(body)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret).to.be.an('object');
                expect(ret.code).to.be.equal(3);
                done();
            });
    });

})