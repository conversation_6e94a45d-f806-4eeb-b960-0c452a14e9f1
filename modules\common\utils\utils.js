const rediser = require('./rediser');
const crypto = require('crypto');

RETURN_CODE = {
	'OK': 00,                   // 处理成功
	'URL_ERROR': 01,            // api错误
	'AUTH_ERROR': 02,           // app_key, app_seceret认证信息错误
	'PARAMETERS_ERROR': 03,     // 上送参数错误
	'HANDLE_ERROR': 04,         // 业务处理错误
	'NULL_ERROR': 05,           // 空数据
	'EXCEED_FRQ_ERROR': 06,     // 访问频率过快
	'ILLEGAL_USER': 07,         // 被封的用户
}

RETURN_MSG = {
	'OK': 'OK',                                 // 处理成功
	'URL_ERROR': 'api not found',               // api错误
	'AUTH_ERROR': 'authentication error',   	// app_key, app_seceret认证信息错误
	'PARAMETERS_ERROR': 'parameters error',     // 上送参数错误
	'HANDLE_ERROR': 'servercie error',          // 业务处理错误
	'NULL_ERROR': 'cannot query data',          // 查询不到数据
	'EXCEED_FRQ_ERROR': 'api freq out of limit',// 访问频率过快
	'ILLEGAL_USER': 'user is untrusted',        // 被封的用户
}

// 错误处理
function err_handler(type, desc, res) {
	res.status(400);
	var msg = desc ? RETURN_MSG[type] + ":" + desc : RETURN_MSG[type];
	res.json({
		code: RETURN_CODE[type],
		msg: msg});
}

// 返回数据
function return_data(res, data) {
	if (data) {
		res.json(data);
	} else {
		err_handler('NULL_ERROR', null, res);
	}
}

/***************************
 * 规范化int数据
 * pname: 	参数名称
 * p: 		参数值
 * opt: 	是否为可选参数
 * def: 	默认值
 * mav: 	最大值
 * *************************/
function norm_int(pname, p, opt, def, max_v){
	if (!opt && !p) { // 必输项不能为空
		throw (pname + "不能为空！");
	}
	if (!p) {
		return def;
	}
	var reg = /^[0-9]+$/;
	if (!reg.test(p)) {
		throw (pname + '不是整数！');
	}
	try {
		p = parseInt(p)
		p = (p <= max_v) ? p : parseInt(max_v);
		p = (p < 0) ? 0 : p;
	} catch (e) {
		p = 0;
	}
	return p;
}

/***************************
 * 规范化float数据
 * pname: 	参数名称
 * p: 		参数值
 * opt: 	是否为可选参数
 * def: 	默认值
 * mav: 	最大值
 * *************************/
function norm_float(pname, p, opt, def, max_v){
	if (!opt && !s) { // 必输项不能为空
		throw (pname + "不能为空！");
	}
	if (!p) {
		return def;
	}
	p = (p < max_v) ? p : max_v;
	p = (p < 0) ? 0.0 : p;
	return p;
}

/***************************
 * 规范化str数据
 * pname: 	参数名称
 * p: 		参数值
 * opt: 	是否为可选参数
 * def: 	默认值
 * valids: 	有效的取值列表
 * *************************/
function norm_str(pname, p, opt, def, valids){
	if (!opt && !p) { // 必输项不能为空
		throw (pname + "不能为空！");
	}
	if (!p) {
		return def;
	}
	if (valids.indexOf(p)>=0) {
		return p;
	} else {
		throw (pname + '参数不合法！');
	}
}

function get_user_tag(req, callback) {
	var token = req.query['api_key'];
	if (null == token) {
		return callback(null, null);
	}
	rediser.get(token, function(err, user_tag) {
		if (err) {
			return callback(null, null);
		}
		return callback(null, user_tag);
	});
}

/*
 * Desc:
 *      将元素分割为多组，每组元素个数为n
 * Params:
 *      ids - 待分割的id列表
 *      k_per_grp - 每组内元素个数
 * Return:
 * 		分割后的id列表
 * Author:
 *      zhangjun
 * Date:
 *      2016-06-28
*/
function ids_split(ids, k_per_grp) {
	var ids_grp = new Array();
	var t = [];
	for (var i in ids) {
		if (i % k_per_grp == (k_per_grp-1)) {
			ids_grp.push(t);
			t = [];
		}
		t.push(ids[i]);
	}
	if (t.length > 0) {
		ids_grp.push(t);
	}
	return ids_grp;
}

/*
 * Desc:
 *      id编码
 * Params:
 *      digit - 编码前的整数值
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-11-15
*/
function encode(digit){
	/**
	 *  When I find JS can't shift operation that
	 *  I feel the whole world is not harmonious.
	 *
	 */
	var salt = 2147483647;
	var _digit  = Math.floor(digit / Math.pow(2, 32)) * Math.pow(2, 32);
	_digit += (digit % (0xffff + 1) - digit % (0xff + 1)) * Math.pow(2, 16);
	_digit += (digit % (0xff + 1)) * Math.pow(2, 16);
	_digit += Math.floor(((digit % 0x00ffffff+1) - (digit % (0xffff + 1))) / Math.pow(2, 8));
	_digit += Math.floor(((digit % (0xffffffff+1)) - (digit % (0xffffff+1))) / Math.pow(2, 24));

	if (_digit > salt){
		var low = salt - _digit % (salt + 1);
		var height = Math.floor(_digit / Math.pow(2, 31)) *  Math.pow(2, 31);
		_digit = height + low;
	} else {
		_digit = salt - _digit;
	}
	return _digit
}

/*
 * Desc:
 *      id解码
 * Params:
 *      digit - 已编码的id
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-11-15
*/
function decode(digit){

	var salt = 2147483647;
	if (digit > salt){
		var low = salt - digit % (salt + 1);
		var height = Math.floor(digit / Math.pow(2, 31)) *  Math.pow(2, 31);
		digit = height + low;
	} else {
		digit = salt - digit;
	}

	var _digit  = Math.floor(digit / Math.pow(2, 32)) * Math.pow(2, 32);
	_digit += (digit % (0xff + 1)) * Math.pow(2, 24);
	_digit += (digit % (0xffff + 1) - digit % (0xff + 1)) * Math.pow(2, 8);
	_digit += Math.floor(((digit % (0xffffffff+1)) - (digit % (0xffffff+1))) / Math.pow(2, 16));
	_digit += Math.floor(((digit % (0xffffff+1)) - (digit % (0xffff+1))) / Math.pow(2, 16));
	return _digit;
}

function get_day_of_year(t) {
	var new_year = new Date();
	new_year.setMonth(0);
	new_year.setDate(1);
	new_year.setHours(0);
	new_year.setMinutes(0);
	new_year.setMilliseconds(0);
	return parseInt((t-new_year)/(1000 * 60 * 60 *24)+2);
}

function get_time_seq(t) {
	var d_seq = get_day_of_year(t);
	var h_seq = t.getHours() + t.getDate()*24;
	var m_seq = t.getMinutes() + t.getHours()*60;
	// 16777216=4096*4096
	return tiny_encode(d_seq, h_seq, m_seq, 4096);
}

function parse_time_seq(seq) {
	return tiny_decode(seq, 4096);
}

function to_local_time(date_obj) {
    var y = date_obj.getFullYear();
    var m = date_obj.getMonth() + 1;
    var d = date_obj.getDate();
    var ho = date_obj.getHours(); //
    var mi = date_obj.getMinutes();
    var se = date_obj.getSeconds();
    return `${y}-${m}-${d} ${ho}:${mi}:${se}`;
}

/*
 * Desc:
 *      加密算法
 *      输入是三个整数以及base值，base值需要是2的幂
 * Params:
 *      d - a integer represents day
 *      h - a integer represents hour
 *      m - a integer represents min
 *      base - a number must be 2^exp
 *      st. h<base, m<base
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2017-03-30
*/
function tiny_encode(d, h, m, base) {
	if ( base&(base-1) != 0 || // base != 2^exp
			h >= base ||
			m >= base ) {
		throw Error('base must be 2^exp, h or m should little than base');
	}
	return d*base*base + h*base + m;
}

/*
 * Desc:
 *      desc
 * Params:
 *      param1 - comment
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2017-03-30
*/
function tiny_decode(num, base) {
	if (num <= 0) {
		return {day: 0, hour: 0, min: 0};
	}
	var t = num / base;
	var d = parseInt(t / base);
	var h = parseInt(t) & (base -1);
	var m = num & (base-1) ;
	return {day: d, hour: h, min: m};
}

function is_empty_obj(obj) {
    if (Array.isArray(obj)) {
        return false;
    }
	for ( var name in obj ) {
		return false;
	}
	return true;
}

function getMd5(x){
	let md5 = crypto.createHash('md5');
	return md5.update(x).digest('hex');
}

/**
 * 格式化日期(YYYY年MM月DD日)
 * @param date
 * @returns {string}
 */
function format_date(date) {
	if (!date) date = new Date()
	let year = date.getFullYear();
	let month = date.getMonth() + 1;
	let day = date.getDate();
	// 将月份和日期补齐两位数
	month = month < 10 ? '0' + month : month;
	day = day < 10 ? '0' + day : day;
	return year + '年' + month + '月' + day + '日';
}

module.exports = {
	err_handler: err_handler,
	norm_int: norm_int,
	norm_float: norm_float,
	norm_str: norm_str,
	return_data: return_data,
	ids_split: ids_split,
	get_user_tag: get_user_tag,
	encode: encode,
	decode: decode,
	get_day_of_year: get_day_of_year,
	get_time_seq: get_time_seq,
	parse_time_seq: parse_time_seq,
    to_local_time: to_local_time,
	is_empty_obj: is_empty_obj,
	tiny_encode: tiny_encode,
	tiny_decode: tiny_decode,
	getMd5: getMd5,
	format_date: format_date
}
