const ObjectID = require('mongodb').ObjectID;
const Ajv = require('ajv');
const ajv = new Ajv({removeAdditional: 'all'});
const Logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const schemaTag = require('../schemas/video_tag');

// 数据库集合名
const videoTagColl = 'video_tag';
const videoCateTagColl = 'video_category_tag';
const videoColl = 'video';
const videoCateColl = 'video_category';

////////////////////////// 视频标签开始 ---------------->

// kbp审核通过，推送过来
const postVideoTag = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    try {
        let data = getDataTemplate(body);
        // 格式校验
        if (!ajv.validate(schemaTag.putVideoTag, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '标签格式错误');
        }
        data.ctime = new Date();
        data.utime = new Date();
        let result = await db.collection(videoTagColl).insertOne(data);
        if (result.insertedId) {
            //let id = result.insertedId.toString();
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '添加出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// kbp审核通过，推送过来
const updateVideoTag = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    let body = req.body;
    try {
        let data = getDataTemplate(body);
        // 格式校验
        if (!ajv.validate(schemaTag.putVideoTag, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '标签格式错误');
        }
        data.utime = new Date();
        let cond = {_id: ObjectID(id)};
        let setData = {$set: data};
        let result = await db.collection(videoTagColl).updateOne(cond, setData, {upsert: true});
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '修改出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 检查标签是否已使用
const checkVideoTagIsUsed = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.query.id; // 标签组 id 必传，因为是检查某个标签组下的标签是否使用。
    let name = req.query.name; // 参数标签名称，逗号分割
    if (!id || !name) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数缺少id，标签名称');
    }
    let tagNameArr = name.split(',');
    try {
        let result = await getTagIsUsed(id, tagNameArr, videoColl);
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 检查标签组名称是否重复
const checkVideoGroupNameExists = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {id, name, period, subject} = req.query;
    if (!name || !period || !subject ) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数缺少标签组名称,学段，学科');
    }
    let cond = {period, subject, name};
    if (id) {
        cond['_id'] = {$ne: ObjectID(id)};
    }
    let project = {_id: 1};
    let isExists = false;
    try {
        let questionTag = await db.collection(videoTagColl).findOne(cond, {fields: project});
        if (questionTag) {
            isExists = true;
        }
        responseWrapper.succ({status: isExists});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 试题标签列表
const getVideoTagsList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {period, subject, offset, limit} = req.query;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '传参缺少学段学科');
    }
    offset = offset ? parseInt(offset) : 0;
    limit = limit ? parseInt(limit) : 10;
    try {
        let cond = {subject: subject, period: period};
        let result = await db.collection(videoTagColl).find(cond).skip(offset).limit(limit).sort({ctime: -1}).toArray();
        let totalNum = await db.collection(videoTagColl).count(cond);
        for (let item of result) {
            item.id = item._id;
            delete item._id;
        }
        responseWrapper.succ({total_num: totalNum, list: result});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 获取标签详情
const getVideoTagDetail = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    try {
        let cond = {_id: ObjectID(id)};
        let result = await db.collection(videoTagColl).findOne(cond);
        if (result && result._id) {
            result.id = id;
            delete result._id;
            let allTagNameArr = getAllTagNamesArr(result); // 获取所有标签名称
            result.tags_used = await getTagIsUsed(id, allTagNameArr, videoColl); // 获取已经使用的标签
        }
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//  <--------------------- 视频标签结束 //////////////////


////////////////////////// 专辑标签开始 ---------------->

// kbp审核通过，推送过来
const postVideoCateTag = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    try {
        let data = getDataTemplate(body);
        // 格式校验
        if (!ajv.validate(schemaTag.putVideoCateTag, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '标签格式错误');
        }
        data.ctime = new Date();
        data.utime = new Date();
        let result = await db.collection(videoCateTagColl).insertOne(data);
        if (result.insertedId) {
            //let id = result.insertedId.toString();
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '添加出错了');
        }
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// kbp审核通过，推送过来
const updateVideoCateTag = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    let body = req.body;
    try {
        let data = getDataTemplate(body);
        // 格式校验
        if (!ajv.validate(schemaTag.putVideoCateTag, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '标签格式错误');
        }
        data.utime = new Date();
        let cond = {_id: ObjectID(id)};
        let setData = {$set: data};
        let result = await db.collection(videoCateTagColl).updateOne(cond, setData, {upsert: true});
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '修改出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 检查标签是否已使用
const checkVideoCateTagIsUsed = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.query.id; // 标签组 id 必传，因为是检查某个标签组下的标签是否使用。
    let name = req.query.name; // 参数标签名称，逗号分割
    if (!id || !name) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数缺少id，标签名称');
    }
    let tagNameArr = name.split(',');
    try {
        let result = await getTagIsUsed(id, tagNameArr, videoCateColl);
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 检查标签组名称是否重复
const checkVideoCateGroupNameExists = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {id, name, period, subject} = req.query;
    if (!name || !period || !subject ) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数缺少标签组名称,学段，学科');
    }
    let cond = {period, subject, name};
    if (id) {
        cond['_id'] = {$ne: ObjectID(id)};
    }
    let project = {_id: 1};
    let isExists = false;
    try {
        let questionTag = await db.collection(videoCateTagColl).findOne(cond, {fields: project});
        if (questionTag) {
            isExists = true;
        }
        responseWrapper.succ({status: isExists});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 标签列表
const getVideoCateTagsList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {period, subject, offset, limit} = req.query;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '传参缺少学段学科');
    }
    offset = offset ? parseInt(offset) : 0;
    limit = limit ? parseInt(limit) : 10;
    try {
        let cond = {subject: subject, period: period};
        let result = await db.collection(videoCateTagColl).find(cond).skip(offset).limit(limit).sort({ctime: -1}).toArray();
        let totalNum = await db.collection(videoCateTagColl).count(cond);
        for (let item of result) {
            item.id = item._id;
            delete item._id;
        }
        responseWrapper.succ({total_num: totalNum, list: result});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 获取标签详情
const getVideoCateTagDetail = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    try {
        let cond = {_id: ObjectID(id)};
        let result = await db.collection(videoCateTagColl).findOne(cond);
        if (result && result._id) {
            result.id = id;
            delete result._id;
            let allTagNameArr = getAllTagNamesArr(result); // 获取所有标签名称
            result.tags_used = await getTagIsUsed(id, allTagNameArr, videoCateColl); // 获取已经使用的标签
        }
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//  <--------------------- 专辑标签结束 //////////////////


const getDataTemplate = (body) => {
    return {
        period: body.period,
        subject: body.subject,
        name: body.name,
        tag_type: body.tag_type,
        values: body.values
    };
};

// 过滤出已使用的标签名
const getTagIsUsed = async (id, tagNameArr, collectionName) => {
    let result = [];
    let project = {_id: 1};
    for (let k = 0, kLen = tagNameArr.length; k < kLen; k++) {
        let tagName = tagNameArr[k];
        let eleMatch = {id: id, 'values.name': tagName};
        let cond = {'tags': {$elemMatch: eleMatch}};
        let question = await db.collection(collectionName).findOne(cond, {fields: project});
        if (question) {
            if (!result.includes(tagName)) {
                result.push(tagName);
            }
        }
    }
    return result;
};

// 获取所有标签名
const getAllTagNamesArr = (result) => {
    let allTagNameArr = [];
    if (result && Array.isArray(result.values)) {
        for (let value of result.values) {
            if (value.name && !allTagNameArr.includes(value.name)) {
                allTagNameArr.push(value.name);
            }
        }
    }
    return allTagNameArr;
};


module.exports = {
    postVideoTag,
    updateVideoTag,
    checkVideoTagIsUsed,    // 检查标签是否已使用
    checkVideoGroupNameExists, // 检查标签组名称是否重复
    getVideoTagsList, // 试题标签列表
    getVideoTagDetail, // 获取标签详情

    postVideoCateTag,
    updateVideoCateTag,
    checkVideoCateTagIsUsed,
    checkVideoCateGroupNameExists,
    getVideoCateTagsList,
    getVideoCateTagDetail,    
};
