const createKbExampaperSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        id: {
            type: 'number',
        },
        type: {
            type: 'string',
        },
        subject: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        score: {
            type: 'number',
        },
        name: {
            type: 'string',
        },
        sch_name: {
            type: 'string',
        },
        sch_short_name: {
            type: 'string',
        },
        region: {
            type: 'string',
        },
        press_version: {
            type: 'string',
        },
        grade: {
            type: 'string',
        },
        province: {
            type: 'string',
        },
        city: {
            type: 'string',
        },
        provinces: {
            type: 'array',
        },
        to_year: {
            type: 'integer',
        },
        from_year: {
            type: 'integer',
        },
        blocks: {
            type: 'array',
        },
        type_score: {
            type: 'array',
        },
        user_name: { type: 'string' },
        user_id: { type: 'number' },
        from: { type: 'string' },
        dbs: { type: 'array' }
    }
};

const createHfsExampaperSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {

        g_paper_id: {
            type: 'string',
        },
        id: {
            type: 'integer',
        },
        type: {
            type: 'string',
        },
        subject: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        score: {
            type: 'number',
        },
        name: {
            type: 'string',
        },
        sch_name: {
            type: 'string',
        },
        sch_short_name: {
            type: 'string',
        },
        region: {
            type: 'string',
        },
        press_version: {
            type: 'string',
        },
        grade: {
            type: 'string',
        },
        province: {
            type: 'string',
        },
        city: {
            type: 'string',
        },
        provinces: {
            type: 'array',
        },
        to_year: {
            type: 'integer',
        },
        from_year: {
            type: 'integer',
        },
        blocks: {
            type: 'array',
        },
        type_score: {
            type: 'array',
        },
        user_name: { type: 'string' },
        user_id: { type: 'number' },
        from: { type: 'string' },
    }
};
//
const createExampaperUnitSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        name: {
            type: 'string',
        },
        grade: {
            type: 'string',
        },
        grade_semester: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        press_version: {
            type: 'string',
        },
        album_type: {
            type: 'string'
        },
        difficulty_proportion: {
            type: 'array'
        },
        chapter_id: {
            type: 'number',
        },
        chapter_ids: {
            type: 'array',
        },
        knowledges: {
            type: 'array',
        },
        category_id: {
            type: 'number',
        },
        cyear: {
            type: 'number'
        },
        subject: {
            type: 'string',
        },
        sch_name: {
            type: 'string',
        },
        type_score: {
            type: 'array'
        },
        sch_short_name: {
            type: 'string',
        },
        score: {
            type: 'number',
        },
        blocks: {
            type: 'array',
        },
        from: {
            type: 'string',
            default: 'kbp_new'
        },
        user_name: { type: 'string' },
        user_id: { type: 'number' },
        dbs: { type: 'array' },
    }
};

const exampaperUnitSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        name: {
            type: 'string',
        },
        grade: {
            type: 'string',
        },
        grade_semester: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        press_version: {
            type: 'string',
        },
        album_type: {
            type: 'string'
        },
        difficulty_proportion: {
            type: 'array'
        },
        chapter_id: {
            type: 'number',
        },
        chapter_ids: {
            type: 'array',
        },
        knowledges: {
            type: 'array',
        },
        category_id: {
            type: 'number',
        },
        cyear: {
            type: 'number'
        },
        subject: {
            type: 'string',
        },
        type_score: {
            type: 'array'
        },
        score: {
            type: 'number',
        },
        blocks: {
            type: 'array',
        },
        from: {
            type: 'string',
            default: 'kbp_new'
        },
        dbs: {
            type: 'array',
        }
    }
};
// 用于校验 exampaper 基础结构 json schema
const exampaperSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        name: {
            type: 'string',
        },
        region: {
            type: 'string',
        },
        from_year: {
            type: 'integer',
        },
        to_year: {
            type: 'integer',
        },
        grade: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        press_version: {
            type: 'string',
        },
        subject: {
            type: 'string',
        },
        type: {
            type: 'string',
        },
        province: {
            type: 'string',
        },
        city: {
            type: 'string',
        },
        provinces: {
            type: 'array',
        },
        sch_name: {
            type: 'string',
        },
        type_score: {
            type: 'array'
        },
        sch_short_name: {
            type: 'string',
        },
        score: {
            type: 'number',
        },
        blocks: {
            type: 'array',
        },
        from: {
            type: 'string',
            default: 'mkp'
        },
        user_name: { type: 'string' },
        user_id: { type: 'number' },
        school_id: { type: 'number' },
        book_name: { type: 'string' },
        chapter_name: { type: 'string' },
        source_url: { type: 'string' },
        analysis_url: { type: 'string' },
        vague_name: { type: 'string' },
        dbs: { type: 'array' },

    }
};
// 用于校验 exampaper 试题块 json schema
const exampaperBlocksSchema = {
    type: 'array',
    additionalProperties: false,
    items: {
        type: 'object',
        properties: {
            title: {
                type: 'string',
            },
            note: {
                type: 'string',
            },
            score: {
                type: 'number',
            },
            type: {
                type: 'string',
            },
            questions: {
                type: 'array',
                items: {
                    type: 'object'
                }
            }
        }
    }
};

module.exports = {
    createKbExampaperSchema,
    createHfsExampaperSchema,
    exampaperSchema,
    createExampaperUnitSchema: createExampaperUnitSchema,
    exampaperBlocksSchema: exampaperBlocksSchema,
    exampaperUnitSchema: exampaperUnitSchema
};
