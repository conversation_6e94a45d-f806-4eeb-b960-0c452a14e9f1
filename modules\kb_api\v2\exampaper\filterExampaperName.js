// 试卷名字替换
const filterExampaperName = function (exampaper) {
    // 试卷名字替换
    let today_time = new Date();
    let vague_name = exampaper.vague_name || ''
    let paper_from = exampaper.from || '';
    if (paper_from != 'mkp') {
        vague_name = exampaper.name || '';
    } else {
        let paper_ctime = exampaper.ctime || null;
        let from_year = exampaper.from_year || null;
        if (paper_ctime) {
            let start_month = (today_time - paper_ctime)/(30*24*60*60*1000);
            if (start_month > 9) {
                vague_name = exampaper.name || '';
            }
        } else if (from_year){
            if (today_time.getFullYear() > from_year + 1){
                vague_name = exampaper.name || '';
            }
        }
    }
    return vague_name
};

module.exports = filterExampaperName;