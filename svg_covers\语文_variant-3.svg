<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
    <!-- 阴影效果定义 -->
    <defs>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.1)"/>
            <feDropShadow dx="0" dy="5" stdDeviation="5" flood-color="rgba(0,0,0,0.1)"/>
        </filter>
    </defs>
    
    
    <defs>
        <linearGradient id="grad_语文_3" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#780000;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#c1121f;stop-opacity:1" />
        </linearGradient>
    </defs>
    
    <!-- 主背景（整个封面使用同一渐变） -->
    <rect width="120" height="150" fill="url(#grad_语文_3)" rx="6" style="filter: url(#shadow);"/>
    
    <!-- 装饰图案 -->
    
                    <defs>
                        <radialGradient id="chineseRadial3" cx="50%" cy="50%">
                            <stop offset="0%" style="stop-color:rgba(255,255,255,0.2);stop-opacity:1" />
                            <stop offset="70%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
                        </radialGradient>
                    </defs>
                    <ellipse cx="60" cy="75" rx="55" ry="55" fill="url(#chineseRadial3)" transform="rotate(20 60 75)"/>
                
    
    <!-- 头部分隔线（从左到右完整覆盖） -->
    <line x1="0" y1="18" x2="120" y2="18" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
    
    <!-- 标题文字 -->
    <text x="60" y="12" font-family="Noto Sans SC, Helvetica Neue, Arial, sans-serif" 
          font-size="9" font-weight="500" fill="white" text-anchor="middle" 
          letter-spacing="0.5px" style="text-transform: uppercase;">语文 | CHINESE</text>
    
    <!-- 外边框 -->
    <rect width="120" height="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5" rx="6"/>
</svg>