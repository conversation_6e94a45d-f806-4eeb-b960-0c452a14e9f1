let Thenjs = require('thenjs');
let Logger = require('../../common/utils/logger');
let ResponseWrapper = require('../../common/utils/response_wrapper');
let params_utils = require('../../common/utils/params.js');
let mongodber = require('../../common/utils/mongodber');
let db = mongodber.use('KB');
let counter = require('../../common/utils/counter');
let rediser = require('../../common/utils/rediser');
let kb_api = require('../config.js').kb_api['v2'];
let _ = require('underscore');
let imageUtils = require('./image_utils');
const questionMobile = require('./question/question_mobile');
const { filterKnowledge } = require('../v2/question/utils/filterData');
const utils = require('../../common/utils/utils');

/*
 * 从card_contents获取question id
 */
function getQuestionId(obj) {
    if (!obj) {
        return [];
    }
    if (Array.isArray(obj.questions)) {
        return obj.questions;
    }
    let retval = [];
    for (let ix in obj) {
        if (typeof obj === 'object' && obj !== null) {
            retval.push(getQuestionId(obj[ix]));
        }
    }
    return retval;
}

function getQuestionParentsObject(obj) {
    let retval = [];
    if (!obj) {
        return retval;
    }
    if (Array.isArray(obj.questions)) {
        retval.push(obj);
    }
    for (let ix in obj) {
        let instance = obj[ix];
        if (obj instanceof Object) {
            retval.push(getQuestionParentsObject(instance));
        }
    }
    return _.flatten(retval);
}

function loadQuestions(knowledge, questions) {
    let obj = getQuestionParentsObject(knowledge.card_contents);
    obj = obj.concat(getQuestionParentsObject(knowledge.dimensions));
    obj = obj.concat(getQuestionParentsObject(knowledge.cognitions));
    obj = obj.concat(getQuestionParentsObject(knowledge.card_contents_b));
    obj = obj.concat(getQuestionParentsObject(knowledge.dimensions_b));
    obj = obj.concat(getQuestionParentsObject(knowledge.cognitions_b));
    obj = obj.concat(getQuestionParentsObject(knowledge.make_crises_b));
    for (let instance of obj) {
        instance.questions = _.filter(_.map(instance.questions, function (id) {
            return questions[id];
        }), function (x) { return !!x; });
    }
}

/*
 * 函数描述:
 * 		基于知识点id，请求知识点内容信息
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/knowledges/{knowledge_id}/
 * Method:
 * 		GET
 * Author:
 * 		zhangjun
 * Date:
 * 		2016-08-18
 */
async function knowledge(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        params = params_utils.create_params(req, kb_api['knowledge']);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    let device = params['device'];
    // let knowledgeColl = ('mobile' === device) ? 'knowledge_mobile' : 'knowledge';
    // let questionColl = ('mobile' === device) ? 'question_mobile' : 'question';
    let knowledgeColl = 'knowledge';
    let questionColl = 'question';

    let proj = params['fields_type'];
    proj.score = 1;
    proj.chance = 1;
    proj.card_contents = 1;
    proj.dimensions = 1; // 素养
    proj.cognitions = 1; // 认知
    proj.make_crises = 1; // 溯危
    proj.pre_knowledges = 1;
    proj.next_knowledges = 1;
    proj.card_contents_b = 1;
    proj.dimensions_b = 1; // 素养
    proj.cognitions_b = 1; // 认知
    proj.make_crises_b = 1; // 溯危
    proj.freq_ques_type = 1; // 常考题型
    proj.knowledge_summary = 1; // 知识小结
    proj.dbs = 1;       // 权限

    const queryDbs = new Set((req.query.dbs || 'basic').split(','));
    let cond = { _id: parseInt(params['knowledge_id']) };
    try {
        let knowledge = await db.collection(knowledgeColl).findOne(cond, { fields: proj });
        if (!knowledge) {
            return responseWrapper.error('NULL_ERROR');
        }
        if (!(knowledge.dbs || ['basic']).some(item => queryDbs.has(item))) {
            return responseWrapper.error('NULL_ERROR');
        }
        knowledge['id'] = knowledge['_id'];
        delete knowledge['_id'];

        let qIds = _.flatten(getQuestionId(knowledge.card_contents));
        qIds = qIds.concat(_.flatten(getQuestionId(knowledge.dimensions)));
        qIds = qIds.concat(_.flatten(getQuestionId(knowledge.cognitions)));
        qIds = qIds.concat(_.flatten(getQuestionId(knowledge.make_crises)));
        qIds = qIds.concat(_.flatten(getQuestionId(knowledge.card_contents_b)));
        qIds = qIds.concat(_.flatten(getQuestionId(knowledge.dimensions_b || {})));
        qIds = qIds.concat(_.flatten(getQuestionId(knowledge.cognitions_b || {})));
        qIds = qIds.concat(_.flatten(getQuestionId(knowledge.make_crises_b || {})));

        let quesPro = { source_url: 0, has_modified: 0 };
        let questions = await db.collection(questionColl).find({ _id: { $in: qIds } }).project(quesPro).toArray();

        questions = _.object(_.pluck(questions, '_id'), questions);
        loadQuestions(knowledge, questions);
        return responseWrapper.succ(knowledge);
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }
}

/*
 * 函数描述:
 * 		请求全量知识点score, chance信息
 * 		该接口专门供hfs使用，调用频率1次/周
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/knowledges/score_chance/
 * Method:
 * 		GET
 * Author:
 * 		zhangjun
 * Date:
 * 		2017-02-24
 */
function knowledge_score_chance(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    // 数据查询 ----------------------------
    Thenjs(function (cont) {
        // 获取试题id列表
        let cache_key = params['cache_key'];
        rediser.get(cache_key, function (err, kns) {
            if (!kns) {
                let coll = db.collection('knowledge');
                // 查询条件
                let cond = {};
                // 提取出的字段
                let proj = { 'score': 1, 'chance': 1 };
                // 查询
                coll.find(cond).project(proj).toArray(function (err, items) {
                    if (err) {
                        return cont(err);
                    }
                    if (items.length > 0) {
                        rediser.set(cache_key, items, 60 * 60);
                        // 过滤出目标试题id
                        return cont(null, items);
                    }
                    return responseWrapper.error('NULL_ERROR');

                });
            } else {
                return cont(null, kns);
            }
        });
    }).then(function (cont, kns) {
        if (kns) {
            return responseWrapper.succ({ knowledges: kns });
        }
        return responseWrapper.error('NULL_ERROR');

    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

async function changeKnowledgeName(knowledgeId, name) {
    let cond = { '_id': knowledgeId };
    let options = { fields: { name: 1, period: 1, subject: 1 } };
    let doc = await db.collection('knowledge').findOne(cond, options);
    if (!doc) {
        return;
    }
    if (doc.name === name) {
        return;
    }
    let fields = {
        name: doc.name,
        period: doc.period,
        subject: doc.subject,
        map_knows: [{ id: knowledgeId, name: name }],
        utime: new Date(),
        ctime: new Date(),
    };
    db.collection('knowledge_map').insertOne(fields, function (err, result) {
        if (err) {
            Logger.error(err);
        }
    });
}

/*
 * 函数描述:
 * 		修改知识点内容
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/knowledges/{knowledge_id}/
 * Method:
 * 		PUT
 * Author:
 * 		zhangjun
 * Date:
 * 		2016-10-24
 */
async function modify_knowledge(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        params = params_utils.create_params(req, kb_api['modify_knowledge']);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    try {
        let cond = { '_id': params['knowledge_id'] };
        let fields = params['fields'];
        fields['has_modified'] = 1;
        fields['utime'] = new Date();
        if (fields.name) {
            await changeKnowledgeName(params['knowledge_id'], fields.name);
        }

        // 检查知识点素养、认知数据合法性
        if (fields.dimensions && fields.dimensions.tags && fields.dimensions.tags.length > 0) {
            let dTags = [];
            fields.dimensions.tags.forEach(t => {
                if ([1, 2, 3, 4, 5].includes(t.score)) {
                    dTags.push(t);
                }
            });
            fields.dimensions.tags = dTags;
        }
        if (fields.cognitions && fields.cognitions.tags && fields.cognitions.tags.length > 0) {
            let cTags = [];
            fields.cognitions.tags.forEach(c => {
                if ([1, 2, 3, 4, 5].includes(c.score)) {
                    cTags.push(c);
                }
            });
            fields.cognitions.tags = cTags;
        }
        if (fields.dimensions_b && fields.dimensions_b.tags && fields.dimensions_b.tags.length > 0) {
            let dTags = [];
            fields.dimensions_b.tags.forEach(t => {
                if ([1, 2, 3, 4, 5].includes(t.score)) {
                    dTags.push(t);
                }
            });
            fields.dimensions_b.tags = dTags;
        }
        if (fields.cognitions_b && fields.cognitions_b.tags && fields.cognitions_b.tags.length > 0) {
            let cTags = [];
            fields.cognitions_b.tags.forEach(c => {
                if ([1, 2, 3, 4, 5].includes(c.score)) {
                    cTags.push(c);
                }
            });
            fields.cognitions_b.tags = cTags;
        }
        fields = filterKnowledge(fields);
        let update_fields = { '$set': fields };
        let result = await db.collection('knowledge').updateOne(cond, update_fields);
        if (result.result.n !== 1) {
            return responseWrapper.error('PARAMETERS_ERROR', '查不到该条记录');
        }
        return responseWrapper.succ({});
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
}



/*
 * 函数描述:
 * 		新增一个知识点内容
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/knowledges/{knowledge_id}/
 * Method:
 * 		POST
 * Author:
 * 		zhangjun
 * Date:
 * 		2016-11-15
 */
function insert_knowledge(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    // 数据查询 ----------------------------
    let coll = db.collection('knowledge');
    let fields = params['fields'];
    Thenjs(function (cont) {
        let cond = {
            period: fields.period,
            subject: fields.subject,
            name: fields.name
        };
        coll.find(cond).hint({ name: 1 }).limit(1).toArray(function (err, exists) {
            if (err) {
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }
            if (exists.length)
                return responseWrapper.error('PARAMETERS_ERROR', '同步数据失败，该学段，学科下面已经存在同名知识点');
            cont(null);
        });
    }).then(function (cont,) {
        if (fields.id) {
            let _id = Number(fields.id);
            if (_id) {
                delete fields.id;
                return cont(null, [_id]);
            }
        }
        // 设置内容
        counter.getNextSequenceValue('knowledge', 1, function (err, seq) {
            if (err) {
                return cont(err);
            }
            cont(null, seq);
        });
    }).then(function (cont, seq) {
        seq = seq[0];
        fields['_id'] = seq;
        fields['ctime'] = new Date();
        fields['utime'] = new Date();
        fields['questions'] = [];
        fields = filterKnowledge(fields);
        // 更新
        coll.insertOne(fields, function (err, result) {
            if (err) {
                return cont(err);
            }
            // return responseWrapper.succ({ code: 0, msg: 'OK', data: { id: seq } });
            return responseWrapper.succ({ id: seq });
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

/*
 * 函数描述:
 * 		基于关键词模糊查询知识点信息
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/search/knowledges/{key_word}/
 * Method:
 * 		GET
 * Author:
 * 		zhangjun
 * Date:
 * 		2016-11-10
 */
function search_knowledges(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    let re = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
        re = new RegExp(params['key_word']);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    // 数据查询 ----------------------------
    Thenjs(function (cont) {
        let coll = db.collection('knowledge');
        // 查询条件
        let cond = { name: re };
        if (params['period']) {
            cond['period'] = params['period'];
        }
        // 设置内容
        let proj = {
            name: 1,
            period: 1,
            subject: 1,
            importance: 1,
            chance: 1,
            score: 1,
        };
        // 更新
        coll.find(cond).project(proj).limit(20).toArray(function (err, items) {
            if (err) {
                return cont(err);
            }
            let result = [];
            for (let i in items) {
                let item = items[i];
                result.push({
                    'id': item['_id'],
                    'name': item['name'],
                    'period': item['period'],
                    'subject': item['subject'],
                    'importance': item['importance'],
                    'chance': item['chance'],
                    'score': item['score'],
                });
            }
            return cont(null, result);
        });
    }).then(function (cont, result) {
        if (result && result.length > 0) {
            return responseWrapper.succ(result);
        }
        return responseWrapper.error('NULL_ERROR');

    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

function getKnowledgeByNames(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let names = decodeURIComponent(req.params.names);
    names = names.split(';');
    db.collection('knowledge').find({
        name: { $in: names }
    }, {
        name: 1,
        period: 1,
        subject: 1
    }).toArray(function (err, docs) {
        if (err) {
            Logger.error(err.message);
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
        let retlist = _.map(docs, function (doc) {
            doc.id = doc._id;
            delete doc._id;
            return doc;
        });
        return responseWrapper.succ(retlist);
    });
}

function getKnowledgeCardImage(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let knowledge_ids = req.params.knowledge_ids.split(',');
    Thenjs.each(knowledge_ids, function (cont, value) {
        value = Number(value);
        let urlString = `/utilbox_api/v2/knowledges/${value}/image`;
        imageUtils.getAndModifyImage(urlString, 'knowledge', 'card_img')(value, function (err, doc) {

            /*
             * Although the err can be described as not empty, but still can
             * not be ended in the form of an exception.
             */
            if (err) {
                doc = err;
            }

            cont(null, { id: value, url: doc });
        });
    }).then(function (cont, result) {
        result = _.filter(result, function (x) {
            return x.id && x.url;
        });
        return responseWrapper.succ(result);
    });
}

// 过滤出来主树
async function filterMaterTree(result) {
    let returnResult = result;
    const treeCache = 'kb_api:v2:knowledge_trees:is_master_tree';
    let masterTreeArr = [];
    try {
        masterTreeArr = await rediser.get(treeCache);
    } catch (error) {
        console.log(error);
    }
    if (!masterTreeArr || 0 === masterTreeArr.length) {
        const project = { period: 1, subject: 1, name: 1 };
        masterTreeArr = await db.collection('knowledge_tree').find({ is_master: 1 }).project(project).toArray();
    }
    if (Array.isArray(masterTreeArr) && masterTreeArr.length > 0) {
        rediser.set(treeCache, masterTreeArr, 60 * 30);
        returnResult = result.filter(item => {
            for (let tree of masterTreeArr) {
                let match = `${tree.period}_${tree.subject}_${tree.name}`;
                if (item.path.includes(match)) {
                    return true;
                }
            }
            return false;
        });
    }
    return returnResult;
}

async function getKnowledgeParent(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let knowledge_ids = req.params.knowledge_ids;
    let treeType = req.query.tree_type || '';
    let knowledgeIdArr = knowledge_ids.split(',', 50);
    knowledgeIdArr = knowledgeIdArr.map(function (kid) {
        return Number(kid);
    });
    let md5 = knowledge_ids;
    if (knowledgeIdArr.length > 2) {
        md5 = utils.getMd5(knowledge_ids);
    }
    const cache_key = 'kb_api:v2:knowledges:parent:' + treeType + md5;
    try {
        let cache_result = await rediser.get(cache_key);
        if (cache_result) {
            return responseWrapper.succ(cache_result);
        }
        let cond = { 'knowledges.id': { $in: knowledgeIdArr } };
        let options = { fields: { name: 1, path: 1, 'knowledges.id': 1, 'knowledges.name': 1 } };
        let result = await db.collection('knowledge_tree_chapter').find(cond, options).toArray();
        for (let i = 0; i < result.length; i++) {
            let newKnows = [];
            result[i].id = result[i]._id;
            delete result[i]._id;
            for (let j = 0; j < result[i].knowledges.length; j++) {
                if (knowledgeIdArr.includes(result[i].knowledges[j].id)) {
                    newKnows.push(result[i].knowledges[j]);
                }
            }
            result[i].knowledges = newKnows;
        }
        // ai 测评卷只要主树
        if ('is_master' === treeType) {
            result = await filterMaterTree(result);
        }
        rediser.set(cache_key, result, 60 * 30);
        return responseWrapper.succ(result);
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }
}

async function getKnowledgeForFuDao(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let limit = req.query.limit || 10;
    let star = req.query.star || 5;
    let cond = { human_importance: { $gte: Number(star) } };

    let subjects = {
        '小学': ['语文', '数学', '英语'],
        '初中': ['语文', '数学', '英语', '物理', '化学', '生物', '政治', '历史', '地理'],
        '高中': ['语文', '数学', '英语', '物理', '化学', '生物', '政治', '历史', '地理'],
    };

    if (!req.query.period || !subjects[req.query.period]) {
        return responseWrapper.error('PARAMETERS_ERROR', '学段需要有');
    }
    cond['period'] = req.query.period;
    let condSubjects = subjects[req.query.period];
    if (req.query.subject) {
        condSubjects = [req.query.subject];
    }
    let proj = { period: 1, subject: 1, human_importance: 1, name: 1 };
    let result = [];
    try {
        for (let subject of condSubjects) {
            cond['subject'] = subject;
            let knows = await db.collection('knowledge').find(cond).project(proj).limit(Number(limit)).toArray();
            knows.forEach(k => {
                k.id = k._id;
                delete k._id;
            });
            result.push(knows);
        }
        return responseWrapper.succ(result);
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }
}

/**
 * 验证这个学科，学段下面，该名称知识点是否存在
 * create by hanmingming
 * date 2018-5-13
 */
function checkKnowledgeExits(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let keys = ['name', 'period', 'subject'];
    let body = req.query;
    let cond = {};
    for (let key of keys) {
        if (!body[key]) {
            return responseWrapper.error('PARAMETERS_ERROR', key + '字段必须');
        }
        cond[key] = body[key];
    }
    if (req.query.id) {
        cond._id = { $ne: Number(req.query.id) };
    }
    let coll = db.collection('knowledge');
    return coll.find(cond).hint({ name: 1 }).limit(1).toArray(function (err, exists) {
        if (err) {
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
        responseWrapper.succ({
            status: !!exists.length
        });
    });
}

function deleteKnowledge(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let knowledgeId = req.params.knowledge_id;
        knowledgeId = knowledgeId * 1;
        db.collection('stash').insert({
            type: 'knowledge',
            id: knowledgeId,
            ctime: new Date()
        }, function (err, writeResult) {
            if (err) {
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }
            return responseWrapper.succ({});
        });
    } catch (err) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }
}

/**
 * 批量获取知识点
 * @param {*} req
 * @param {*} res
 * @returns {*}
 */
async function batch_get_knowledge(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    if (!params['ids']) {
        return responseWrapper.error('PARAMETERS_ERROR', 'ids不能为空');
    }

    let kids = params['ids'].split(',');
    let device = params['device'];
    let filter_fields = req.query.filter_fields || '';
    let proj = {};
    if (filter_fields) {
        filter_fields = filter_fields.split(',');
        for (const field of filter_fields) {
            proj[field] = 1;
        }
    } else {
        proj = params['fields_type'];
    }
    proj['dbs'] = 1;

    try {
        kids = kids.map(id => parseInt(id));
        let cond = { _id: { $in: kids } };

        // let knowledgeColl = ('mobile' === device) ? 'knowledge_mobile' : 'knowledge';
        // let questionColl = ('mobile' === device) ? 'question_mobile' : 'question';
        let knowledgeColl = 'knowledge';
        let questionColl = 'question';

        let knowledges = await db.collection(knowledgeColl).find(cond).project(proj).toArray();
        if (0 === knowledges.length) {
            return responseWrapper.error('NULL_ERROR');
        }
        // 知识点 权限过滤
        const queryDbs = new Set((req.query.dbs || 'basic').split(','));
        knowledges = knowledges.filter(knowledge => (knowledge.dbs || ['basic']).some(item => queryDbs.has(item)));
        if (knowledges.length === 0) return responseWrapper.error('NULL_ERROR', '未找到知识点');

        for (let item of knowledges) {
            item['id'] = item['_id'];
            delete item['_id'];
        }

        let questionIds = [];
        for (let item of knowledges) {
            let _questionIds = _.flatten(getQuestionId(item.card_contents));
            _questionIds = _questionIds.concat(_.flatten(getQuestionId(item.dimensions)));
            _questionIds = _questionIds.concat(_.flatten(getQuestionId(item.cognitions)));
            _questionIds = _questionIds.concat(_.flatten(getQuestionId(item.make_crises)));
            _questionIds = _questionIds.concat(_.flatten(getQuestionId(item.card_contents_b)));
            _questionIds = _questionIds.concat(_.flatten(getQuestionId(item.dimensions_b || {})));
            _questionIds = _questionIds.concat(_.flatten(getQuestionId(item.cognitions_b || {})));
            _questionIds = _questionIds.concat(_.flatten(getQuestionId(item.make_crises_b || {})));
            questionIds.push(..._questionIds);
        }
        let quesPro = { source_url: 0, has_modified: 0 };
        let questions = await db.collection(questionColl).find({ _id: { $in: questionIds } }).project(quesPro).toArray();
        // 试题 权限过滤
        questions = questions.filter(question => (question.dbs || ['basic']).some(item => queryDbs.has(item)));
        questions = _.object(_.pluck(questions, '_id'), questions);
        for (let knowledge of knowledges) {
            loadQuestions(knowledge, questions);
        }
        return responseWrapper.succ(knowledges);
    } catch (err) {
        return responseWrapper.error('HANLDE_ERROR', err.message);
    }

}

const getKnowledgesQuestions = async (req, res) => {
    if (!req.params.knowledge_ids) {
        req.params.knowledge_ids = req.params.knowledge_id;
    }
    const responseWrapper = new ResponseWrapper(res);
    const retval = {
        total_num: 0,
        questions: []
    };
    try {
        let offset = req.query.offset * 1 || 0;
        let limit = req.query.limit * 1 || 10;
        let knowledgeIds = _.map(req.params.knowledge_ids.split(','), (x) => x * 1);
        let type = null;
        if (req.query.type) {
            type = req.query.type;
        }
        let cond = { _id: { $in: knowledgeIds } };
        let proj = { 'questions.id': 1, 'questions.type': 1 };
        let questionArrays = null;
        try {
            questionArrays = await db.collection('knowledge').find(cond).project(proj).toArray();
        } catch (err) {
            throw err;
        }
        // 当没有任何数据的情况，会直接结束运行
        if (!Array.isArray(questionArrays) || questionArrays.length === 0) {
            return responseWrapper.succ(retval);
        }
        // 知识点 权限过滤
        const queryDbs = new Set((req.query.dbs || 'basic').split(','));
        questionArrays = questionArrays.filter(knowledge => (knowledge.dbs || ['basic']).some(item => queryDbs.has(item)));
        if (questionArrays.length === 0) {
            return responseWrapper.succ(retval);
        }

        // go on
        questionArrays = _.flatten(_.pluck(questionArrays, 'questions'));
        questionArrays = _.filter(questionArrays, (x) => {
            let retval = true;
            retval = retval && !!x;
            if (type !== null) {
                retval = retval && x.type === type;
            }
            return retval;
        });
        retval.total_num = questionArrays.length;
        questionArrays = questionArrays.slice(offset, offset + limit);
        // local videos
        let questions = null;
        try {
            questions = await db.collection('question').find({
                _id: { $in: _.pluck(questionArrays, 'id') }
            }).project({ source_url: 0 }).toArray();
        } catch (err) {
            Logger.error(err.stack);
            retval.total_num = 0;
            return responseWrapper.succ(retval);
        }
        _.each(questions, (x) => {
            x.id = x._id;
            delete x._id;
        });
        retval.questions = questions;
        // questionMobile.updateQuestionMobileByQuesion(questions, req.query.device);
        return responseWrapper.succ(retval);
    } catch (err) {
        Logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getKnowledgesQuestionsSamples = async (req, res) => {
    if (!req.params.knowledge_ids) {
        req.params.knowledge_ids = req.params.knowledge_id;
    }
    const responseWrapper = new ResponseWrapper(res);
    const retval = {
        total_num: 0,
        questions: []
    };
    try {
        let knowledgeIds = null;
        let type = null;
        if (req.query.type) {
            type = req.query.type;
        }
        let questionArrays = [];
        let count = 5;
        while (questionArrays.length < 5 && --count >= 0) {
            knowledgeIds = _.map(req.params.knowledge_ids.split(','), (x) => x * 1);
            let questionsTmp = questionArrays;
            knowledgeIds = _.sample(knowledgeIds, 3);
            let cond = { _id: { $in: knowledgeIds } };
            let proj = { 'questions.id': 1, 'questions.type': 1, 'questions': { $slice: 400 } };
            try {
                questionArrays = await db.collection('knowledge').find(cond).project(proj).toArray();
            } catch (err) {
                throw err;
            }
            // 当没有任何数据的情况，会直接结束运行
            if (!Array.isArray(questionArrays) || questionArrays.length === 0) {
                return responseWrapper.succ(retval);
            }
            // go on
            questionArrays = _.flatten(_.pluck(questionArrays, 'questions'));
            questionArrays = _.filter(questionArrays, (x) => {
                let retval = true;
                retval = retval && !!x;
                if (type !== null) {
                    retval = retval && x.type === type;
                }
                return retval;
            });
            questionsTmp = questionsTmp.concat(questionArrays);
            questionArrays = _.uniq(questionsTmp, x => x.id);
            questionArrays = _.sample(questionArrays, 5);
        }
        console.log(count)
        let questions = null;
        try {
            questions = await db.collection('question').find({
                _id: { $in: _.pluck(questionArrays, 'id') }
            }).project({ source_url: 0 }).toArray();
        } catch (err) {
            Logger.error(err.stack);
            retval.total_num = 0;
            return responseWrapper.succ(retval);
        }
        _.each(questions, (x) => {
            x.id = x._id;
            delete x._id;
        });
        retval.questions = questions;
        return responseWrapper.succ(retval.questions);
    } catch (err) {
        Logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getKnowledgesQuestionsCount = async (req, res) => {
    if (!req.params.knowledge_ids) {
        req.params.knowledge_ids = req.params.knowledge_id;
    }
    const responseWrapper = new ResponseWrapper(res);
    try {
        let knowledgeIds = _.map(req.params.knowledge_ids.split(','), (x) => x * 1);
        let cond = { _id: { $in: knowledgeIds } };
        let proj = { 'questions.type': 1, 'questions': { $slice: 200 } };
        let questionArrays = null;
        try {
            questionArrays = await db.collection('knowledge').find(cond).project(proj).toArray();
        } catch (err) {
            throw err;
        }
        // 当没有任何数据的情况，会直接结束运行
        if (!Array.isArray(questionArrays) || questionArrays.length === 0) {
            return responseWrapper.succ({});
        }
        // go on
        questionArrays = _.flatten(_.pluck(questionArrays, 'questions'));
        questionArrays = _.filter(questionArrays, (x) => !!x);
        questionArrays = _.sample(questionArrays, 500);
        questionArrays = _.countBy(questionArrays, function (question) {
            return question.type;
        });
        questionArrays = _.map(_.pairs(questionArrays), function (x) {
            return { name: x[0], value: x[1] };
        });
        return responseWrapper.succ(questionArrays);
    } catch (err) {
        Logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};


const getKnowledgesVideosDetails = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    const retval = {
        total_num: 0,
        videos: []
    };
    try {
        let offset = req.query.offset * 1 || 0;
        let limit = req.query.limit * 1 || 10;
        let knowledgeIds = _.map(req.params.knowledge_ids.split(','), (x) => x * 1);
        let cond = { _id: { $in: knowledgeIds } };
        let proj = { videos: 1, dbs: 1 };
        let videoArrays = await db.collection('knowledge').find(cond).project(proj).toArray();
        if (!Array.isArray(videoArrays) || videoArrays.length === 0) return responseWrapper.succ(retval);
        // 知识点 权限过滤
        const queryDbs = new Set((req.query.dbs || 'basic').split(','));
        videoArrays = videoArrays.filter(item => (item.dbs || ['basic']).some(item => queryDbs.has(item)));
        if (videoArrays.length === 0) return responseWrapper.error('HANDLE_ERROR', '未找到知识点');

        videoArrays = _.flatten(_.pluck(videoArrays, 'videos'));
        videoArrays = _.filter(videoArrays, (x) => !!x);
        videoArrays = _.uniq(videoArrays, x => x.id);
        let videoProj = { _id: 1, name: 1, is_out_show: 1, user: 1, tags: 1, utime: 1, dbs: 1 };
        let videoCond = { _id: { $in: _.pluck(videoArrays, 'id') } };
        let tagsand = [];
        if (req.query.tags) {
            let tags = JSON.parse(req.query.tags);
            for (let tag of tags) {
                let tagand = [];
                for (let val of tag.values) {
                    tagand.push(val.name);
                }
                let tagscond = {
                    tags: {
                        $elemMatch: {
                            id: tag.id,
                            'values.name': {
                                $in: tagand
                            }
                        }
                    }
                };
                tagsand.push(tagscond);
            }
        }
        if (tagsand.length !== 0) cond['$and'] = tagsand;
        let videos = await db.collection('video').find(videoCond).project(videoProj).toArray();
        // 视频 权限过滤
        videos = videos.filter(video => (video.dbs || ['basic']).some(item => queryDbs.has(item)));
        if (videos.length === 0) return responseWrapper.error('HANDLE_ERROR', '未找到视频');
        let videoCateCond = { 'videos.id': { $in: _.map(videos, o => o._id) } };
        let videoCateProj = { type: 1, videos: 1 };
        let videoCategorys = await db.collection('video_category').find(videoCateCond).project(videoCateProj).toArray();
        let retVideos = [];
        _.each(videos, (x) => {
            x.id = x._id;
            delete x._id;
            x.user_name = x.user && x.user.name || '';
            delete x.user;
            x.is_out_show = x.is_out_show || false;
            x.is_relate = true;
            delete x.tags;
            videoCategorys.forEach(vc => {
                if (_.map(vc.videos, o => o.id).includes(x.id)) {
                    x.category = vc.type || '';
                    retVideos.push(x);
                }
            });
        });
        retval.total_num = retVideos.length;
        retval.videos = retVideos.slice(offset, offset + limit);
        let outs = [], unouts = [];
        retval.videos.forEach(v => {
            if (v.is_out_show) outs.push(v);
            else unouts.push(v);
        });
        outs.sort(function (a, b) { return b.utime - a.utime; });
        retval.videos = outs.concat(unouts);

        let retvalVideo = {}
        for (let j = 0; j < retval.videos.length; j++) {
            let rvideoId = retval.videos[j]['id'];
            retvalVideo[rvideoId] = retval.videos[j];
        }
        let resultVideos = []
        if (videoArrays && retval.videos.length != videoArrays.length) {
            for (let i = 0; i < videoArrays.length; i++) {
                let oneVideo = videoArrays[i];
                let videoId = oneVideo.id || '';
                let videoName = oneVideo.name || '';
                let otherVideo;
                if (retvalVideo && retvalVideo.hasOwnProperty(videoId)) {
                    otherVideo = retvalVideo[videoId];
                } else {
                    otherVideo = {
                        category: '',
                        id: videoId,
                        is_out_show: false,
                        is_relate: true,
                        name: videoName,
                        user_name: "",
                        utime: new Date("2023-01-01 00:00:00")
                    };
                }
                resultVideos.push(otherVideo);
            }
        }
        retval.videos = resultVideos;
        retval.total_num = resultVideos.length;

        return responseWrapper.succ(retval);
    } catch (err) {
        Logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const updateKnowledgesVideos = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    try {
        if (!req.params.knowledge_id
            || !req.params.video_id
            || !['cancel_relate', 'out_show', 'cancel_out_show'].includes(req.body.type)) {
            return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
        }
        let knowCond = { _id: Number(req.params.knowledge_id) };
        let knowProj = { videos: 1 };
        let knowledges = await db.collection('knowledge').find(knowCond).project(knowProj).toArray();
        if (_.isEmpty(knowledges)) {
            return responseWrapper.error('HANDLE_ERROR', '知识点数据为空！');
        }
        let videoCond = { _id: { $in: _.map(knowledges[0].videos, o => o.id) } };
        let videoProj = { is_out_show: 1 };
        let videos = await db.collection('video').find(videoCond).project(videoProj).toArray();
        let outShowCount = 0;
        videos.forEach(v => {
            outShowCount += v.is_out_show ? 1 : 0;
        });
        if (outShowCount >= 5 && req.body.type === 'out_show') {
            return responseWrapper.error('HANDLE_ERROR', '该知识点已有5个外显视频，请取消当前外显后再次选择。');
        }
        if (req.body.type === 'cancel_relate') {
            // knowledge: remove video_id
            let newVideos = [];
            knowledges[0].videos.forEach(v => {
                if (v.id !== +req.params.video_id) newVideos.push(v);
            });
            await db.collection('knowledge').updateOne({ _id: Number(req.params.knowledge_id) }, { $set: { videos: newVideos } });

            // video: remove knowledge_id
            let video = await db.collection('video').findOne({ _id: Number(req.params.video_id) }, { fields: { knowledges: 1 } });
            if (video && video.knowledges) {
                let newKnows = [];
                video.knowledges.forEach(k => {
                    if (k.id !== +req.params.knowledge_id) newKnows.push(k);
                });
                await db.collection('video').updateOne({ _id: Number(req.params.video_id) }, { $set: { knowledges: newKnows } });
            }
        } else if (req.body.type === 'out_show') {
            await db.collection('video').updateOne({ _id: Number(req.params.video_id) }, { $set: { is_out_show: true, utime: new Date() } });
        } else if (req.body.type === 'cancel_out_show') {
            await db.collection('video').updateOne({ _id: Number(req.params.video_id) }, { $set: { is_out_show: false } });
        }
        return responseWrapper.succ({});
    } catch (err) {
        Logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getKnowledgesVideosRelaton = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    try {
        let knowledgeIds = _.map(req.params.knowledge_ids.split(','), (x) => x * 1);
        let cond = { _id: { $in: knowledgeIds } };
        let proj = { videos: 1, dbs: 1 };
        let videoArrays = null;
        try {
            videoArrays = await db.collection('knowledge').find(cond).project(proj).toArray();
        } catch (err) {
            throw err;
        }
        // 知识点 权限过滤
        const queryDbs = new Set((req.query.dbs || 'basic').split(','));
        videoArrays = videoArrays.filter(video => (video.dbs || ['basic']).some(item => queryDbs.has(item)));
        if (videoArrays.length === 0) return responseWrapper.error('HANDLE_ERROR', '未找到知识点');
        videoArrays.forEach(k => {
            k.id = k._id;
            k.is_relate_video = !_.isEmpty(k.videos) ? true : false;
            delete k._id;
            delete k.videos;
            delete k.dbs;
        });
        return responseWrapper.succ(videoArrays || []);
    } catch (err) {
        Logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const updatePreNextKnowledge = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    let saveData = req.body;
    if (!Array.isArray(saveData.pre_knowledges) && !Array.isArray(saveData.next_knowledges)) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }

    let knowId = req.params.knowledge_id;
    try {
        let ret = await db.collection('knowledge').updateOne({ _id: Number(knowId) }, { $set: saveData });
        if (1 !== ret.result.ok) {
            return responseWrapper.error('HANDLE_ERROR', 'update knowledge faild!');
        }
    } catch (error) {
        Logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }
    return responseWrapper.succ({ id: knowId });
};

const getKnowledgesVideos = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    const retval = {
        total_num: 0,
        videos: []
    };
    try {
        let offset = req.query.offset * 1 || 0;
        let limit = req.query.limit * 1 || 10;
        let knowledgeIds = _.map(req.params.knowledge_ids.split(','), (x) => x * 1);
        let cond = { _id: { $in: knowledgeIds } };
        let proj = { videos: 1, card_contents: 1, name: 1, dbs: 1 };
        let videoArrays = null;
        try {
            videoArrays = await db.collection('knowledge').find(cond).project(proj).toArray();
        } catch (err) {
            throw err;
        }
        // 当没有任何数据的情况，会直接结束运行
        if (!Array.isArray(videoArrays) || videoArrays.length === 0) {
            return responseWrapper.succ(retval);
        }
        // 知识点 权限过滤
        const queryDbs = new Set((req.query.dbs || 'basic').split(','));
        videoArrays = videoArrays.filter(video => (video.dbs || ['basic']).some(item => queryDbs.has(item)));
        if (videoArrays.length === 0) return responseWrapper.error('HANDLE_ERROR', '未找到知识点')

        // go on
        let knowName = videoArrays[0].name || '';
        let cardContents = videoArrays[0].card_contents || {};
        videoArrays = _.flatten(_.pluck(videoArrays, 'videos'));
        videoArrays = _.filter(videoArrays, (x) => !!x);
        videoArrays = _.uniq(videoArrays, x => x.id);
        retval.total_num = videoArrays.length;
        videoArrays = videoArrays.slice(offset, offset + limit);
        let all_videos = [];
        if (req.query.all_video && req.query.all_video == '1') {
            for (let i = 0; i < videoArrays.length; i++) {
                // 相似度过滤
                let type_ = videoArrays[i].type || 'history_save';
                let similarity = videoArrays[i].similarity || 0.8;
                if (type_ == 'history_save') {
                    all_videos.push(videoArrays[i]);
                } else if (similarity >= 0.8) {
                    all_videos.push(videoArrays[i]);
                }
            }
        }
        // local videos
        let videos = null;
        try {
            videos = await db.collection('video').find({
                _id: { $in: _.pluck(videoArrays, 'id') }
            }).project({ _id: 1, name: 1, data_url: 1, }).toArray();
        } catch (err) {
            Logger.error(err.stack);
            retval.total_num = 0;
            return responseWrapper.succ(retval);
        }
        _.each(videos, (x) => {
            x.id = x._id;
            delete x._id;
            x.url = x.data_url;
            delete x.data_url;
        });
        retval.total_num = videos.length;
        retval.videos = videos;
        if (req.query.all_video && req.query.all_video == '1') {
            retval.know_name = knowName;
            retval.videos = all_videos;
            retval.total_num = all_videos.length;
            retval.card_contents = cardContents;
        }
        return responseWrapper.succ(retval);
    } catch (err) {
        Logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

// 获取批量知识点的考法和对象
const getBatchRelation = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    let ids = req.body.ids;
    if (!Array.isArray(ids)) {
        return responseWrapper.error('PARAMETERS_ERROR', '请传知识点数组');
    }
    if (ids.length === 0) {
        return responseWrapper.succ([]);
    }
    let cond = { _id: { $in: ids } };
    let proj = { name: 1, targets: 1, know_methods: 1 };
    try {
        let retval = await db.collection('knowledge').find(cond).project(proj).toArray();
        if (retval.length > 0) {
            for (let i = 0; i < retval.length; i++) {
                retval[i].id = retval[i]._id;
                delete retval[i]._id;
            }
        }
        return responseWrapper.succ(retval);
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    knowledge: knowledge,
    knowledge_score_chance: knowledge_score_chance,
    insert_knowledge: insert_knowledge,
    modify_knowledge: modify_knowledge,
    search_knowledges: search_knowledges,
    getKnowledgeByNames: getKnowledgeByNames,
    getKnowledgeCardImage: getKnowledgeCardImage,
    getKnowledgeForFuDao: getKnowledgeForFuDao,
    deleteKnowledge: deleteKnowledge,
    checkKnowledgeExits: checkKnowledgeExits,
    batch_get_knowledge: batch_get_knowledge,
    getKnowledgesVideos: getKnowledgesVideos,
    getKnowledgesVideosRelaton: getKnowledgesVideosRelaton,
    updatePreNextKnowledge: updatePreNextKnowledge,
    getKnowledgesVideosDetails: getKnowledgesVideosDetails,
    updateKnowledgesVideos: updateKnowledgesVideos,
    getKnowledgesQuestions: getKnowledgesQuestions,
    getKnowledgesQuestionsCount: getKnowledgesQuestionsCount,
    getKnowledgesQuestionsSamples: getKnowledgesQuestionsSamples,
    getBatchRelation,
    getKnowledgeParent,
};
