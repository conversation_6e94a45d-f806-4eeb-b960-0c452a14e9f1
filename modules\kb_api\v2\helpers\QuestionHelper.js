let Logger = require('../../../common/utils/logger');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const counter = require('../../../common/utils/counter');
const _ = require('lodash');


const addRecoQuestions = async (id, questionIds = []) => {
    if (!questionIds) {
        throw new Error('questionIds不能为空');
    }
    if (!questionIds.length) {
        return;
    }
    await db.collection('question').updateOne({_id: id}, {$set: {reco_questions: questionIds}});
};


//大题小题的core_knowledges，id,name必填，'chance','score'没有为空
function compatible_core_knowledges(kns) {
    let keys = ['id', 'name'];
    let knowledgeKeys = ['id', 'name', 'chance', 'score'];
    for (let i in kns) {
        let kn = kns[i];
        kn.chance = kn.chance * 1 || 0;
        kn.score = kn.score * 1 || 0;
        for (let k in kn) {
            //去掉多余字段
            if (knowledgeKeys.indexOf(k) == -1) {
                delete kn[k];
                continue;
            }
            //必填字段
            if (keys.indexOf(k) == -1) {
                //throw ('core_knowledges.'+k+'必填字段');
            }
        }
    }
}

/**
 * 试题结构变化 兼容返回数据
 * 下面三个新增字段
 *      blocks.knowledges
 *      blocks.core_knowledges
 *      blocks.comments
 * @param {*} ques 试题数组
 * @return {*}
 * @private
 */
function _compatible_question(ques) {
    if (!Array.isArray(ques)) {
        return new Error('compatible_question要求试题结构是数组');
    }
    //blocks 兼容 'knowledges', 'core_knowledges'
    let compatible = ['knowledges', 'core_knowledges'];
    let err_message = null;
    try {
        for (let fields of ques) {
            for (let ckey of compatible) {
                let blks = fields['blocks'];
                if (!blks[ckey]) {
                    blks[ckey] = _.map(blks.stems, () => fields[ckey] || []);
                    //大题小题的core_knowledges，id,name必填，'chance','score'没有为空

                } else {
                    if (!Array.isArray(blks[ckey])) {
                        Logger.error(fields._id + '试题结构blocks.' + ckey + '异常');
                        err_message = new Error('blocks.' + ckey + '应该是个数组');
                    }
                    for (let ks of blks[ckey]) {
                        compatible_core_knowledges(ks);
                    }
                }

                blks[ckey] = blks[ckey].map(u => u && Array.isArray(u) ? u : []);
                delete blks.comments;
                _compatible_question(fields[ckey] || []);
                //选择题：‘’，多选题：[''],填空题：[''],解答题：''
                for (let i = 0; i < blks.types.length; i++) {
                    let t_type = blks.types[i];
                    let ans = blks.answers[i] || '';
                    if (['选择题', '解答题'].indexOf(t_type) != -1) {
                        if (Array.isArray(ans)) {
                            blks.answers[i] = ans.join(' ');
                        }
                    }
                    if (['多选题', '填空题'].indexOf(t_type) != -1) {
                        if (typeof (ans) == 'string') {
                            blks.answers[i] = [ans];
                        }
                    }
                }
            }

        }
    } catch (e) {
        return e;
    }
    return err_message;
}

const _removeStyle = function (str) {
    str = str.replace(/font-size:\w+;?/g, '');
    str = str.replace(/font-family:\w+;?/g, '');
    return str;
};

const _removeQuestionStyle = function (question) {
    question.comment = _removeStyle(question.comment);
    question.description = _removeStyle(question.description);

    let blocks = question.blocks;
    let stems = blocks.stems || [];
    for (let i = 0; i < stems.length; i++) {
        stems[i].stem = _removeStyle(stems[i].stem);
        if (stems[i].stem.options) {
            for (let key in stems[i].stem.options) {
                stems[i].stem.options[key] = _removeStyle(stems[i].stem.options[key]);
            }
        }
    }
    // 替换答案
    let answers = blocks.answers || [];
    for (let i = 0; i < answers.length; i++) {
        if (answers[i] instanceof Array) {
            for (let j = 0; j < answers[i].length; j++) {
                answers[i][j] = _removeStyle(answers[i][j]);
            }
        } else {
            answers[i] = _removeStyle(answers[i]);
        }
    }
    // blocks.answers = answers;
    // 替换解答
    let solutions = blocks.solutions;
    for (let i = 0; i < solutions.length; i++) {
        solutions[i] = _removeStyle(solutions[i]);
    }

    // 替换解析
    let explanations = blocks.explanations;
    for (let i = 0; i < explanations.length; i++) {
        explanations[i] = _removeStyle(explanations[i]);
    }
};

// 设置相似题
const setSimilarQuestions = async (questionId, newSimilarQuestionIds) => {
    if (!_.isArray(newSimilarQuestionIds)) return;
    let question;
    if (_.isObject(questionId)) {
        question = questionId;
    } else {
        question = await db.collection('question').findOne({_id: questionId});
    }
    if (!question) throw '无对应 ID 试题';
    let similarQuestionIds = (_.isArray(question.reco_questions) ? question.reco_questions : []).filter(e => !!e);
    let relatedSimilarQuestions = await db.collection('question').find({_id: {$in: _.uniq([...similarQuestionIds, ...newSimilarQuestionIds])}}).toArray();
    let appendIds = _.difference(newSimilarQuestionIds, similarQuestionIds);
    let removeIds = _.difference(similarQuestionIds, newSimilarQuestionIds);

    // 修改新增关联相似题的相似题状态
    await Promise.all(appendIds.map(id => {
        let question = _.find(relatedSimilarQuestions, e => e._id === id);
        if (!question) throw `对应 ID ${id} 相似题不存在`;
        let similarQuestionIds = _.isArray(question.reco_questions) ? question.reco_questions : [];
        let newSimilarQuestionIds = _.uniq([...similarQuestionIds, questionId]).filter(_id => _id !== id);
        if (similarQuestionIds.sort().join() === newSimilarQuestionIds.sort().join()) {
            return question;
        }
        return db.collection('question').updateOne(
            {_id: id},
            {$set: {reco_questions: newSimilarQuestionIds}}
        );
    }));

    // 修改移除关联相似题的相似题状态
    await Promise.all(removeIds.map(id => {
        let question = _.find(relatedSimilarQuestions, e => e._id === id);
        if (!question) throw '关联相似题异常';
        let similarQuestionIds = _.isArray(question.reco_questions) ? question.reco_questions : [];
        let newSimilarQuestionIds = similarQuestionIds.filter(_id => _id !== questionId);
        if (similarQuestionIds.sort().join() === newSimilarQuestionIds.sort().join()) {
            return question;
        }
        return db.collection('question').updateOne(
            {_id: id},
            {$set: {reco_questions: newSimilarQuestionIds}}
        );
    }));

    await db.collection('question').updateOne(
        {_id: questionId},
        {$set: {reco_questions: newSimilarQuestionIds.filter(id => id !== questionId)}}
    );
};

// 创建试题
const createQuestion = async (questionData) => {
    const now = new Date();
    let question = {};
    question.id = questionData.id;
    // question.elite = questionData.elite || 0; mod: 取消创建试题默认 非精品（elite=0）设置，非 0|1 视为【未编题】
    if (questionData.elite === 0 || questionData.elite === 1) {
        question.elite = questionData.elite;
    }
    question.description = questionData.description || '';
    question.type = questionData.type || '';
    question.comment = questionData.comment || '';
    question.difficulty = 0.5;
    question.orig_diff = questionData.orig_diff || question.difficulty;
    question.region = questionData.region || '';
    question.period = questionData.period || '';
    question.subject = questionData.subject || '';
    question.source_url = '';
    question.ctime = now;
    question.utime = now;
    question.has_modified = questionData.has_modified || 1;
    question.from = questionData.from || 'kbp';
    question.year = now.getFullYear();
    question.quality = 1;
    question.refer_exampapers = [];
    question.refer_times = 0;
    question.knowledges = questionData.knowledges || [];
    if (questionData.tags) {
        question.tags = questionData.tags;
    }
    if (questionData.type_tags) {
        question.type_tags = questionData.type_tags;
    }
    // 校验相似题
    if (questionData.reco_questions) {
        let err = null;
        if (!_.isArray(questionData.reco_questions)) {
            err = '创建试题相似题 (reco_questions) 字段需为数组形式';
        } else {
            // 支持相似题列表存在 Object 的形式 - 从 Object 中尝试提取 ID
            questionData.reco_questions = questionData.reco_questions.map(e => {
                if (_.isObject(e)) return e.id;
                return e;
            });
            questionData.reco_questions.forEach(id => {
                if (!_.isNumber(id)) err = `创建试题相似题 (reco_questions) 数组中存在不为数字类型的ID ${id}`;
            });
        }
        if (err) {
            throw err;
        }
        question.reco_questions = questionData.reco_questions;
    }
    question.blocks = questionData.blocks || {
        stems: [],
        types: [],
        answers: [],
        explanations: [],
        solutions: [],
        knowledges: [],
        core_knowledges: [],
        comments: []
    };

    if (!questionData.difficulty) {
        question.difficulty = 0.6;
    }

    if (questionData.from === 'zjw') {
        question.source_url = questionData.source_url;
    }

    // 替换是试题中可能由于老师输入带入的font-size和font-family
    _removeQuestionStyle(question);
    // 简单、中等、困难 难度向 0-1难度转化
    if (['简单', '中等', '困难'].indexOf(questionData.difficulty) >= 0) {
        if (questionData.difficulty === '简单') {
            question.difficulty = 0.8;
        } else if (questionData.difficulty === '困难') {
            question.difficulty = 0.4;
        } else {
            question.difficulty = 0.6;
        }
    }

    questionData.difficulty = Number(questionData.difficulty);

    // 1-5 难度向 0-1难度转化过程
    if (!isNaN(questionData.difficulty) && questionData.difficulty >= 1 && questionData.difficulty <= 5) {
        let difficulty_arry = [0.9, 0.75, 0.6, 0.4, 0.1];
        questionData.difficulty = difficulty_arry[parseInt(questionData.difficulty) - 1];
    }

    if (!isNaN(questionData.difficulty) && questionData.difficulty > 0 && questionData.difficulty < 1) {
        question.difficulty = questionData.difficulty;
    }
    // orig diff
    // 新增时候会附加这个字段，和 difficulty 保持一致，范围0-1，之后的修改不会再动这个字段 [子韬确认]
    question.orig_diff = question.difficulty;

    // 插入式题结构兼容
    let err = _compatible_question([question]);
    if (err) {
        throw 'PARAMETERS_ERROR:' + err.message;
    }
    // 需要新申请一个
    if (typeof question.id === 'string') {
        let id = await counter.nextSeq('question_private', 1);
        question.id = id[0];
    }
    question._id = question.id;
    delete question.id;
    await db.collection('question').insertOne(question, {w: 'majority'});
    // 设置相似题
    if (!_.isEmpty(question.reco_questions)) {
        await setSimilarQuestions(question, question.reco_questions);
    }
    return question;
};


module.exports = {
    createQuestion,
    addRecoQuestions,
    setSimilarQuestions,
};
