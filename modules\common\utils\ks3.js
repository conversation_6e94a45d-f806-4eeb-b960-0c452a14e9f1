/**
 * Created by kevin on 15/11/25.
 */
const crypto = require('crypto');
const querystring = require('querystring');
const Config = require('config');
const urllib = require('url');
const { BosClient } = require('@baiducloud/sdk');
const BOS_CONFIG = Config.get('BOS');

const bos_con = {
    endpoint: BOS_CONFIG.endpoint, // 	bj.bcebos.com
    credentials: {
        ak: BOS_CONFIG.ak,
        sk: BOS_CONFIG.sk
    }
};

const client = new BosClient(bos_con);

// 签名
const getSignedUrl = (url, expirationInSeconds = 7200) => {
    let key = urllib.parse(url).pathname;
    let timestamp = null; // 默认当前时间
    return client.generatePresignedUrl(BOS_CONFIG.bucketName, key, timestamp, expirationInSeconds);
};

function get_sign_url(url, expirationAfter) {
    let _url = urllib.parse(url);

    // 百度云
    if (_url.host === BOS_CONFIG.hostname) {
        return getSignedUrl(url, expirationAfter);
    }

    var path = _url.path;
    var hostname = _url.hostname;
    var bucket_name = '';
    if (hostname.search('-ks3') >= 0) {
        bucket_name = hostname.split('-ks3')[0];
        path = '/' + bucket_name + path;
    } else if (hostname.search('-kss') >= 0) {
        bucket_name = hostname.split('-kss')[0];
        path = '/' + bucket_name + path;
    } else {
        bucket_name = _url.path.split('/')[1]; // bucket名字在路径第一层
    }

    let bucket_conf = 'ks_conf.' + bucket_name;
    var conf = Config.get(bucket_conf);
    if (!conf) {
        throw new Error(`${url} doesnot have legal bucket name`);
    }

    // 设置过期时间以及签名, 签名包含路径信息
    // TODO 过期时间 1970-01-18T22:46:41.825Z，是否应该调整
    var expires = parseInt(new Date().getTime() / 1000) + expirationAfter;
    var text = ['GET', '', '', expires, path].join('\n');

    var signature = crypto.createHmac('sha1', conf.sk).update(text).digest('base64');

    var query = {
        KSSAccessKeyId: conf.ak,
        Expires: expires,
        Signature: signature,
    };
    var requrl = url + '?' + querystring.stringify(query);
    return requrl;
}

module.exports = {
    get_sign_url: get_sign_url,
};
