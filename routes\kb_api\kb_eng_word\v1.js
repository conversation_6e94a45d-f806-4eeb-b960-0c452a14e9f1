const express = require('express');
const router = express.Router();
const newWord = require('../../../modules/kb_api/v2/eng_word/new_word');
const multer = require('multer');
const upload = multer({dest: './uploads'});

// 获取生词，生词率
router.post('/new_words', newWord.getNewWord);
// 新增场景列表
router.post('/scenes', newWord.addNewWord);
// 获取场景列表
router.get('/scenes', newWord.getScene);
// 修改删除场景
router.put('/scenes', newWord.updateScene);
// 列表
router.get('/words', newWord.wordsList);
// 更新
router.put('/words/:id', newWord.updateWord);
// 删除
router.delete('/words/:id', newWord.deleteWord);
// 查找
router.get('/uniqueWord', newWord.searchWord);
// 检查新增场景数据
router.post('/checkData', upload.single('file'), newWord.checkData);

module.exports = router;