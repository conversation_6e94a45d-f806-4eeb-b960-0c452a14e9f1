const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const { ObjectId } = require('mongodb');

const { createResourceAlbumValidator, updateResourceAlbumValidator } = require('./schema/resource_album');

/**
 * 创建资源专辑
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const createResourceAlbum = async (req, res) => {
    res.resMode = 'normal';
    let resourceWrapper = new ResponseWrapper(res);
    let body = req.body;
    if (!createResourceAlbumValidator(body)) {
        return resourceWrapper.error('PARAMETERS_ERROR');
    }
    try {
        const dbs = (req.query.dbs || 'basic').split(',');
        const isKbp = dbs.includes('kbp');
        body.dbs = (body.dbs || dbs).filter(db => isKbp || dbs.includes(db));

        body.ctime = new Date();
        body.utime = new Date();
        body.valid = 1;
        let result = await db.collection('resource_album').insertOne(body);
        return resourceWrapper.succ({ id: result.insertedId.toString() });
    } catch (error) {
        return resourceWrapper.error('HANDLE_ERROR', error);
    }
};

/**
 * 更新资源专辑
 * @param {*} req
 * @param {*} res
 * @returns {ResponseWrapper}
 */
const updateResourceAlbum = async (req, res) => {
    res.resMode = 'normal';
    let resourceWrapper = new ResponseWrapper(res);
    let body = req.body;
    if (!updateResourceAlbumValidator(body)) {
        return resourceWrapper.error('PARAMETERS_ERROR');
    }
    let { id, dbs = 'basic' } = req.query;
    if (!id) {
        return resourceWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');
    const query = {
        _id: new ObjectId(id),
        dbs: { $in: dbs }
    };
    // 非kbp平台，不能更新 dbs, valid, 只能更新自己的有效资源
    if (!dbs.includes('kbp')) {
        delete body.dbs;
        delete body.valid;
        query.valid = 1;
    }

    body.utime = new Date();
    try {
        let resourceAlbum = await db.collection('resource_album').findOne(query, { fields: { _id: 1 } });
        if (!resourceAlbum) {
            return resourceWrapper.error('NULL_ERROR', '资源未找到');
        }
        await db.collection('resource_album').updateOne({ _id: new ObjectId(id) }, { $set: body });
        return resourceWrapper.succ({ id });
    } catch (error) {
        return resourceWrapper.error('HANDLE_ERROR', error);
    }

};

/**
 * 删除资源专辑
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const deleteResourceAlbum = async (req, res) => {
    res.resMode = 'normal';
    let resourceWrapper = new ResponseWrapper(res);

    let { id, dbs = 'basic' } = req.query;
    if (!id) {
        return resourceWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');
    const query = {
        _id: new ObjectId(id),
        dbs: { $in: dbs },
        valid: 1,
    };
    try {
        let resourceAlbum = await db.collection('resource_album').findOne(query, { fields: { _id: 1, dbs: 1 } });
        if (!resourceAlbum) {
            return resourceWrapper.error('NULL_ERROR', '资源未找到');
        }
        resourceAlbum.dbs = resourceAlbum.dbs.filter(db => !dbs.includes(db));
        resourceAlbum.utime = new Date();
        await db.collection('resource_album').updateOne({ _id: new ObjectId(id) }, { $set: resourceAlbum });
        return resourceWrapper.succ({ id });
    } catch (error) {
        return resourceWrapper.error('HANDLE_ERROR', error);
    }
};



/**
 * 获取资源专辑详情
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const getResourceAlbum = async (req, res) => {
    res.resMode = 'normal';
    let resourceWrapper = new ResponseWrapper(res);
    let { id, dbs = 'basic' } = req.query;
    if (!id) {
        return resourceWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');
    const query = {
        _id: new ObjectId(id),
        dbs: { $in: dbs },
        valid: 1,
    };
    try {
        let resourceAlbum = await db.collection('resource_album').findOne(query);
        if (!resourceAlbum) {
            return resourceWrapper.error('NULL_ERROR', '资源未找到');
        }
        return resourceWrapper.succ(resourceAlbum);
    } catch (error) {
        return resourceWrapper.error('HANDLE_ERROR', error);
    }
};


/**
 * 获取专辑资源列表
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const getResourceAlbumList = async (req, res) => {
    res.resMode = 'normal';
    let resourceWrapper = new ResponseWrapper(res);
    const sort = { ctime: -1 };
    let { period, subject, limit = 10, offset = 0, valid = 1, dbs = 'basic' } = req.query;
    limit = +limit;
    offset = +offset;
    if (!period || !subject || limit !== limit || offset !== offset) {
        return resourceWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');
    const query = {
        dbs: { $in: dbs },
        valid: 1,
        period,
        subject,
    };
    if (dbs.includes('kbp')) {
        query.valid = valid;
    }
    try {
        let resourceAlbumList = await db.collection('resource_album').find(query).sort(sort).skip(offset).limit(limit).toArray();
        if (!resourceAlbumList || resourceAlbumList.length === 0) {
            return resourceWrapper.error('NULL_ERROR', '无资源');
        }
        return resourceWrapper.succ(resourceAlbumList);
    } catch (error) {
        return resourceWrapper.error('HANDLE_ERROR', error);
    }
};


module.exports = {
    createResourceAlbum,
    updateResourceAlbum,
    deleteResourceAlbum,
    getResourceAlbum,
    getResourceAlbumList,
};