const ajv = require('ajv');
const ajvInstance = new ajv({
    removeAdditional: true,
});


const createResourceAlbumSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        icon: { type: 'string' },
        name: { type: 'string' },
        title: { type: 'string' },
        subtitle: { type: 'string' },
        description: { type: 'string' },
        period: { type: 'string' },
        grade: { type: 'string' },
        subject: { type: 'string' },
        year: { type: 'integer' },
        view_times: { type: 'integer' },
        download_times: { type: 'integer' },
        resource_count: { type: 'integer' },
        status: { type: 'integer' },
        children_title: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    title: { type: 'string' },
                    style: {
                        type: 'object',
                        properties: {
                            key: {
                                type: 'string'
                            }
                        }
                    }
                }
            }
        },
        children: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    type: { type: 'string' },
                    is_query: { type: 'integer' },
                    children: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                name: { type: 'string' },
                                type: { type: 'string' },
                                is_query: { type: 'integer' },
                                children: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            id: { type: 'string' },
                                            name: { type: 'string' },
                                            type: { type: 'string' },
                                            is_query: { type: 'integer' },
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        dbs: {
            type: 'array',
            items: { type: 'string' }
        },
    }
};


const updateResourceAlbumSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        icon: { type: 'string' },
        name: { type: 'string' },
        title: { type: 'string' },
        subtitle: { type: 'string' },
        description: { type: 'string' },
        period: { type: 'string' },
        grade: { type: 'string' },
        subject: { type: 'string' },
        year: { type: 'integer' },
        view_times: { type: 'integer' },
        download_times: { type: 'integer' },
        resource_count: { type: 'integer' },
        status: { type: 'integer' },
        children_title: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    title: { type: 'string' },
                    style: {
                        type: 'object',
                        properties: {
                            key: {
                                type: 'string'
                            }
                        }
                    }
                }
            }
        },
        children: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    type: { type: 'string' },
                    is_query: { type: 'integer' },
                    children: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                id: { type: 'string' },
                                name: { type: 'string' },
                                type: { type: 'string' },
                                is_query: { type: 'integer' },
                                children: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            id: { type: 'string' },
                                            name: { type: 'string' },
                                            type: { type: 'string' },
                                            is_query: { type: 'integer' },
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        dbs: {
            type: 'array',
            items: { type: 'string' }
        },
        valid: {
            type: 'integer',
            enum: [0, 1]
        }
    }
};


const createResourceAlbumValidator = ajvInstance.compile(createResourceAlbumSchema);
const updateResourceAlbumValidator = ajvInstance.compile(updateResourceAlbumSchema);

module.exports = {
    createResourceAlbumValidator,
    updateResourceAlbumValidator,
};