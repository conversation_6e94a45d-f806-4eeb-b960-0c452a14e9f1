/* eslint-disable no-undef */
const config = require('config');
const expect = require('chai').expect;
const superagent = require('superagent');
const url = require('url');
const qs = require('qs');
const KBAPISERV = config.get('kb_api_server');


describe('修改用户配置信息', function () {

	let appendQuesUrl = url.format({
		protocol: KBAPISERV.protocol,
		hostname: KBAPISERV.hostname,
		pathname: '/kb_api/v2/questions',
		port: process.env.NODE_PORT,
		search: qs.stringify({
			api_key: KBAPISERV.api_key
		})
	});

	it('正例测试', function (done) {
		let body = {
			'description': '如图为某草原生态系统的食物网简图．',
			'blocks': {
				'types': ['选择题'],
				'stems': [{
					'stem': '如图为某草原生态系统的食物网简图．',
					'options': {
						'A': '1234',
						'B': '1234',
						'C': '1234',
						'D': '1234',
					}
				}],
				'explanations': ['如图为某草原生态系统的食物网简图．'],
				'answers': ['如图为某草原生态系统的食物网简图．'],
				'solutions': ['如图为某草原生态系统的食物网简图．']
			},
			'comment': '如图为某草原生态系统的食物网简图．',
			'orig_diff': 0.6,
			'difficulty': 0.6,
			'has_modified': 1,
			'type': '选择题',
			'quality': 1,
			'region': '',
			'period': '高中',
			'subject': '语文',
			'source_url': '',
			'from': 25,
			'refer_times': 0,
			'year': 2017,
			'knowledges': [{
				'importance': 2,
				'chance': 0.08,
				'score': 0.48,
				'id': 2030764031,
				'name': '种间关系'
			}]
		};

		superagent
			.post(appendQuesUrl)
			.set('Content-Type', 'application/json')
			.send(body)
			.end(function (err, res) {
				expect(err).to.be.an('null');
				expect(res).to.not.be.an('null');
				expect(res.status).to.be.equal(200);
				let ret = JSON.parse(res.text);
				expect(ret).to.be.an('object');
				expect(ret.question_id).to.be.an('number');
				console.log(ret.question_id);
				done();
			});
	});
});

