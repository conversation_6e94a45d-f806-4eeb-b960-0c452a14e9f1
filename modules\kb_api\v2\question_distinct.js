const ObjectID = require('mongodb').ObjectID;
const Ajv = require('ajv');
const ajv = new Ajv({removeAdditional: 'all'});
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const schema = require('./schemas/question_distinct');

// 数据库集合名
const questionDistinctColl = 'question_distinct';
const questionColl = 'question';

/**
 * 数据模板
 * @param body
 * @returns {{period: *, subject: *, type: *,
 *            parent: *, children: *}}
 */
const getDataTemplate = (body) => {
    return {
        period: body.period,
        subject: body.subject,
        type: body.type,
        parent: body.parent,
        children: body.children
    };
};

/**
 * 子试题去重，并不包含母题
 * @param children
 * @param parent
 * @returns {any[]}
 */
const uniq = (children, parent) => {
    // 去重
    children = Array.from(new Set(children));
    let parentIndex = children.indexOf(parent);
    // 去除母题
    if (parentIndex > -1) {
        children.splice(children.indexOf(parent), 1);
    }
    return children;
};

/**
 * 获取子题
 * @param data
 * @returns {Promise<{setChildren: (*|any[]), childrenIds: Array}>}
 */
const getChildren = async (data) => {
    data.children = uniq(data.children, data.parent);
    // 所有子题合并
    let setChildren = data.children;
    let childQuesCond = {parent: {$in: data.children}};
    let project = {children: 1};
    let oldChildren = await db.collection(questionDistinctColl).find(childQuesCond).project(project).toArray();
    let childrenIds = [];
    for (let child of oldChildren) {
        setChildren = setChildren.concat(child.children);
        childrenIds.push(child._id);
    }
    setChildren = uniq(setChildren, data.parent);
    return {setChildren, childrenIds};
};

/**
 * 删除旧的母子关系
 * @param childrenIds
 * @param saveId
 * @returns {Promise<void>}
 */
const deleteOldChildren = async (childrenIds, saveId = '') => {
    let arr = childrenIds.map(item => {
        return item.toString();
    });
    let saveIdStr = saveId.toString();
    if (arr.indexOf(saveIdStr) > -1) {
        arr.splice(arr.indexOf(saveIdStr), 1);
    }
    if (arr.length > 0) {
        arr = arr.map(item => {
            return ObjectID(item);
        });
        await db.collection(questionDistinctColl).deleteMany({_id: {$in: arr}});
    }
};
/**
 * 更新试题是否是重复题
 * @param children
 * @param parent
 * @returns {Promise<void>}
 */
const updateQuestionsRepeat = async (children, parent) => {
    if (parent) {
        // 非重复题
        let cond = {_id: parent};
        let set = {repeat: 0, utime: new Date()};
        await db.collection(questionColl).updateOne(cond, {$set: set});
    }
    if (Array.isArray(children) && children.length > 0) {
        // 重复题
        let cond = {_id: {$in: children}};
        let set = {repeat: 1, utime: new Date()};
        await db.collection(questionColl).updateMany(cond, {$set: set});
    }
};

/**
 * 更新去重表
 * @param cond
 * @param children
 * @returns {Promise<void>}
 */
const updateQuestionDistinct = async (cond, children) => {
    let set = {children: children, utime: new Date()};
    await db.collection(questionDistinctColl).findOneAndUpdate(cond, {$set: set});
};

const checkChildren = async (data) => {
    for (let i = 0; i < data.children.length; i++) {
        let child = data.children[i];
        let childQuesCond = {children: child};
        let doc = await db.collection(questionDistinctColl).findOne(childQuesCond);
        if (doc && doc.parent !== data.parent) {
            return `子题id${child}已经存在，不能再为其他试题的子题`;
        }
    }
    return false;
};

/**
 * 接收数据接口
 * @param req
 * @param res
 * @returns {Promise<*>}
 */
const add = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    try {
        // 入库数据模板
        let data = getDataTemplate(body);
        // 数据格式校验
        if (!ajv.validate(schema.post, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '格式错误');
        }
        // 子题校验，子题不能再为其他试题的子题
        let exists = await checkChildren(data);
        if (exists) {
            return responseWrapper.error('HANDLE_ERROR', exists);
        }
        // 更新子题
        let {setChildren, childrenIds} = await getChildren(data);
        data.children = setChildren;
        // 母题存在
        let parentQuesCond = {parent: data.parent};
        let docs = await db.collection(questionDistinctColl).find(parentQuesCond).toArray();
        if (docs.length > 1) {
            return responseWrapper.error('HANDLE_ERROR', '数据异常');
        }
        let doc = docs[0];
        if (doc) {
            let cond = {_id: doc._id};
            let children = doc.children.concat(data.children);
            children = uniq(children, doc.parent);
            await updateQuestionDistinct(cond, children);
            await deleteOldChildren(childrenIds, doc._id);
            await updateQuestionsRepeat(children, doc.parent);
            responseWrapper.succ({id: doc._id});
        } else {
            // 子题存在
            let childQuesCond = {children: data.parent};
            let docs = await db.collection(questionDistinctColl).find(childQuesCond).toArray();
            if (docs.length > 1) {
                return responseWrapper.error('HANDLE_ERROR', '数据异常');
            }
            let doc = docs[0];
            if (doc) {
                let cond = {_id: doc._id};
                let children = doc.children.concat(data.children);
                children = uniq(children, doc.parent);
                await updateQuestionDistinct(cond, children);
                await deleteOldChildren(childrenIds, doc._id);
                await updateQuestionsRepeat(children, doc.parent);
                responseWrapper.succ({id: doc._id});
            } else {
                data.ctime = new Date();
                data.utime = new Date();
                let result = await db.collection(questionDistinctColl).insertOne(data);
                if (result.insertedId) {
                    await deleteOldChildren(childrenIds, '');
                    await updateQuestionsRepeat(data.children, data.parent);
                    responseWrapper.succ({id: result.insertedId});
                } else {
                    responseWrapper.error('HANDLE_ERROR', '保存出错了');
                }
            }
        }
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

/**
 * 更新试题是否是重复题
 * @param children
 * @param parent
 * @returns {Promise<void>}
 */
const updateRepeat = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        // 数据格式校验
        if (!ajv.validate(schema.updateRepeat, req.body)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '格式错误');
        }
        let {parent, children} = req.body;
        if (!parent) {
            return responseWrapper.error('PARAMETERS_ERROR', '母题不存在');
        }
        if (!Array.isArray(children) || children.length === 0) {
            return responseWrapper.error('PARAMETERS_ERROR', '子题不存在');
        }

        // 非重复题
        let pcond = {_id: parent};
        let pset = {repeat: 0, utime: new Date()};
        await db.collection(questionColl).updateOne(pcond, {$set: pset});

        // 重复题
        let cond = {_id: {$in: children}};
        let set = {repeat: 1, utime: new Date()};
        await db.collection(questionColl).updateMany(cond, {$set: set});
        responseWrapper.succ({});
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
};


module.exports = {
    updateRepeat,
};
