/**
 * 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 9500,
    app_key: 'iyunxiao_kbplat180305',
};

describe('根据id获取辅导手册', function () {
    this.timeout(90000);
    it('/kb_api/v2/tutor_manual_chapters/:id', async function () {
        let id = '2147418111,2147352575';
        let pathname = `/kb_api/v2/tutor_manual_chapters/${id}`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            level: '1'
        };
        let result = await request.get(url).query(data);
        console.log(result.body);
        assert.ok(Array.isArray(result.body));
    });
    it('/kb_api/v2/tutor_manual_chapters/:id', async function () {
        let id = 2147418111;
        let pathname = `/kb_api/v2/tutor_manual_chapters/${id}`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            level: '1'
        };
        let result = await request.get(url).query(data);
        console.log(result.body);
        assert.ok(result.body.id === id);
    });
});