// 视频资源相关路由
const video = require('../../../modules/kb_api/v2/video/resource');

module.exports = function (router) {

    router.get('/knowledges/:knowledge_ids/videos', video.knowledge_videos); // todo 迁移到知识点下
    // 获取视频分类信息
    router.get('/videos/categorys/', video.video_cats);
    // 通过视频分类id获取视频列表
    router.get('/videos/categorys/:category_id/', video.video_category);
    //视频专辑列表 by sq
    router.get('/videos/categories', video.get_video_categories);
    //创建视频专辑
    router.post('/videos/categories', video.add_video_category);
    //编辑视频专辑
    router.put('/videos/categories/:category_id', video.put_video_category);
    //移除视频专辑
    router.delete('/videos/categories/:category_id', video.delete_video_category);
    //视频专辑视频列表
    router.get('/videos/categories/:category_id/videos', video.get_video_category_videos);
    //视频专辑添加视频
    router.post('/videos/categories/:category_id/videos', video.add_video_category_videos);
    //视频专辑视频信息
    router.get('/videos/categories/:category_id/videos/:video_id', video.get_video_category_video);
    //编辑视频
    router.put('/videos/:video_id', video.put_video);
    //移除专辑视频
    router.delete('/videos/categories/:category_id/videos/:video_id', video.delete_video_category_video);
    //视频专辑添加已有视频
    router.put('/videos/categories/:category_id/videos_existing/:video_id', video.put_video_category_existing_video);
    //查询视频
    router.get('/videos/video_list', video.get_video_list);
    //创建基础视频
    router.post('/videos/', video.add_video);
    // 获取多个视频，根据ids=101, 234
    router.get('/videos/', video.videos);
    //url签名
    router.get('/videos/cryptoUrl', video.cryptoUrl);
    // 根据视频id取视频信息
    router.get('/videos/:video_id/', video.video);
    //视频专辑信息
    router.get('/videos/categories/:category_id', video.get_video_category);
    //根据视频id获取专辑信息
    router.get('/videos/getcategories/:video_id', video.getcategories);
    
};