/*
 * Desc:
 *      验证传入的book_id是否有效
 * Params:
 *      p - 调用方传参
 * Return:
 *
 * Author:
 *      z<PERSON><PERSON>
 * Date:
 *      2016-08-19
*/
function check_book_id(p) {
    if (p > 0) {
        return true;
    } else {
        return false;
    }
}

/*
 * Desc:
 *      验证传入的knowledge_tree_id是否有效
 * Params:
 *      p - 调用方传参
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-19
*/
function check_knowledge_tree_id(p) {
    if (p > 0) {
        return true;
    } else {
        return false;
    }
}

/*
 * Desc: 将调用方传入的fields_type映射为查询数据库的语句
 * Params: p - 调用方传参
 * Return: {field:1}
*/
function kn_fields_type_post(p) {
    let obj = {
        common: {
            _id: 1,
            name: 1,
            subject: 1,
            period: 1,
            score: 1,
            chance: 1,
            card_contents: 1,
            importance: 1,
            contents: 1,
            statistics: 1,
            'videos.id': 1,
            'videos.name': 1,
            'videos.from': 1,
            human_importance: 1,
            knowledge_teach: 1,
            dimensions: 1,
            cognitions: 1,
            make_crises: 1,
            pre_knowledges: 1,
            next_knowledges: 1,
            card_contents_b: 1,
            dimensions_b: 1,
            cognitions_b: 1,
            make_crises_b: 1,
            freq_ques_type: 1,
            knowledge_summary: 1,
        },
        statistics: {
            _id: 1,
            name: 1,
            importance: 1,
            statistics: 1,
            score: 1,
            chance: 1,
            card_contents: 1
        },
        hfsfd_courseware: {
            _id: 1,
            name: 1,
            importance: 1,
            human_importance: 1,
            dimensions: 1,
            card_contents: 1
        },
        fudao_app: {
            _id: 1,
            name: 1,
            card_contents: 1,
            card_contents_b: 1,
            pre_knowledges: 1
        }
    };
    return obj[p] || obj.common;
}

/*
 * Desc:
 *      将调用方传入的fields_type映射为查询数据库的语句
 * Params:
 *      p - 调用方传参
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-23
*/
function paper_fields_type_post(p) {
    let fields = [];
    if ('common' === p) {
        fields = ['_id', 'name', 'vague_name',
            'view_times', 'download_times',
            'blocks', 'period', 'subject', 'type', 'provinces',
            'type_score', 'score', 'province', 'city', 'from_year', 'to_year',
            'press_version', 'grade', 'g_paper_id', 'user_id', 'user_name'];
    } else {
        fields = ['_id', 'name', 'vague_name',
            'view_times', 'download_times', 'provinces',
            'blocks', 'statistics', 'period', 'subject', 'type', 'grade',
            'type_score', 'score', 'province', 'city', 'from_year', 'to_year'];
    }
    let proj = {};
    for (let field of fields) {
        proj[field] = 1;
    }
    return proj;
}

/*
 * Desc:
 *      将调用方传入的fields_type映射为查询数据库的语句
 * Params:
 *      p - 调用方传参
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-23
*/
function ebook_fields_type_post(p) {
    var fields = null;
    if ('common' == p) {
        fields = ['pages_url', 'pages_num'];
    } else {
        fields = [];
    }
    var proj = {};
    for (var i in fields) {
        proj[fields[i]] = 0;
    }
    return proj;
}

/*
 * Desc:
 *      将调用方传入的kn_q_sort_by映射为查询数据库的语句
 * Params:
 *      p - 调用方传参
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2017-03-20
*/
function kn_q_sort_by_post(p) {
    if ('integrated' == p) {
        return 'sco';
    } else if ('year' == p) {
        return 'year';
    } else if ('cite_num' == p) {
        return 'ncite';
    } else if ('refer_times' == p) {
        return 'refer_times';
    } else if ('use_times' == p) {
        return 'use_times';
    } else if ('quality' == p) {
        return 'quality';
    } else {
        return 'ntest';
    }
}

/**
 * 试题排序
 * @param p
 *  // 排序规则, 默认'year' 表示按时间排序  ；
 *  'refer_times',
 *  'integrated'，表示综合排序；
 *  'cite_num'，表示引用次数排序；
 *  'test_num'，表示真卷次数排序,
 *  'use_times'表示组卷次数排序
 */
function question_sort_by_post(p) {
    if ('integrated' === p) {
        return { 'year': -1, 'ctime': -1, 'score': -1 };
    } else if ('year' === p) {
        return { 'year': -1 };
    } else if ('cite_num' === p) {
        return { 'refer_times': -1 };
    } else if ('refer_times' === p) {
        return { 'refer_times': -1 };
    } else if ('use_times' === p) {
        return { 'use_times': -1 };
    } else {
        return { 'ntest': -1 };
    }
}


/*
 * Desc:
 *      验证传入的difficulty是否有效
 * Params:
 *      p - 调用方传参
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-09-05
*/
function check_diff(p) {
    if (p) {
        var s = new Set(['容易', '较易', '中等', '较难', '困难']);
        var diffs = p.split(',');
        for (var i in diffs) {
            var diff = diffs[i];
            if (!s.has(diff)) {
                return false;
            }
        }
        return true;
    }
}

/*
 * Desc:
 *      将调用方传入的难度映射为查询数据库的语句
 * Params:
 *      p - 调用方传参
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-19
 */
function diff_post(p) {
    var diffs = p.split(',');
    var int_diffs = new Set();
    for (var i in diffs) {
        var diff = diffs[i];
        var int_diff = 0;
        if ('容易' == diff) {
            int_diffs.add(1);
            // int_diffs.add(1);
            //int_diffs.add(2);
        } else if ('较易' == diff) {
            int_diffs.add(2);
            // int_diffs.add(3);
            // int_diffs.add(4);
        } else if ('中等' == diff) {
            int_diffs.add(3);
            // int_diffs.add(5);
            // int_diffs.add(6);
        } else if ('较难' == diff) {
            int_diffs.add(4);
            // int_diffs.add(7);
            // int_diffs.add(8);
        } else if ('困难' == diff) {
            int_diffs.add(5);
            // int_diffs.add(9);
            // int_diffs.add(10);
        }
    }
    return int_diffs;
}

function check_ids(p) {
    var ids = p;
    if (!(ids instanceof Array)) {
        ids = p.split(',');
    }
    var reg = /^[0-9]+$/;
    for (var i in ids) {
        var id = ids[i];
        if (!reg.test(id)) {
            throw ('id 不是整数！');
        }
    }
    return true;
}

function split_ids(ids) {
    var max_num = 300;
    var result = [];
    var ids = ids;
    if (!(ids instanceof Array)) {
        ids = ids.split(',');
    }
    for (var i in ids) {
        var id = parseInt(ids[i]);
        result.push(id);
        if (i > max_num) {
            break;
        }
    }
    return result;
}

function level_post(p) {
    if ('province' == p) {
        return 1;
    } else if ('city' == p) {
        return 2;
    } else if ('country' == p) {
        return 3;
    }
}

module.exports = {
    check_book_id: check_book_id,
    check_knowledge_tree_id: check_knowledge_tree_id,
    kn_fields_type_post: kn_fields_type_post,
    paper_fields_type_post: paper_fields_type_post,
    ebook_fields_type_post: ebook_fields_type_post,
    kn_q_sort_by_post: kn_q_sort_by_post,
    question_sort_by_post: question_sort_by_post,
    check_diff: check_diff,
    diff_post: diff_post,
    check_ids: check_ids,
    split_ids: split_ids,
    level_post: level_post,
}
