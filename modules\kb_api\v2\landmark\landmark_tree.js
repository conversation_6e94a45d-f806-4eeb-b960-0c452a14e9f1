let Logger = require('../../../common/utils/logger');
let ResponseWrapper = require('../../../common/utils/response_wrapper');
let mongodber = require('../../../common/utils/mongodber');
let db = mongodber.use('KB');
const treeValue = require('../schemas/landmark_tree');
const counter = require('../../../common/utils/counter');

const landmark_tree = db.collection('landmark_tree');
const landmark_point = db.collection('landmark_point');

// const getIds = async (result) => {
//     if (Array.isArray(result.chapters)) {
//         for (let cha of result.chapters) {
//             for (let key in cha) {
//                 if (Object.prototype.hasOwnProperty.call(cha, key)) {
//                     if ('landmark_point_ids' === key) {
//                         let landmarkPointArr = [];
//                         let pointIds = cha['landmark_point_ids'];
//                         let pointData = await landmark_point.find({ _id: { $in: pointIds } }).project({ name: 1, invalid: 1 }).toArray();
//                         for (const pd of pointData) {
//                             if (0 === pd.invalid) { // 有效的数据
//                                 let data = { id: pd._id, name: pd.name, key: 'landmark' };
//                                 landmarkPointArr.push(data);
//                             }
//                         }
//                         if (landmarkPointArr.length > 0) { // 数据存在设置此字段，如果不存在，删除此字段
//                             cha['landmark_point_ids'] = landmarkPointArr;
//                         } else {
//                             delete cha['landmark_point_ids'];
//                         }
//                     } else if ('chapters' === key) {
//                         await getIds(cha);
//                     }
//                 }
//             }
//         }
//     }
// };

const getIds = (result, data) => {
    if (Array.isArray(result.chapters)) {
        for (let cha of result.chapters) {
            for (let key in cha) {
                if (Object.prototype.hasOwnProperty.call(cha, key)) {
                    if ('landmark_point_ids' === key) {
                        // 判断参数类型 确定使用哪块代码
                        if (Array.isArray(data)) {
                            let pointIds = cha['landmark_point_ids'];

                            if (Array.isArray(pointIds)) {
                                pointIds.forEach(id => {
                                    data.push(id);
                                })
                            }
                        } else {
                            let pointIds = cha['landmark_point_ids'];
                            let landmarkPointArr = [];
                            pointIds.forEach((id) => {
                                if(data[id]) {
                                    landmarkPointArr.push(data[id]);
                                }
                            });

                            if (landmarkPointArr.length > 0) { // 数据存在设置此字段，如果不存在，删除此字段
                                cha['landmark_point_ids'] = landmarkPointArr;
                            } else {
                                delete cha['landmark_point_ids'];
                            }
                        }

                    } else if ('chapters' === key) {
                        getIds(cha, data);
                    }
                }
            }
        }
    }
};

const deleteChapters = async (result) => {
    if (Array.isArray(result.chapters)) {
        for (let cha of result.chapters) {
            for (let key in cha) {
                if (Object.prototype.hasOwnProperty.call(cha, key)) {
                    if ('landmark_point_ids' === key) {
                        let landmarkPointArr = [];
                        if (Array.isArray(cha['landmark_point_ids'])) {
                            for (let ids of cha['landmark_point_ids']) {
                                landmarkPointArr.push(ids.id);
                            }
                        }
                        if (landmarkPointArr.length > 0) { // 数据存在设置此字段，如果不存在，删除此字段
                            cha['landmark_point_ids'] = landmarkPointArr;
                        } else {
                            delete cha['landmark_point_ids'];
                        }
                    } else if ('chapters' === key) {
                        await deleteChapters(cha);
                    }
                }
            }
        }
    }
};

const getLandmarkTreeList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let period = req.query.period;
    let subject = req.query.subject;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', 'period 和 subject 为必传字段');
    }
    try {
        let cond = {
            period: period,
            subject: subject,
            invalid: 0
        };
        let result = await landmark_tree.findOne(cond);
        if (result && result.chapters && Array.isArray(result.chapters)) {
            let resIds = [];
            getIds(result, resIds);
            let pointData = await landmark_point.find({ _id: { $in: resIds } }).project({ name: 1, invalid: 1 }).toArray();
            let dataMap = {};
            for (const pd of pointData) {
                if (0 === pd.invalid) { // 有效的数据
                    let data = { id: pd._id, name: pd.name, key: 'landmark' };
                    dataMap[pd._id] = data;
                }
            }
            getIds(result, dataMap);

            result.id = result._id;
            delete result._id;
        }
        return responseWrapper.succ(result);
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const putLandmarkTreeInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = Number(req.params.id);
    let data = req.body;
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR', 'id 为必传字段');
    }
    try {
        treeValue.valiLandmarkTreeDate(data);
        data.utime = new Date();
        await deleteChapters(data);
        data.has_modified = 1;
        await landmark_tree.updateOne({ _id: id }, { $set: data });
        return responseWrapper.succ({});
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getLandmarkTreeById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = Number(req.params.id);
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR', 'id 为必传字段');
    }
    try {
        let result = await landmark_tree.findOne({ _id: id, invalid: 0 });
        if (result && result.chapters && Array.isArray(result.chapters)) {
            let resIds = [];
            getIds(result, resIds);
            let pointData = await landmark_point.find({ _id: { $in: resIds } }).project({ name: 1, invalid: 1 }).toArray();
            let dataMap = {};
            for (const pd of pointData) {
                if (0 === pd.invalid) { // 有效的数据
                    let data = { id: pd._id, name: pd.name, key: 'landmark' };
                    dataMap[pd._id] = data;
                }
            }
            getIds(result, dataMap);
            result.id = result._id;
            delete result._id;
        }
        return responseWrapper.succ(result);
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const landmarkTreeInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let data = req.body;
    try {
        treeValue.valiLandmarkTreeDate(data);
        const seq = await counter.nextSeq('landmark_tree', 1);
        data._id = seq[0];
        data.invalid = 0;
        data.has_modified = 1;
        data.ctime = new Date();
        data.utime = new Date();
        await deleteChapters(data);
        let result = await landmark_tree.insertOne(data);
        return responseWrapper.succ(result.insertedId);
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getLandmarkTreeList: getLandmarkTreeList,
    getLandmarkTreeById: getLandmarkTreeById,
    putLandmarkTreeInfo: putLandmarkTreeInfo,
    landmarkTreeInfo: landmarkTreeInfo
};

