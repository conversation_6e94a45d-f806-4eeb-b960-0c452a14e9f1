const _ = require('lodash');
const Logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const counter = require('../../../common/utils/counter');
const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const questionHelper = require('../helpers/QuestionHelper');
const {
    createKbExampaperSchema,
    createHfsExampaperSchema,
    exampaperSchema,
    exampaperBlocksSchema,
    exampaperUnitSchema,
    createExampaperUnitSchema,
} = require('./schema/Exampaper');

// 兼容省市 provinces
const setProvinces = (paper) => {
    if (!paper.provinces) {
        if (paper.province) {
            paper.provinces = [{ name: paper.province, cities: [] }];
        }
        if (paper.province && paper.city) {
            paper.provinces[0].cities.push(paper.city);
        }
    }
};

/**
 * 校验 exampaper 是否满足 schema 规范
 * @param {*} paper
 * @return {*}
 */
const validateExampaper = (paper) => {
    setProvinces(paper);
    return ajv.validate(exampaperSchema, paper)
        && ajv.validate(exampaperBlocksSchema, paper.blocks);
};

const validateExampaperUnit = (paper) => {
    return ajv.validate(exampaperUnitSchema, paper)
        && ajv.validate(exampaperBlocksSchema, paper.blocks);
};
const validateCreateExampaperUnit = (paper) => {
    return ajv.validate(createExampaperUnitSchema, paper)
        && ajv.validate(exampaperBlocksSchema, paper.blocks);
};

// 设置blocks下面的 questions为 [ qid ]
const _procssQuestions = (block) => {
    let quesArr = [];
    let questions = block.questions;
    if (Array.isArray(questions) && questions.length) {
        let score = Number((block.score / questions.length).toFixed(1));
        questions.forEach((question) => {
            if (question) {
                if (_.isObject(question) && question.id) {
                    quesArr.push({ id: question.id, score: question.score || score });
                } else {
                    quesArr.push({ id: question, score: score });
                }
            }
        });
        block.questions = quesArr;
    }
};

/**
 * 创建试卷 - 电子试卷
 * date: 2018-07-19 for kbp 2.5 电子试卷
 * @param {*} req
 * @param {*} res
 */
const createExampaper = async (req, res) => {
    const response_wrapper = new ResponseWrapper(res);
    const exampaper = req.body;
    const g_paper_id = req.body.g_paper_id || 0;
    const valid = validateExampaper(exampaper);
    if (!valid) {
        return response_wrapper.error('PARAMETERS_ERROR');
    }
    try {
        const seq = await counter.nextSeq('digital_exampaper', 1);
        const now = new Date();
        exampaper.statistics = [];
        exampaper.type_score = [];
        exampaper.ctime = now;
        exampaper.is_elite = 0;
        exampaper.has_modified = 1;
        exampaper.utime = now;
        if (['drm', 'drm_yuanpei'].includes(exampaper.from)) {
            //exampaper.from = 'drm';
            delete exampaper.g_paper_id;
        } else if (['mt'].includes(exampaper.from)) {
            // 命题试卷
        } else if (['jyy'].includes(exampaper.from)) {
            // 教研云采集试题
            exampaper.from = 'jyy';
            exampaper.g_paper_id = g_paper_id;
        } else if (['xueke_zujuan'].includes(exampaper.from)) {

        } else {
            exampaper.from = 'mkp';
            exampaper.g_paper_id = g_paper_id;
        }
        exampaper.source_url = `/kb_api/v2/exampaper/${seq[0]}`;
        exampaper.download_times = 0;
        exampaper.category_id = 0;
        exampaper.view_times = 0;
        exampaper._id = seq[0];
        exampaper.dbs = exampaper.dbs || ['basic'];

        if (Array.isArray(exampaper.blocks)) {
            for (let block of exampaper.blocks) {
                _procssQuestions(block); // 设置questions 为 [试题id]
            }
        }

        const { period, subject, press_version, grade } = exampaper;
        // 查询试卷分类表，确定 category id
        const exampaperCategory = await db.collection('exampaper_category').findOne({
            period,
            subject,
            press_version,
            grade,
            invalid: { $in: [null, 0] }
        });
        if (!exampaperCategory) {
            return response_wrapper.error('PARAMETERS_ERROR', `当前试卷分类没有${period},${subject},${press_version},${grade}`);
        }
        exampaper.category_id = exampaperCategory.category_id;

        await db.collection('exampaper').insertOne(exampaper);
        response_wrapper.succ({ id: exampaper._id });
    } catch (e) {
        Logger.error(e);
        return response_wrapper.error('HANDLE_ERROR');
    }
};

/**
 * 创建试卷 - 电子试卷
 * date: 2024-04-29
 * @param {*} req
 * @param {*} res
 */
const createExampaper2 = async (req, res) => {
    const response_wrapper = new ResponseWrapper(res);
    const exampaper = req.body;
    const g_paper_id = req.body.g_paper_id || 0;
    const valid = validateExampaper(exampaper);
    const from = req.body.from || '';
    if (!valid) {
        return response_wrapper.error('PARAMETERS_ERROR');
    }
    try {
        const seq = await counter.nextSeq('digital_exampaper', 1);
        const now = new Date();
        exampaper.statistics = [];
        exampaper.type_score = [];
        exampaper.ctime = now;
        exampaper.is_elite = 0;
        exampaper.has_modified = 1;
        exampaper.utime = now;
        if (['drm', 'drm_yuanpei'].includes(exampaper.from)) {
            //exampaper.from = 'drm';
            delete exampaper.g_paper_id;
        } else if (['mt'].includes(exampaper.from)) {
            // 命题试卷
        } else if (['jyy'].includes(exampaper.from)) {
            // 教研云采集试题
            exampaper.from = 'jyy';
            exampaper.g_paper_id = g_paper_id;
        } else if (['xueke_zujuan', 'tiku', 'zwst', 'ai_organize_paper'].includes(exampaper.from)) {
            // 组卷网、题库、众望试题下载导入
        } else {
            exampaper.from = 'mkp';
            exampaper.g_paper_id = g_paper_id;
        }

        exampaper.download_times = 0;
        exampaper.category_id = 0;
        exampaper.view_times = 0;
        exampaper._id = seq[0];
        exampaper.dbs = exampaper.dbs || ['basic'];

        if (Array.isArray(exampaper.blocks)) {
            for (let block of exampaper.blocks) {
                _procssQuestions(block); // 设置questions 为 [试题id]
            }
        }

        if (!['xueke_zujuan'].includes(from)) exampaper.source_url = `/kb_api/v2/exampaper/${seq[0]}`;

        const { period, subject, press_version, grade } = exampaper;
        if (!['xueke_zujuan', 'tiku', 'zwst', 'ai_organize_paper'].includes(from) || press_version) {
            // 查询试卷分类表，确定 category id
            const exampaperCategory = await db.collection('exampaper_category').findOne({
                period,
                subject,
                press_version,
                grade,
                invalid: { $in: [null, 0] }
            });
            if (!exampaperCategory) {
                return response_wrapper.error('PARAMETERS_ERROR', `当前试卷分类没有${period},${subject},${press_version},${grade}`);
            }
            exampaper.category_id = exampaperCategory.category_id;
        }
        await db.collection('exampaper').insertOne(exampaper);
        response_wrapper.succ({ id: exampaper._id });
    } catch (e) {
        Logger.error(e);
        return response_wrapper.error('HANDLE_ERROR');
    }
};

/**
 * 更新试卷, 接口已经不在使用
 * @param {{body:*}} req
 * @param {{exampaperId:number}} req.params
 * @param {*} res
 * @returns {Promise<void>}
 */
const updateExampaper = async (req, res) => {
    const response_wrapper = new ResponseWrapper(res);
    const exampaperId = +req.params.exampaperId;
    if (!exampaperId) {
        return response_wrapper.error('PARAMETERS_ERROR', '试卷id必须传');
    }
    const dbs = (req.query.dbs || 'basic').split(',');
    // 非 kbp 的权限，不能修改 dbs
    if (!dbs.includes('kbp') && req.body) {
        delete req.body.dbs;
    }
    try {
        const oldExampaper = await db.collection('exampaper').findOne({ _id: exampaperId, dbs: { $in: dbs } }, { _id: 1 });
        if (!oldExampaper) {
            return response_wrapper.error('PARAMETERS_ERROR', '试卷不存在');
        }
        const exampaper = req.body;
        // 默认转换一下类型为字符串，避免其判断为整数
        //exampaper.g_paper_id = exampaper.g_paper_id.toString();

        const valid = validateExampaper(exampaper);
        const { period, subject, press_version, grade } = exampaper;

        // region 为空默认
        if (!exampaper.region) {
            exampaper.region = exampaper.province;
        }
        // 查询试卷分类表，确定 category id
        const exampaperCategory = await db.collection('exampaper_category').findOne({
            period,
            subject,
            press_version,
            grade,
            invalid: { $in: [null, 0] }
        });
        if (!exampaperCategory) {
            return response_wrapper.error('PARAMETERS_ERROR', `当前试卷分类没有${period},${subject},${press_version},${grade}`);
        }
        exampaper.category_id = exampaperCategory.category_id;


        if (!valid) {
            return response_wrapper.error('PARAMETERS_ERROR', '请求体不符合要求');
        }
        if (Array.isArray(exampaper.blocks)) {
            for (let block of exampaper.blocks) {
                _procssQuestions(block); // 设置questions
            }
        }
        // 每次人工修改该试卷后 has_modified 置为 1
        exampaper.has_modified = 1;

        //let gId = exampaper.g_paper_id;
        exampaper.utime = new Date();
        delete exampaper.g_paper_id;
        delete exampaper.statistics;
        delete exampaper.type_score;
        delete exampaper._id;
        await db.collection('exampaper').updateOne({ _id: exampaperId }, { '$set': exampaper });
        response_wrapper.succ({});
    } catch (e) {
        Logger.error(e);
        return response_wrapper.error('HANDLE_ERROR');
    }
};

/**
 * 处理试卷数据 - 直接加工传入数据
 * @param {*} exampaper 待加工试卷数据
 * @param {*} validateSchema 试卷数据 schema
 * @return {Promise<*>}
 */
const _processExampaperData = async (exampaper, validateSchema, newQuestionsObjectIdToNumberIdMap) => {
    setProvinces(exampaper);
    if (!ajv.validate(validateSchema, exampaper)) {
        throw '请求体不符合Schema规范';
    }
    // 移除前端附加临时字段 temporary_question_info
    delete exampaper['temporary_question_info'];
    // 检查分类字段
    let period = exampaper.period;
    let subject = exampaper.subject;
    let press_version = exampaper.press_version;
    let grade = exampaper.grade;

    // 考试类型 & 教材版本
    // 1.考试类型含【专题】则教材 版本改为 全部版本，不允许用户选择
    // 2. 全部版本 写入数据库是 转成 all_version
    if (~grade.indexOf('专题') && press_version !== 'all_version') {
        throw '考试类型含【专题】则教材版本必须为 all_version';
    }

    // 试卷取 category_id
    // 1. 是专题类试卷  用学段 + 学科 + grade
    // 2. 不是专题类试卷  用学段 + 学科 + 版本 + grade
    let categoryCond = ~grade.indexOf('专题')
        ? { period, subject, grade, invalid: { $in: [null, 0] } }
        : { period, subject, press_version, grade, invalid: { $in: [null, 0] } };

    const exampaperCategory = await db.collection('exampaper_category').findOne(categoryCond);
    if (!exampaperCategory) {
        throw 'Category 分类筛选项不符合规范';
    }
    exampaper.category_id = exampaperCategory.category_id;
    // 处理题块数据
    if (Array.isArray(exampaper.blocks)) {
        await Promise.all(exampaper.blocks.map(async block => {
            // 试卷试题扩展信息处理，提取试题分数
            //_processQuestionExt(block);

            // 移除前端附加临时字段 temporary_question_info
            delete block['temporary_question_info'];
            // 处理试题数据
            let questions = [];
            if (Array.isArray(block.questions)) {
                questions = await Promise.all(block.questions.map(async question => {
                    let questionId = _.isNumber(question) ? question : question.id;
                    let recoQuestions = [];
                    if (_.isObject(question)) {
                        // 移除前端附加 mark_history_ques_ids
                        delete question['mark_history_ques_ids'];
                        // 排除异常数据
                        question.reco_questions = (question.reco_questions || []).filter(e => !!e);
                        // 新增试题-关联的新创建的相似题（创建相似题）
                        recoQuestions = await Promise.all(question.reco_questions.map(async (recoQuestion) => {
                            if (recoQuestion && _.isString(recoQuestion.id)) {
                                let questionObjectId = recoQuestion.id.toString();
                                let newRecoQuestion = await questionHelper.createQuestion(_.cloneDeep(recoQuestion));
                                recoQuestion.id = newRecoQuestion._id;
                                newQuestionsObjectIdToNumberIdMap[questionObjectId] = newRecoQuestion._id;
                            }
                            return recoQuestion.id;
                        }));
                    }
                    // 新增试题（创建原题）
                    if (_.isString(questionId)) {
                        let questionObjectId = questionId.toString();
                        question.reco_questions = recoQuestions;
                        let newQuestion = await questionHelper.createQuestion(question);
                        questionId = newQuestion._id;
                        newQuestionsObjectIdToNumberIdMap[questionObjectId] = newQuestion._id;
                    }
                    // 更新已有试题的相似题
                    if (_.isNumber(questionId) && question.reco_questions) {
                        await questionHelper.setSimilarQuestions(questionId, recoQuestions);
                    }
                    let score = 0;
                    if (_.isObject(question) && question.score) {
                        score = Number(question.score) || 0;
                    }
                    return { id: questionId, score: score };
                }));
            }
            block.questions = questions;
        }));
    }
    exampaper._id = exampaper.id;
    delete exampaper.id;
    if (!exampaper._id) {
        throw '缺少_id';
    }
    return exampaper;
};

/**
 * 创建试卷 - 试卷库
 * @param {*} req
 * @param {*} res
 * @returns {Promise<void>}
 */
const createExampaperInstance = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    const exampaper = req.body;
    let newQuestionsObjectIdToNumberIdMap = {};
    try {
        await _processExampaperData(exampaper, createKbExampaperSchema, newQuestionsObjectIdToNumberIdMap);
        // 补充新增基础字段
        exampaper.ctime = exampaper.utime = new Date();
        exampaper.statistics = [];
        exampaper.type_score = [];

        await db.collection('exampaper').insertOne(exampaper);
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
    responseWrapper.succ({ exampaper, new_questions_ids_map: newQuestionsObjectIdToNumberIdMap });
};

/**
 * 编辑试卷 - 试卷库
 * @param {*} req
 * @param {*} res
 * @returns {Promise<void>}
 */
const updateExampaperInstance = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    const exampaper = req.body;
    let newQuestionsObjectIdToNumberIdMap = {};
    try {
        await _processExampaperData(exampaper, createKbExampaperSchema, newQuestionsObjectIdToNumberIdMap);
        let oldExampaper = await db.collection('exampaper').findOne({ _id: exampaper._id });
        if (!oldExampaper) {
            responseWrapper.error('NULL_ERROR', '无对应 ID 试卷');
        }
        // 补充修改基础字段
        exampaper.ctime = oldExampaper.ctime;
        exampaper.utime = new Date();
        exampaper.statistics = oldExampaper.statistics;
        exampaper.type_score = oldExampaper.type_score;
        // 每次人工修改该试卷后 has_modified 置为 1
        exampaper.has_modified = 1;

        // 删除 _id
        let _id = exampaper._id;
        // 移除不希望更改的字段
        let setExampaper = _.omit(exampaper, ['_id', 'user_id', 'user_name', 'from']);

        await db.collection('exampaper').updateOne({ _id: _id }, { '$set': setExampaper });
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
    responseWrapper.succ({ exampaper, new_questions_ids_map: newQuestionsObjectIdToNumberIdMap });
};

/**
 * 创建好分数试卷 - 试卷库
 * @param {*} req 请求 body 为试卷数据
 * @param {*} res
 * @returns {Promise<void>}
 */
const createHfsExampaperInstance = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    const exampaper = req.body;
    let newQuestionsObjectIdToNumberIdMap = {};
    try {
        await _processExampaperData(exampaper, createHfsExampaperSchema, newQuestionsObjectIdToNumberIdMap);
        // 补充新增基础字段
        exampaper.ctime = exampaper.utime = new Date();
        exampaper.statistics = [];
        exampaper.type_score = [];
        exampaper.from = 'mkp';

        await db.collection('exampaper').insertOne(exampaper);
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
    responseWrapper.succ({ exampaper, new_questions_ids_map: newQuestionsObjectIdToNumberIdMap });
};

/**
 * 编辑好分数试卷 - 试卷库
 * @param {*} req 请求 body 为试卷数据
 * @param {*} res
 * @returns {Promise<void>}
 */
const updateHfsExampaperInstance = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    const exampaper = req.body;
    let newQuestionsObjectIdToNumberIdMap = {};
    try {
        await _processExampaperData(exampaper, createHfsExampaperSchema, newQuestionsObjectIdToNumberIdMap);
        let oldExampaper = await db.collection('exampaper').findOne({ _id: exampaper._id });
        if (!oldExampaper) {
            responseWrapper.error('NULL_ERROR', '无对应 ID 试卷');
        }
        // 补充修改基础字段
        exampaper.ctime = oldExampaper.ctime;
        exampaper.utime = new Date();
        exampaper.statistics = oldExampaper.statistics;
        exampaper.type_score = oldExampaper.type_score;
        // 每次人工修改该试卷后 has_modified 置为 1
        exampaper.has_modified = 1;

        let updateObj = {};
        if (oldExampaper.source === 'digital_exampaper') {
            exampaper.from = 'digital_exampaper';
            updateObj['$unset'] = { source: 1 };
        }
        updateObj['$set'] = _.omit(exampaper, ['_id', 'user_id', 'user_name', 'g_paper_id']);

        await db.collection('exampaper').updateOne({ _id: exampaper._id }, updateObj);
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
    responseWrapper.succ({ exampaper, new_questions_ids_map: newQuestionsObjectIdToNumberIdMap });
};

/**
 * 修改试卷使用类型
 * @param {*} req 请求 body 为试卷数据
 * @param {*} res
 * @returns {Promise<void>}
 */
const updateExampaperUseType = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    const exampaperId = req.params.exampaper_id, { use_type } = req.body;
    if (!exampaperId || !use_type) {
        return responseWrapper.error('PARAMETERS_ERROR', '试卷id、用途必须传');
    }
    try {
        let oldExampaper = await db.collection('exampaper').findOne({ _id: Number(exampaperId) });
        if (!oldExampaper) {
            return responseWrapper.error('NULL_ERROR', '无对应 ID 试卷');
        }
        if (oldExampaper.from !== 'mkp' || !['ctb', 'digital'].includes(use_type)) {
            return responseWrapper.error('NULL_ERROR', '试卷来源或用途类型错误！');
        }
        let updateObj = { $set: { use_type: use_type } };
        await db.collection('exampaper').updateOne({ _id: Number(exampaperId) }, updateObj);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e);
    }
    responseWrapper.succ({ id: exampaperId });
};

/**
 * 命题平台更新试卷
 * @param {{body:*}} req
 * @param {{exampaperId:number}} req.params
 * @param {*} res
 * @returns {Promise<void>}
 */
const updateMtExampaper = async (req, res) => {
    const response_wrapper = new ResponseWrapper(res);
    const exampaperId = parseInt(req.params.exampaperId);
    const dbs = (req.query.dbs || 'basic').split(',');
    // 只有kbp平台可以修改dbs字段
    if (!dbs.includes('kbp') && req.body) {
        delete req.body.dbs;
    }
    if (!exampaperId) {
        return response_wrapper.error('PARAMETERS_ERROR', '试卷id必须传');
    }
    try {
        // 判断用户是否有权限更改该试卷
        const oldExampaper = await db.collection('exampaper').findOne({ _id: exampaperId, dbs: { $in: dbs } }, { _id: 1 });
        if (!oldExampaper) {
            return response_wrapper.error('NULL_ERROR', '试卷不存在');
        }
        const exampaper = req.body;

        // 默认转换一下类型为字符串，避免其判断为整数
        //exampaper.g_paper_id = exampaper.g_paper_id.toString();

        const valid = validateExampaper(exampaper);
        const { period, subject, press_version, grade } = exampaper;

        // region 为空默认
        if (!exampaper.region) {
            exampaper.region = exampaper.province;
        }
        // 查询试卷分类表，确定 category id
        const exampaperCategory = await db.collection('exampaper_category').findOne({
            period,
            subject,
            press_version,
            grade,
            invalid: { $in: [null, 0] }
        });
        if (!exampaperCategory) {
            return response_wrapper.error('PARAMETERS_ERROR', `当前试卷分类没有${period},${subject},${press_version},${grade}`);
        }
        exampaper.category_id = exampaperCategory.category_id;


        if (!valid) {
            return response_wrapper.error('PARAMETERS_ERROR', '请求体不符合要求');
        }
        if (Array.isArray(exampaper.blocks)) {
            for (let block of exampaper.blocks) {
                //_processQuestionExt(block); // 添加questions_ext
                _procssQuestions(block); // 设置questions
            }
        }
        // 每次人工修改该试卷后 has_modified 置为 1
        exampaper.has_modified = 1;

        //let gId = exampaper.g_paper_id;
        exampaper.utime = new Date();
        delete exampaper.g_paper_id;
        delete exampaper.statistics;
        delete exampaper.type_score;
        delete exampaper._id;
        await db.collection('exampaper').updateOne({ _id: exampaperId }, { '$set': exampaper });
        response_wrapper.succ({});
    } catch (e) {
        Logger.error(e);
        return response_wrapper.error('HANDLE_ERROR');
    }
};

const _processExampaperUnitData = async (exampaper, validateSchema, newQuestionsObjectIdToNumberIdMap) => {
    // 处理题块数据
    if (Array.isArray(exampaper.blocks)) {
        await Promise.all(exampaper.blocks.map(async block => {
            // 处理试题数据
            let questions = [];
            if (Array.isArray(block.questions)) {
                questions = await Promise.all(block.questions.map(async question => {
                    let questionId = _.isNumber(question) ? question : question.id;
                    let recoQuestions = [];
                    if (_.isObject(question)) {
                        // 排除异常数据
                        question.reco_questions = (question.reco_questions || []).filter(e => !!e);
                        // 新增试题-关联的新创建的相似题（创建相似题）
                        recoQuestions = await Promise.all(question.reco_questions.map(async (recoQuestion) => {
                            if (recoQuestion && _.isString(recoQuestion.id)) {
                                let questionObjectId = recoQuestion.id.toString();
                                let newRecoQuestion = await questionHelper.createQuestion(_.cloneDeep(recoQuestion));
                                recoQuestion.id = newRecoQuestion._id;
                                newQuestionsObjectIdToNumberIdMap[questionObjectId] = newRecoQuestion._id;
                            }
                            return recoQuestion.id;
                        }));
                    }
                    // 新增试题（创建原题）
                    if (_.isString(questionId)) {
                        let questionObjectId = questionId.toString();
                        question.reco_questions = recoQuestions;
                        let newQuestion = await questionHelper.createQuestion(question);
                        questionId = newQuestion._id;
                        newQuestionsObjectIdToNumberIdMap[questionObjectId] = newQuestion._id;
                    }
                    // // 更新已有试题的相似题
                    // if (_.isNumber(questionId) && question.reco_questions) {
                    //     await questionHelper.setSimilarQuestions(questionId, recoQuestions);
                    // }
                    let score = 0;
                    if (_.isObject(question) && question.score) {
                        score = Number(question.score) || 0;
                    }
                    return { id: questionId, score: score };
                }));
            }
            block.questions = questions;
        }));
    }
    if (exampaper.exampaper_id) {
        exampaper._id = exampaper.exampaper_id;
    }
    delete exampaper.exampaper_id;
    if (!exampaper._id) {
        throw '缺少_id';
    }
    return exampaper;
};

const createExampaperAlbum = async (req, res) => {
    const response_wrapper = new ResponseWrapper(res);
    const exampaper = req.body;
    const valid = validateCreateExampaperUnit(exampaper);
    let newQuestionsObjectIdToNumberIdMap = {};
    if (!valid) {
        return response_wrapper.error('PARAMETERS_ERROR');
    }
    try {
        const seq = await counter.nextSeq('digital_exampaper', 1);
        const now = new Date();
        exampaper.statistics = exampaper.statistics ? exampaper.statistics : [];
        exampaper.type_score = exampaper.type_score ? exampaper.type_score : [];
        exampaper.ctime = now;
        exampaper.has_modified = 1;
        exampaper.utime = now;
        exampaper.rtime = now;
        exampaper.is_show = true;
        exampaper.source_url = `/kb_api/v2/exampaper/${seq[0]}`;
        exampaper.download_times = 0;
        exampaper.view_times = 0;
        exampaper.exampaper_id = seq[0];
        exampaper.category_id = Number(exampaper.category_id);
        exampaper.from = 'kbp_new';
        exampaper.dbs = (exampaper.dbs && exampaper.dbs.length === 0) ? exampaper.dbs : ['basic'];
        await _processExampaperUnitData(exampaper, createKbExampaperSchema, newQuestionsObjectIdToNumberIdMap);
        if (Array.isArray(exampaper.blocks)) {
            for (let block of exampaper.blocks) {
                _procssQuestions(block); // 设置questions 为 [试题id]
            }
        }
        const exampaperCategory = await db.collection('book').findOne({ _id: Number(exampaper.category_id) }, { _id: 1 });
        if (!exampaperCategory._id) {
            return response_wrapper.error('PARAMETERS_ERROR', `教材中没有不存在该教材${exampaper.category_id}`);
        }

        if (exampaper.album_type === '单元测') {
            // 单元测试卷会操作album表
            await exampaperUnitEnterAlbum(exampaper, now);
        }

        await db.collection('exampaper').insertOne(exampaper);
        response_wrapper.succ({ id: exampaper._id });
    } catch (e) {
        Logger.error(e);
        return response_wrapper.error('HANDLE_ERROR');
    }
};

/**
 * 单元测试卷修改album表
 * @param exampaper
 * @param now
 * @returns {Promise<*>}
 */
const exampaperUnitEnterAlbum = async (exampaper, now) => {
    try {
        const resAlbum = await db.collection('album').findOne({
            book_id: exampaper.category_id
        });

        if (resAlbum) {
            let bool = resAlbum.chapter_exam.some((elem) => {
                return elem[exampaper.chapter_id] ? elem[exampaper.chapter_id] : null;
            });
            if (bool) {
                exampaper.is_show = false;
            } else {
                await db.collection('album').updateOne({ book_id: exampaper.category_id }, {
                    $set: { utime: now, cyear: exampaper.cyear },
                    $push: { chapter_exam: { [exampaper.chapter_id]: exampaper._id } }
                });
            }
        } else {
            const data = {
                period: exampaper.period,
                subject: exampaper.subject,
                grade: exampaper.grade,
                book_id: exampaper.category_id,
                press_version: exampaper.press_version,
                download_times: 0,
                view_times: 0,
                cyear: exampaper.cyear,
                album_type: exampaper.album_type,
                ctime: now,
                utime: now
            };
            data.chapter_exam = [{ [exampaper.chapter_id]: exampaper._id }];
            await db.collection('album').insertOne(data);
        }
    } catch (e) {
        return e;
    }
};

const updateExampaperAlbum = async (req, res) => {
    const response_wrapper = new ResponseWrapper(res);
    const exampaperId = parseInt(req.params.exampaper_id);
    let newQuestionsObjectIdToNumberIdMap = {};
    if (!exampaperId) {
        return response_wrapper.error('PARAMETERS_ERROR', '试卷id必须传');
    }
    const dbs = (req.query.dbs || 'basic').split(',');
    if (!dbs.includes('kbp') && req.body) {
        delete req.body.dbs;
    }
    try {
        // dbs 过滤
        const oldExampaper = await db.collection('exampaper').findOne({ _id: exampaperId, dbs: { $in: dbs } }, { _id: 1 });
        if (!oldExampaper) {
            return response_wrapper.error('NULL_ERROR', '试卷不存在');
        }
        const exampaper = req.body;

        await _processExampaperUnitData(exampaper, createKbExampaperSchema, newQuestionsObjectIdToNumberIdMap);
        const valid = validateExampaperUnit(exampaper);
        if (!valid) {
            return response_wrapper.error('PARAMETERS_ERROR', '请求体不符合要求');
        }
        // 查询试卷分类表，确定 category id
        const exampaperCategory = await db.collection('book').findOne({ _id: Number(exampaper.category_id) }, { _id: 1 });
        if (!exampaperCategory._id) {
            return response_wrapper.error('PARAMETERS_ERROR', `教材中没有不存在该教材${exampaper.category_id}`);
        }
        const result = await db.collection('exampaper').findOne({ _id: exampaperId });
        if (!result) {
            return response_wrapper.error('NULL_ERROR', '数据不存在');
        }
        if (Array.isArray(exampaper.blocks)) {
            for (let block of exampaper.blocks) {
                _procssQuestions(block); // 设置questions
            }
        }
        exampaper.has_modified = 1;
        exampaper.utime = new Date();
        exampaper.rtime = new Date();
        await db.collection('exampaper').updateOne({ _id: exampaperId }, { '$set': exampaper });
        return response_wrapper.succ({});
    } catch (e) {
        Logger.error(e);
        return response_wrapper.error('HANDLE_ERROR');
    }
};

const updateExampaperStatus = async (req, res) => {
    const response_wrapper = new ResponseWrapper(res);
    let albumType = req.params.album_type;
    let dbs = req.body.dbs || ['basic'];
    if (!['AI', 'unit'].includes(albumType)) {
        return response_wrapper.error('PARAMETERS_ERROR', '专辑类型参数错误');
    }
    let isShow = req.body.is_show;
    let exampaperId = parseInt(req.body.exampaper_id);
    if (!exampaperId || isShow === undefined) {
        return response_wrapper.error('PARAMETERS_ERROR', '缺少公共必要字段');
    }

    try {
        // dbs 过滤
        const resExam = await db.collection('exampaper').findOne({ _id: exampaperId, dbs: { $in: dbs } });
        if (!resExam) {
            return response_wrapper.error('NULL_ERROR', '试卷不存在');
        }

        if (albumType === 'unit') {
            let chapterId = parseInt(req.body.chapter_id);
            let categoryId = parseInt(req.body.category_id);
            if (!chapterId || !categoryId) {
                return response_wrapper.error('PARAMETERS_ERROR', '缺少单元测必要字段');
            }
            let result = await updateExampaperStatusForUnit(resExam, exampaperId, isShow, categoryId, chapterId);
            if (result === 'exists') {
                return response_wrapper.succ({ data: 'exists' });
            }
            if (result === 'direct_pass') {
                return response_wrapper.succ({});
            }
        }

        await db.collection('exampaper').updateOne({ _id: exampaperId }, { $set: { is_show: isShow, utime: new Date() } });
        return response_wrapper.succ({});
    } catch (e) {
        Logger.error(e);
        return response_wrapper.error('HANDLE_ERROR', e);
    }
};

/**
 * 处理单元测上下架
 * @param resExam
 * @param exampaperId
 * @param isShow
 * @param categoryId
 * @param chapterId
 * @returns {Promise<string|*>}
 */
const updateExampaperStatusForUnit = async (resExam, exampaperId, isShow, categoryId, chapterId) => {
    try {
        if (isShow === true) {  // 上架
            const data = {
                period: resExam.period,
                subject: resExam.subject,
                grade: resExam.grade,
                book_id: resExam.category_id,
                press_version: resExam.press_version,
                album_type: resExam.album_type,
                cyear: resExam.cyear
            };
            let resAlbum = await db.collection('album').findOne({ book_id: categoryId });
            if (resAlbum) {
                for (let chap of resAlbum.chapter_exam) {
                    let one_chap_id = Object.keys(chap)[0];
                    let one_exam_id = Object.values(chap)[0];
                    if (parseInt(one_chap_id) === parseInt(chapterId)) {
                        if (parseInt(one_exam_id) === parseInt(exampaperId)) {
                            // return response_wrapper.succ({});
                            return 'direct_pass';
                        }
                        if (one_exam_id) {
                            // return response_wrapper.succ({ data: 'No' });
                            return 'exists';
                        }
                    }
                }
                await db.collection('album').updateOne({ book_id: categoryId }, {
                    $set: { utime: new Date(), cyear: resExam.cyear },
                    $push: { chapter_exam: { [chapterId]: exampaperId } }
                });
            } else { // 新建
                data.download_times = 0;
                data.view_times = 0;
                data.chapter_exam = [{ [chapterId]: exampaperId }];
                data.ctime = resExam.ctime;
                data.utime = resExam.utime;
                await db.collection('album').insertOne(data);
            }
        } else { // 下架
            let resAlbum = await db.collection('album').findOne({ book_id: categoryId });
            if (!resAlbum) {
                // return response_wrapper.error('NULL_ERROR', '试卷不存在');
                throw new Error('album不存在此记录');
            }
            await db.collection('album').updateOne({ book_id: categoryId }, {
                $set: { utime: new Date() },
                $pull: { chapter_exam: { [chapterId]: exampaperId } }
            });
        }
        return 'success';
    } catch (e) {
        return e;
    }
};

const getExampaperAlbumById = async (req, res) => {
    const response_wrapper = new ResponseWrapper(res);
    const examId = parseInt(req.params.exampaper_id);
    const albumType = req.query.album_type;
    const dbs = (req.query.dbs || 'basic').split(',');
    if (!examId) {
        return response_wrapper.error('PARAMETERS_ERROR', 'exampaper_id 为必传字段');
    }
    if (!albumType) {
        return response_wrapper.error('PARAMETERS_ERROR', 'album_type 为必传字段');
    }
    try {
        const result = await db.collection('exampaper').findOne({ _id: examId, album_type: albumType, dbs: { $in: dbs } });
        return response_wrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return response_wrapper.error('HANDLE_ERROR');
    }
};

/**
 * 获取AI测评卷/单元测名称是否存在
 * @param req
 * @param res
 * @returns {Promise<*>}
 */
const getExampaperAlbumIsExists = async (req, res) => {
    const response_wrapper = new ResponseWrapper(res);
    let examName = req.query.exampaper_name.toString();
    let albumType = req.query.album_type.toString();
    let examId = Number(req.query.exampaper_id);
    const dbs = (req.query.dbs || 'basic').split(',');
    let exists = false;
    try {
        let cond = { album_type: albumType, name: examName, dbs: { $in: dbs } };
        if (examId) {
            // 不与自身校验
            cond['_id'] = { $ne: examId };
        }
        let result = await db.collection('exampaper').findOne(cond, { _id: 1 });
        if (result) {
            exists = true;
        }
        return response_wrapper.succ({ exists });
    } catch (e) {
        Logger.error(e);
        return response_wrapper.error('HANDLE_ERROR');
    }
}

/**
 * 获取试卷年级列表
 * @param req
 * @param res
 */
const getExampaperGradeList = (req, res) => {
    const response_wrapper = new ResponseWrapper(res);
    const gradeSemester = {
        'periods': [
            {
                'name': '小学',
                'grades': [
                    {
                        'name': '一年级上'
                    },
                    {
                        'name': '一年级下'
                    },
                    {
                        'name': '二年级上'
                    },
                    {
                        'name': '二年级下'
                    },
                    {
                        'name': '三年级上'
                    },
                    {
                        'name': '三年级下'
                    },
                    {
                        'name': '四年级上'
                    },
                    {
                        'name': '四年级下'
                    },
                    {
                        'name': '五年级上'
                    },
                    {
                        'name': '五年级下'
                    },
                    {
                        'name': '六年级上'
                    },
                    {
                        'name': '六年级下'
                    }
                ]
            },
            {
                'name': '初中',
                'grades': [
                    {
                        'name': '七年级上'
                    },
                    {
                        'name': '七年级下'
                    },
                    {
                        'name': '八年级上'
                    },
                    {
                        'name': '八年级下'
                    },
                    {
                        'name': '九年级上'
                    },
                    {
                        'name': '九年级下'
                    }
                ]
            },
            {
                'name': '高中',
                'grades': [
                    {
                        'name': '高一上'
                    },
                    {
                        'name': '高一下'
                    },
                    {
                        'name': '高二上'
                    },
                    {
                        'name': '高二下'
                    },
                    {
                        'name': '高三上'
                    },
                    {
                        'name': '高三下'
                    }
                ]
            }
        ]
    };
    return response_wrapper.succ(gradeSemester);
};

module.exports = {
    createExampaper,
    createExampaper2,
    updateExampaper,
    createExampaperInstance,
    createExampaperAlbum: createExampaperAlbum,
    createHfsExampaperInstance,
    updateExampaperInstance,
    updateExampaperAlbum: updateExampaperAlbum,
    updateExampaperStatus: updateExampaperStatus,
    updateHfsExampaperInstance,
    updateExampaperUseType,
    updateMtExampaper,
    getExampaperAlbumById: getExampaperAlbumById,
    getExampaperAlbumIsExists,
    getExampaperGradeList,
};
