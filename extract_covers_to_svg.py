#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教辅封面提取工具 - 将HTML中的封面设计转换为SVG格式
"""

import re
import os
from typing import Dict, List, Tuple

def parse_gradient(gradient_str: str) -> str:
    """解析CSS渐变并转换为SVG格式"""
    # 提取linear-gradient参数
    match = re.search(r'linear-gradient\(([^)]+)\)', gradient_str)
    if not match:
        return '#333333'
    
    params = match.group(1).split(',')
    angle = params[0].strip()
    colors = [color.strip() for color in params[1:]]
    
    # 转换角度为SVG坐标
    if '135deg' in angle:
        x1, y1, x2, y2 = "0%", "0%", "100%", "100%"
    elif '90deg' in angle:
        x1, y1, x2, y2 = "0%", "0%", "0%", "100%"
    else:
        x1, y1, x2, y2 = "0%", "0%", "100%", "0%"
    
    # 创建SVG渐变定义
    gradient_id = f"grad_{hash(gradient_str) % 10000}"
    gradient_def = f'''
    <defs>
        <linearGradient id="{gradient_id}" x1="{x1}" y1="{y1}" x2="{x2}" y2="{y2}">
            <stop offset="0%" style="stop-color:{colors[0]};stop-opacity:1" />
            <stop offset="100%" style="stop-color:{colors[1] if len(colors) > 1 else colors[0]};stop-opacity:1" />
        </linearGradient>
    </defs>'''
    
    return gradient_def, f"url(#{gradient_id})"

def create_decorative_pattern(subject: str, variant: str) -> str:
    """根据科目和变体创建装饰图案，精确还原原始HTML样式"""
    patterns = {
        'chinese': {
            'variant-1': '''
                <defs>
                    <pattern id="chineseGrid1" patternUnits="userSpaceOnUse" width="15" height="15">
                        <path d="M 15 0 L 0 0 0 15" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                    </pattern>
                </defs>
                <rect x="0" y="0" width="120" height="150" fill="url(#chineseGrid1)" opacity="0.08"/>
            ''',
            'variant-2': '<circle cx="95" cy="125" r="35" fill="white" opacity="0.1"/>',
            'variant-3': '''
                <defs>
                    <radialGradient id="chineseRadial3" cx="50%" cy="50%">
                        <stop offset="0%" style="stop-color:rgba(255,255,255,0.2);stop-opacity:1" />
                        <stop offset="70%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
                    </radialGradient>
                </defs>
                <ellipse cx="60" cy="75" rx="55" ry="55" fill="url(#chineseRadial3)" transform="rotate(20 60 75)"/>
            '''
        },
        'math': {
            'variant-1': '''
                <defs>
                    <pattern id="mathGrid1" patternUnits="userSpaceOnUse" width="12" height="12">
                        <path d="M 12 0 L 0 0 0 12" fill="none" stroke="rgba(255,255,255,0.5)" stroke-width="1"/>
                    </pattern>
                </defs>
                <rect x="0" y="0" width="120" height="150" fill="url(#mathGrid1)" opacity="0.1"/>
            ''',
            'variant-2': '<circle cx="-10" cy="180" r="40" fill="none" stroke="rgba(255,255,255,0.5)" stroke-width="8" opacity="0.15"/>',
            'variant-3': '''
                <defs>
                    <pattern id="mathDiagonal3" patternUnits="userSpaceOnUse" width="16" height="16" patternTransform="rotate(45)">
                        <rect x="0" y="0" width="8" height="16" fill="rgba(255,255,255,0.2)"/>
                    </pattern>
                </defs>
                <rect x="-120" y="-75" width="360" height="300" fill="url(#mathDiagonal3)" opacity="0.05"/>
            '''
        },
        'english': {
            'variant-1': '<text x="60" y="90" font-family="Arial, sans-serif" font-size="50" font-weight="700" fill="white" opacity="0.07" text-anchor="middle" transform="rotate(-15 60 75)">ABC</text>',
            'variant-2': '''
                <defs>
                    <pattern id="englishStripes2" patternUnits="userSpaceOnUse" width="30" height="100%">
                        <rect x="0" y="0" width="15" height="100%" fill="rgba(255,255,255,0.3)"/>
                    </pattern>
                </defs>
                <rect x="0" y="105" width="120" height="45" fill="url(#englishStripes2)" opacity="0.1"/>
            ''',
            'variant-3': '<polygon points="75,150 85,150 85,40 120,40 120,40 120,150 120,150 75,150" fill="white" opacity="0.05"/>'
        },
        'physics': {
            'variant-1': '''
                <circle cx="60" cy="75" r="55" fill="none" stroke="white" stroke-width="2" opacity="0.15"/>
                <circle cx="60" cy="75" r="55" fill="none" stroke="white" stroke-width="2" opacity="0.15" transform="rotate(60 60 75)"/>
            ''',
            'variant-2': '''
                <defs>
                    <pattern id="physicsGrid2" patternUnits="userSpaceOnUse" width="30" height="30">
                        <path d="M 30 0 L 0 0 0 30" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
                    </pattern>
                </defs>
                <rect x="0" y="0" width="120" height="150" fill="url(#physicsGrid2)" opacity="0.1"/>
            '''
        },
        'chemistry': {
            'variant-1': '<polygon points="100,20 120,32.5 120,57.5 100,70 80,57.5 80,32.5" fill="rgba(255,255,255,0.2)" opacity="0.2"/>',
            'variant-2': '<circle cx="25" cy="135" r="35" fill="none" stroke="white" stroke-width="3" opacity="0.1"/>'
        },
        'biology': {
            'variant-1': '''
                <path d="M60,10 C80,35 40,60 80,85 C40,110 80,135 60,160" stroke="white" stroke-width="4" fill="none" opacity="0.15"/>
                <path d="M80,10 C60,35 100,60 60,85 C100,110 60,135 80,160" stroke="white" stroke-width="4" fill="none" opacity="0.15"/>
            ''',
            'variant-2': '''
                <defs>
                    <pattern id="bioLines2" patternUnits="userSpaceOnUse" width="6" height="6" patternTransform="rotate(15)">
                        <rect x="0" y="0" width="1" height="6" fill="rgba(255,255,255,0.1)"/>
                    </pattern>
                </defs>
                <rect x="-37.5" y="-37.5" width="195" height="225" fill="url(#bioLines2)" opacity="0.2"/>
            '''
        },
        'history': {
            'variant-1': '<rect x="0" y="0" width="120" height="150" fill="#fdf6e3" opacity="0.08"/>',
            'variant-2': '''
                <rect x="0" y="60" width="120" height="30" fill="rgba(255,255,255,0.1)"/>
                <line x1="0" y1="60" x2="120" y2="60" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
                <line x1="0" y1="90" x2="120" y2="90" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
            '''
        },
        'geography': {
            'variant-1': '''
                <circle cx="140" cy="15" r="55" fill="none" stroke="white" stroke-width="1" opacity="0.1"/>
                <circle cx="140" cy="15" r="48" fill="none" stroke="white" stroke-width="1" opacity="0.1"/>
                <circle cx="140" cy="15" r="41" fill="none" stroke="white" stroke-width="1" opacity="0.1"/>
            ''',
            'variant-2': '''
                <defs>
                    <radialGradient id="geoRadial2" cx="50%" cy="50%">
                        <stop offset="40%" style="stop-color:transparent;stop-opacity:0" />
                        <stop offset="41%" style="stop-color:rgba(255,255,255,0.2);stop-opacity:1" />
                        <stop offset="42%" style="stop-color:rgba(255,255,255,0.2);stop-opacity:1" />
                        <stop offset="43%" style="stop-color:transparent;stop-opacity:0" />
                        <stop offset="60%" style="stop-color:transparent;stop-opacity:0" />
                        <stop offset="61%" style="stop-color:rgba(255,255,255,0.2);stop-opacity:1" />
                        <stop offset="62%" style="stop-color:rgba(255,255,255,0.2);stop-opacity:1" />
                        <stop offset="63%" style="stop-color:transparent;stop-opacity:0" />
                    </radialGradient>
                </defs>
                <circle cx="60" cy="75" r="70" fill="url(#geoRadial2)" opacity="0.2"/>
            '''
        },
        'politics': {
            'variant-1': '''
                <defs>
                    <radialGradient id="politicsRadial1" cx="50%" cy="50%">
                        <stop offset="0%" style="stop-color:rgba(255,215,0,0.2);stop-opacity:1" />
                        <stop offset="60%" style="stop-color:transparent;stop-opacity:0" />
                    </radialGradient>
                </defs>
                <circle cx="-60" cy="-60" r="80" fill="url(#politicsRadial1)" opacity="0.5"/>
            ''',
            'variant-2': '<text x="110" y="50" font-family="Arial, sans-serif" font-size="50" fill="white" opacity="0.15" text-anchor="middle" transform="rotate(15 110 50)">★</text>'
        }
    }

    return patterns.get(subject, {}).get(variant, '')

def create_svg_cover(subject_name: str, subject_english: str, gradient_colors: str, subject_key: str, variant: str) -> str:
    """创建单个封面的SVG，精确还原原始HTML样式"""

    # 解析渐变
    gradient_def, fill_color = parse_gradient(gradient_colors)

    # 获取装饰图案
    pattern = create_decorative_pattern(subject_key, variant)

    # 处理特殊的三色渐变（物理variant-1）
    if subject_key == 'physics' and variant == 'variant-1':
        gradient_def = '''
        <defs>
            <linearGradient id="grad_physics_1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#0f2027;stop-opacity:1" />
                <stop offset="50%" style="stop-color:#203a43;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#2c5364;stop-opacity:1" />
            </linearGradient>
        </defs>'''
        fill_color = "url(#grad_physics_1)"

    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
    {gradient_def}

    <!-- 主背景 -->
    <rect width="120" height="150" fill="{fill_color}" rx="6"/>

    <!-- 装饰图案 -->
    {pattern}

    <!-- 顶部标题区域背景 -->
    <rect x="0" y="0" width="120" height="18" fill="rgba(255,255,255,0.1)" rx="6 6 0 0"/>

    <!-- 标题分隔线 -->
    <line x1="10" y1="18" x2="110" y2="18" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>

    <!-- 标题文字 -->
    <text x="60" y="12" font-family="Noto Sans SC, Helvetica Neue, Arial, sans-serif"
          font-size="9" font-weight="500" fill="white" text-anchor="middle"
          letter-spacing="0.5px" style="text-transform: uppercase;">{subject_name} | {subject_english}</text>

    <!-- 底部区域 -->
    <rect x="0" y="125" width="120" height="25" fill="rgba(0,0,0,0.1)" rx="0 0 6 6"/>

    <!-- 外边框 -->
    <rect width="120" height="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5" rx="6"/>

    <!-- 阴影效果（模拟box-shadow） -->
    <defs>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.1)"/>
            <feDropShadow dx="0" dy="5" stdDeviation="5" flood-color="rgba(0,0,0,0.1)"/>
        </filter>
    </defs>
    <rect width="120" height="150" fill="none" rx="6" style="filter: url(#shadow);"/>
</svg>'''

    return svg_content

def extract_subject_styles(html_content: str) -> Dict[str, Dict[str, str]]:
    """从HTML中提取各科目的样式信息"""
    subjects = {}
    
    # 科目映射
    subject_mapping = {
        'chinese': ('语文', 'CHINESE'),
        'math': ('数学', 'MATHEMATICS'),
        'english': ('英语', 'ENGLISH'),
        'physics': ('物理', 'PHYSICS'),
        'chemistry': ('化学', 'CHEMISTRY'),
        'biology': ('生物', 'BIOLOGY'),
        'history': ('历史', 'HISTORY'),
        'geography': ('地理', 'GEOGRAPHY'),
        'politics': ('政治', 'POLITICS')
    }
    
    # 提取样式规则
    style_pattern = r'\.subject-(\w+)\.variant-(\d+)\s*\{\s*background:\s*([^;]+);'
    matches = re.findall(style_pattern, html_content)
    
    for subject_key, variant, gradient in matches:
        if subject_key not in subjects:
            subjects[subject_key] = {}
        subjects[subject_key][f'variant-{variant}'] = gradient.strip()
    
    return subjects, subject_mapping

def main():
    """主函数"""
    # 读取HTML文件
    with open('教辅封面2.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 提取样式信息
    subjects, subject_mapping = extract_subject_styles(html_content)
    
    # 创建输出目录
    output_dir = 'svg_covers'
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成SVG文件
    for subject_key, variants in subjects.items():
        if subject_key in subject_mapping:
            subject_name, subject_english = subject_mapping[subject_key]
            
            for variant, gradient in variants.items():
                svg_content = create_svg_cover(
                    subject_name, subject_english, gradient, subject_key, variant
                )
                
                filename = f"{subject_name}_{variant}.svg"
                filepath = os.path.join(output_dir, filename)
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(svg_content)
                
                print(f"已生成: {filepath}")
    
    print(f"\n所有SVG文件已生成到 {output_dir} 目录中")

if __name__ == "__main__":
    main()
