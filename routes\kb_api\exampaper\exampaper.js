// dmp试卷路由
const exampaper = require('../../../modules/kb_api/v2/exampaper');

module.exports = function (router) {
    // 获取试卷资源的统计信息
    router.get('/exampapers/filters/', exampaper.filters);
    // 获取试卷资源的统计信息
    router.get('/exampapers/profile/', exampaper.profile);
    // 获取最新更新的试卷
    router.get('/exampapers/recently/', exampaper.recently);
    // 获取试卷分类信息
    router.get('/exampapers/categorys/', exampaper.exampaper_cats);
    // 通过试卷分类id获取试卷列表
    router.get('/exampapers/categorys/:category_id/', exampaper.exampaper_category);
    // 知识点/细分/对象获取试题
    router.post('/exampapers/by_search', exampaper.exampaperBySearch);
    // 根据试卷id取试卷内容信息
    router.get('/exampapers/:exampaper_id/', exampaper.exampaper);
    router.get('/exampapers/no_view/:exampaper_id/', exampaper.exampaperNoView);
    router.get('/exampapers', exampaper.getExampapersByCondition);

    router.get('/exampapers/:exampaper_ids/list', exampaper.getExampapersByIds);
    router.get('/exampapers/:exampaper_ids/abstract', exampaper.getExampapersAbstract);
    // 根据试卷 id 数组查询对应的创建时间
    router.get('/exampapers/ctimes/:exampaper_ids', exampaper.getExampaperCtimeByIds);

    router.post('/assemble/exampaper', exampaper.createExampaperByKnowledges);
    router.post('/assemble/knowledge/question_num/', exampaper.getQuestionNumByKnowledge);
    // 组卷生成word
    router.post('/assemble/exampaper/download/', exampaper.assembleExampaper);
    //router.get('/exampaper/download/', exampaper.download);
    // 根据试卷id, 下载相应word文件
    router.get('/exampapers/:exampaper_id/download/', exampaper.downloadExampaper);

    router.post('/exampapers/instance', exampaper.createExampaperInstance);
    router.put('/exampapers/instance', exampaper.updateExampaperInstance);

    router.post('/exampapers/hfs/instance', exampaper.createHfsExampaperInstance);
    router.put('/exampapers/hfs/instance', exampaper.updateHfsExampaperInstance);
    router.put('/exampapers/:exampaper_id/use_type', exampaper.updateExampaperUseType);

    router.post('/exampapers/albums', exampaper.createExampaperAlbum);
    router.put('/exampapers/albums/:exampaper_id', exampaper.updateExampaperAlbum);
    router.put('/exampapers/albums/:album_type/status', exampaper.updateExampaperStatus);
    router.get('/exampapers/albums/:exampaper_id', exampaper.getExampaperAlbumById);
    router.get('/exampapers/albums/names/is_exists', exampaper.getExampaperAlbumIsExists);
    router.get('/album/list/', exampaper.getAlbumList);
    router.get('/album/:id/', exampaper.getAlbumInfoById);

    router.get('/grades/list', exampaper.getExampaperGradeList); // 获取试卷所有年级列表
};