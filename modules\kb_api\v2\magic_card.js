const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const mongodb = require('mongodb');
const ObjectId = mongodb.ObjectId;
const ResponseWrapper = require('../../common/utils/response_wrapper');
const logger = require('../../common/utils/logger');


class MagicCard {
    constructor() {
        return new Proxy(this, {
            get: (target, p) => {
                let prop = Reflect.get(target, p);
                if (typeof prop  === 'function') {
                    return prop.bind(this);
                }
            }
        });
    }

    static _checkFilter ({name, test_direction, levels = []}) {
        if (!name || !test_direction) {
            throw new Error('请输入魔术卡名称以及常考题型！');
        }
        if (!Array.isArray(levels)) {
            throw new Error('data error, levels数据类型异常！');
        }
        for (let item of levels) {
            if (!item.name) {
                item = null;
                continue;
            }
            if (!+item.num || +item.num < 0) {
                throw new Error(`levels num error: ${item.num}`);
            }
            for (let ele of item.analysis) {
                if (!Array.isArray(ele.contents)) {
                    throw new Error(`analysis contents error: ${ele.contents}`);
                }
                ele = {
                    name: ele.name,
                    contents: ele.contents
                };
            }
            item = {
                name: item.name,
                num: +item.num,
                analysis: item.analysis
            };
        }
        levels = levels.filter(e => e);
    }

    static async _getKnowledgeTreeById (id) {
        let knowledgeTree = await db.collection('knowledge_tree').findOne({_id: id}, {nodes: 1});
        if (!knowledgeTree) {
            return [];
        }
        let chapterIds = [];
        let nodes = [];
        let nodeIdMap = {};

        for (let chapter1 of knowledgeTree.nodes || []) {
            if (!chapter1.nodes) {
                if (chapter1.id) {
                    chapterIds.push(chapter1.id);
                    nodeIdMap[chapter1.id] = chapter1.id;
                    nodes.push({
                        id: chapter1.id,
                        name: chapter1.name
                    });
                }
            } else {
                for (let chapter2 of chapter1.nodes || []) {
                    if (!chapter2.nodes) {
                        if (chapter2.id) {
                            chapterIds.push(chapter2.id);
                            nodeIdMap[chapter2.id] = chapter2.id;
                            nodes.push({
                                id: chapter2.id,
                                name: chapter2.name
                            });
                        }
                    } else { 
                        let added = false;
                        for (let chapter3 of chapter2.nodes || []) {
                            if (chapter3.id) {
                                nodeIdMap[chapter3.id] = chapter2.id;
                                chapterIds.push(chapter3.id);
                                if (!added) {
                                    nodes.push({
                                        id: chapter2.id,
                                        name: chapter2.name
                                    });
                                    added = true;
                                }
                            }
                        }
                    }
                }
            }
        }

        let knowledges = await db.collection('knowledge_tree_chapter')
            .find({_id: {$in: chapterIds}}).project({knowledges: 1}).toArray();
        
        return {nodes, nodeIdMap, knowledges};
    }

    static async _checkSameNodes (nodeIds, id) {
        let magicCards = await db.collection('magic_card')
            .find({_id: {$ne: id}, 'nodes.id': {$in: nodeIds}}).project({name:1, nodes: 1}).toArray();
        let msg = [];

        for (let card of magicCards || []) {
            let nodeNames = [];
            for (let node of card.nodes || []) {
                if (nodeIds.includes(node.id)) {
                    nodeNames.push(node.name);
                }
            }
            let nodeName = nodeNames.join();
            msg.push(`${nodeName}已存在于${card.name}中`);
        }
        if (msg.length) {
            throw new Error(msg.join());
        }
    }

    async getMagicCard (req, res) {
        let responseWrapper = new ResponseWrapper(res);
        let {period, subject, limit = 10, offset = 0} = req.query;
        limit = +limit; offset = +offset;

        let cond = {period, subject, is_del: {$ne: true}};

        let cursor = db.collection('magic_card')
            .find(cond);
        let total_num = await cursor.count();
        let list = [];

        if (limit === -1) {
            list = await cursor.toArray();
        } else {
            list = await cursor.sort({ctime: -1}).skip(offset).limit(limit).toArray();
        }

        for (let ele of list) {
            ele.id = ele._id;
            delete ele._id;
        }
        return responseWrapper.succ({total_num, list});
    }

    async getMagicCardList (req, res) {
        let responseWrapper = new ResponseWrapper(res);
        let {period, subject, keyword, key, limit = 10, offset = 0} = req.query;
        limit = +limit; offset = +offset;

        const keyMap = {
            name: 'name',
            nodes: 'nodes.name',
            knowledges: 'knowledges.name'
        };

        const sortMagic = (arr, key, keyword) => {
            for (let item of arr || []) {
                let now = +new Date();
                let toNowTime = now - (+new Date(item.ctime));
                let keySort = '';
                let regex = new RegExp(keyword);
                if (key === 'name') {
                    keySort = item.name.length - keyword.length;
                } else {
                    for (let ele of item[key] || []) {
                        if (regex.test(ele.name)) {
                            let tempSort = ele.name.length - keyword.length;
                            if (isNaN(parseInt(keySort)) || keySort > tempSort) {
                                keySort = tempSort;
                            }
                        }
                    }
                }
                item.sortBy = `a${now + keySort}b${now + toNowTime}`;
                if (isNaN(parseInt(keySort))) {
                    item.sortBy = `z${now}b${toNowTime}`;
                }
            }
            arr = arr.sort((a, b) => a.sortBy > b.sortBy);           
            
        };

        let cond = {period, subject, is_del: {$ne: true}};

        if (!keyword || !key || !keyMap[key]) {
            this.getMagicCard(req, res);
            return;
        }
    
        cond[keyMap[key]] = {$regex: keyword};
    
        let cursor = db.collection('magic_card')
            .find(cond).project({[key]: 1, ctime: 1}).sort({ctime: -1});
        let total_num = await cursor.count();
        let list = await cursor.toArray();

        //排序
        sortMagic(list, key, keyword);

        list = list.slice(offset, offset+limit);
        let ids = list.map(e => e._id);
        list = await db.collection('magic_card').find({_id: {$in: ids}}).toArray();
        sortMagic(list, key, keyword);
        for (let ele of list) {
            ele.id = ele._id;
            delete ele._id;
        }
        return responseWrapper.succ({total_num, list});
    }

    async createMagicCard (req, res) {
        let responseWrapper = new ResponseWrapper(res);
        let {period, subject, name, test_direction, levels} = req.body;
        try {       
            if (!period || !subject) {
                throw new Error('PARAMETERS_ERROR');
            }
            if (!test_direction || !Array.isArray(test_direction)) {
                throw new Error(`test_direction 格式错误！${test_direction}`);
            }
            MagicCard._checkFilter(req.body);

            let newData = {
                period, subject,
                name, test_direction, levels,
                knowledge_tree_id:'',
                nodes: [],
                knowledges: [],
        
                ctime: new Date(),
                utime: new Date(),
                is_del: false
            };

            await db.collection('magic_card').insertOne(newData);
        } catch (error) {
            return responseWrapper.error('HANDLE_ERROR', error.message);
        }
        return responseWrapper.succ({result: true});
    }

    async editMagicCard (req, res) {
        let responseWrapper = new ResponseWrapper(res);
        let {name, test_direction, levels} = req.body;
        let id = req.params.id;
        try {
            if (!id) {
                throw new Error('缺少id！');
            }
            if (!test_direction || !Array.isArray(test_direction)) {
                throw new Error(`test_direction 格式错误！${test_direction}`);
            }
            id = ObjectId(id);
            MagicCard._checkFilter(req.body);

            let newData = {
                name, test_direction, levels,
                utime: new Date(),
            };

            await db.collection('magic_card').updateOne({_id: id}, {$set: newData});
        } catch (error) {
            return responseWrapper.error('HANDLE_ERROR', error.message);
        }
        return responseWrapper.succ({result: true});
    }

    async delMagicCard (req, res) {
        let responseWrapper = new ResponseWrapper(res);
        let {id, type} = req.params;
        try {
            
            if (!id) {
                throw new Error('缺少id！');
            }
            if (!type || type === 'undefined' || !['card', 'knowledge'].includes(type)) {
                throw new Error('Params Error！');
            }
            id = ObjectId(id);
            let set = type === 'card' ? 
            {is_del: true, dtime: new Date()} : {knowledge_tree_id: '', nodes: [], knowledges: [], utime: new Date()};
            
            await db.collection('magic_card').updateOne({_id: id}, {$set: set});
            
        } catch (error) {
            return responseWrapper.error('HANDLE_ERROR', error.message);    
        }
        return responseWrapper.succ({result: true});
    }
        
    async getMagicCardDetail (req, res) {
        let responseWrapper = new ResponseWrapper(res);
        let id = req.params.id;
        try {
     
            if (!id) {
                throw new Error('缺少id！');
            }
            id = ObjectId(id);

            let card = await db.collection('magic_card')
                .findOne({_id: id, is_del: {$ne: true}}, {knowledge_tree_id: 0, nodes: 0, knowledges: 0});
            if (!card) {
                return responseWrapper.succ({});
            }
            card.id = card._id;
            delete card._id;
            return responseWrapper.succ({card});
                
        } catch (error) {
            return responseWrapper.error('HANDLE_ERROR', error.message);    
        }
    }

    async editMagicCardKnowledge (req, res) {
        let responseWrapper = new ResponseWrapper(res);
        let {id} = req.params;
        let {knowledgeTreeId, nodesId} = req.body;
        try {
            if (!id) {
                throw new Error('缺少id！');
            }
            id = ObjectId(id);
            if (!+knowledgeTreeId || !Array.isArray(nodesId)) {
                throw new Error('Params Error！');
            }
            nodesId = nodesId.map(e => +e).filter(ele => +ele);

            // 校验是否存在重复的二级节点
            await MagicCard._checkSameNodes(nodesId, id);

            let {nodes, nodeIdMap, knowledges} = await MagicCard._getKnowledgeTreeById(knowledgeTreeId);
            nodes = nodes.filter(e => nodesId.includes(e.id));
            let tempNodeIds = nodes.map(e => e.id);
            let knowledge_id = [];
            for (let k of knowledges || []) {
                if (tempNodeIds.includes(nodeIdMap[k._id])) {
                    k.knowledges = (k.knowledges || []).map((e) => {
                        e = {name: e.name, id: e.id };
                        return e;
                    });
                    knowledge_id = [...knowledge_id, ...k.knowledges];
                }
            }
            
          
            let set = {
                knowledge_tree_id: +knowledgeTreeId, 
                nodes: nodes, 
                knowledges: knowledge_id
            };
            await db.collection('magic_card').updateOne({_id: id}, {$set: set});
            
        } catch (error) {
            return responseWrapper.error('HANDLE_ERROR', error.message);    
        }
        return responseWrapper.succ({result: true});
    }

    // 待加缓存
    static async _findNode({knowledgeTreeId, nodeIds, knowledgeId}) {
        let nodes = await db.collection('knowledge_tree_chapter').find({_id: {$in: nodeIds}}).toArray();
        let _nodes = [];
        for (let e of nodes) {
            if (e.knowledges) {
                for (let k of e.knowledges) {
                    if (k.id === knowledgeId) {
                        return e;
                    }
                }
            } else {
                _nodes.push(e);
            }
        }
        
        if (!_nodes.length) return null;

        let tree = await db.collection('knowledge_tree').findOne({_id: knowledgeTreeId});
        if (!tree) return null;

        let nodeMap = {}, endNodeIds = [];
        const _findEndNodes = (children) => {
            let ret = [];
            for (let child of children) {
                if (!child.nodes) {
                    ret.push(child);
                    endNodeIds.push(child.id);
                } else {
                    ret.push(..._findEndNodes(child.nodes));
                }
            }
            return ret;
        };
        const _findSecondNode = (nodeId) => {
            for (let first of tree.nodes) {
                if (first.id === nodeId) {
                    return first;
                }
                if (!first.nodes) continue;
                for (let second of first.nodes || []) {
                    if (second.id === nodeId) {
                        return second;
                    }
                }
            }
        };
        // 找到二级包含的所有末级chapter
        for (let _node of _nodes) {
            let node = _findSecondNode(_node._id);
            if (node) {
                nodeMap[node.id] = _findEndNodes(node.nodes || []);
            }
        }
        if (endNodeIds.length) {
            nodes = await db.collection('knowledge_tree_chapter').find({_id: {$in: endNodeIds}}).project({}).toArray();
            for (let _node of _nodes) {
                let endNodes = nodeMap[_node._id];
                if (!endNodes) continue;
                for (let endNode of endNodes) {
                    let node = nodes.find(e => e._id === endNode.id);
                    if (!node || !node.knowledges) continue;
                    if (node.knowledges.find(e => e.id === knowledgeId)) {
                        return _node;
                    }
                }
            }
        }
    }

    // 根据知识点ID获取魔术卡（辅导调用）
    async getMagicCardByKnowledgeId(req, res) {
        res.resMode = 'normal';
        let responseWrapper = new ResponseWrapper(res);
        let knowledgeId = +req.query.id;
        let level = +req.query.level;
        
        try {
            if (!knowledgeId || !level) {
                return responseWrapper.error('PARAMETERS_ERROR', '需要传入参数，知识点ID: knowledge_id，得分层次: level');
            }

            let card = await db.collection('magic_card').findOne({
                'knowledges.id': knowledgeId, is_del: {$ne: true}
            });

            if (!card) {
                return responseWrapper.error('NULL_ERROR');
            }
            // 获取知识点对应的二级节点，（需要展示）
            let {knowledge_tree_id: knowledgeTreeId, nodes} = card;
            let node = await MagicCard._findNode({knowledgeTreeId, nodeIds: nodes.map(e => e.id), knowledgeId});
            if (node) {
                card.node = {
                    id: node._id,
                    name: node.name,
                    score: node.score || 0,
                    chance: node.chance || 0,
                };
            } else card.node = {
                score: 0,
                chance: 0
            };

            level = (card.levels || []).find(e => e.num === level);
            card.analysis = level ? (level.analysis || []) : [];
            delete card.levels;
            delete card.nodes;
            delete card.knowledges;
            delete card.knowledge_tree_id;
            delete card.is_del;

            card.id = card._id;
            delete card._id;
            responseWrapper.succ(card);
        } catch (error) {
            logger.error(error);
            responseWrapper.error('HANDLE_ERROR', error.message);
        }
    }
}

module.exports = new MagicCard();
