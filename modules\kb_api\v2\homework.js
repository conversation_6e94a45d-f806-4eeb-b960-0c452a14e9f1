const _ = require('underscore');

const Logger = require('../../common/utils/logger');
const err_handler = require('../../common/utils/utils.js').err_handler;
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const crypto = require('crypto');
const rediser = require('../../common/utils/rediser');
const cache = rediser.getCache();
const ResponseWrapper = require('../../common/utils/response_wrapper');

function md5sum(x){
	var md5 = crypto.createHash('md5');
	return md5.update(x).digest('hex');
}

function getSubjects(req, res){
	var responseWrapper = new ResponseWrapper(res);

	// period: 高中，初中
	var period = req.query.period;
	if(!period){
		return responseWrapper.error('PARAMETERS_ERROR' ,'必须要有period字段');
	}

	/*
	 * etag is redis key
	 */
	var etag = md5sum(['getSubjects', period].join(':'));

	rediser.get(etag, function (err, item) {

		if(err){
			return responseWrapper.error('HANDLE_ERROR' ,err.message);
		}
		
		if(item){
			try{
				if(typeof item === 'string')
					item = JSON.parse(item);
			}catch(err){
				return responseWrapper.error('HANDLE_ERROR' ,err.message);
			}

			/*
			 * 只要走到这个分支，代码便不会继续执行了
			 */
			return  responseWrapper.succ(item);
		}

		return db.collection('book_answer').find({
			period: period
		}, {
			subject:1
		}).toArray(function(err, docs){

			if(err){
				return responseWrapper.error('HANDLE_ERROR' ,err.message);
			}	

			if(!Array.isArray(docs))
				docs = [];

			var retval = {
				subjects: _.uniq(_.pluck(docs, 'subject'))
			};
			rediser.set(etag, retval, 60*1);
			return  responseWrapper.succ(retval);
		});
	});
}

function getBooksCategory(req, res){
	var responseWrapper = new ResponseWrapper(res);
	var period = req.query.period;
	var subject = req.query.subject;

	var cond = {};
	var etag = [];
	etag.push('getBooksCategory');

	if(period){
		cond.period = period;
		etag.push(period);
	}

	if(subject){
		cond.subject = subject;
		etag.push(subject);
	}

	etag = md5sum(etag.join(':'));

	const proj = {
		period: 1,
		subject: 1,
		press_version: 1,
		grade: 1
	};

	rediser.get(etag, function (err, item) {

		if(err){
			return responseWrapper.error('HANDLE_ERROR' ,err.message);
		}
		
		if(item){
			try{
				if(typeof item === 'string')
					item = JSON.parse(item);
			}catch(err){
				return responseWrapper.error('HANDLE_ERROR' ,err.message);
			}

			return  responseWrapper.succ(item);
		}
		var sort_by = {'order_no' : 1};
		return db.collection('book_answer').find(cond, proj).sort(sort_by).toArray(function(err, docs){

			if(err){
				return responseWrapper.error('HANDLE_ERROR' ,err.message);
			}	

			var retval = {
				children: []
			};

			_.each(docs, function(book){
				var _period = book.period;
				var _subject = book.subject;
				var _press_version = book.press_version;
				var _grade = book.grade;
				var _id = book._id;

				var period = _.find(retval.children, function(pd){ return pd.name == _period});
				if(!period){
					period = {
						key: 'period',
						name: _period,
						children: []	
					};
					retval.children.push(period);
				}
				
				var subject = _.find(period.children, function(sj){ return sj.name == _subject});
				if(!subject){
					subject = {
						key: 'subject',
						name: _subject,
						children: []	
					};

					period.children.push(subject);
				}

				var press_version = _.find(subject.children, function(pv){ return pv.name == _press_version});
				if(!press_version){
					press_version = {
						key: 'press_version',
						name: _press_version,
						children: []	
					};
					subject.children.push(press_version);
				}

				var grade = _.find(press_version.children, function(gd){ return gd.name == _grade});
				if(!grade){
					grade = {
						key: 'grade',
						name: _grade,
						id: _id
					};
					press_version.children.push(grade);
				}
			});

			var item = {book:retval};
			rediser.set(etag, JSON.stringify(item), 60 * 1);
			return  responseWrapper.succ(item);
		});
	});

}

function getChapters(req, res){
	var responseWrapper = new ResponseWrapper(res);

	var chapter_ids = decodeURIComponent(req.params.chapter_ids);
	chapter_ids = chapter_ids.split(',');
	chapter_ids = _.map(chapter_ids, function(chapter_id){
		return Number(chapter_id);		
	});

	var proj = {name: 1, answers: 1};

	// if(req.query.device === 'mobile')
	// 	proj.answers_mobile = 1;
	// else
	// 	proj.answers = 1;

	return db.collection('book_answer_block').find({
		_id: {$in: chapter_ids}
	}, proj).toArray(function(err, docs){
		if(err){
			return responseWrapper.error('HANDLE_ERROR' ,err.message);
		}	

		var order = [];
		
		_.each(chapter_ids, function(chapter){

			var b = _.find(docs, function(doc){
				return doc._id == chapter;	
			});

			if(b){
				delete b._id;
				order.push(b);
			}
		});

		return  responseWrapper.succ({
			chapters: order 
		});
	});
}

function getBook(req, res){
	var responseWrapper = new ResponseWrapper(res);

	/*
	 * make sure bookId is number
	 */
	var bookId = req.params.book_id;
	bookId = Number(bookId);

	if(isNaN(bookId))
		return responseWrapper.error('PARAMETERS_ERROR' ,'book_id 必须为整型数据');

	return db.collection('book_answer').findOne({
		_id: bookId
	}, {
		ctime: 0,
		utime: 0
	}, function(err, doc){

		if(err){
			return responseWrapper.error('HANDLE_ERROR' ,err.message);
		}

		if(!doc){
			return responseWrapper.error('NULL_ERROR' ,'不存在这本书');
		}

		if(doc._id)
			doc.id = doc._id;

		delete doc._id;
		delete doc.order_no;
		delete doc.ctime;
		delete doc.utime;

		return  responseWrapper.succ(doc);
	});
}

module.exports = {
	getSubjects: getSubjects,
	getBooksCategory: getBooksCategory,
	getChapters: getChapters,
	getBook: getBook
}
