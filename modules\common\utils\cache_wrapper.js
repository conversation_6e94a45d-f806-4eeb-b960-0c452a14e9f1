const _ = require('lodash');
const rediser = require('./rediser');
const config = require('config');

// 默认数据有效时间为 5 min
const defaultValidTime = 60 * 5;
// 默认缓存有效时间为 1 day
const defaultCacheTime = 60 * 60 * 24;
// 接口最大 pending 时间为 5 min
const maxPendingTime = 60 * 5;

// 简易任务队列
const workshop = {
    tasks: [],
    workers: 1,
    tick: setInterval(async () => {
        try {
            let task = workshop.tasks.shift();
            if (task && workshop.workers > 0) {
                workshop.workers--;
                await task();
            }
        } catch (e) {
            throw e;
        } finally {
            workshop.workers++;
        }
    }, 1000 + Math.round(Math.random() * 1000))
};

/*
    # 缓存机制
    - 默认缓存响应数据 1 day & 数据有效时间为 5 min
    - 每次接受请求，如果找到缓存数据，则返回缓存数据（cacheTime）
    - 如果发现缓存数据过期（validTime）
        - 依然先返回缓存数据
        - 缓存数据置入 pending 标识并🔄刷新缓存数据
        - 调用接口获取数据并🔄刷新缓存数据
    - 如果没有缓存数据
        - 缓存数据置入 pending 标识并🔄刷新缓存数据
        - 调用接口获取数据并🔄刷新缓存数据

    # Redis key 生成机制
    - URL : /kb_api/v2/resources/profile/exam/update:8500?id=998&name=wd
    - KEY : kb_api:v2:resources:profile:exam:update:8500:id_998&name_wd

    # 请求锁机制
    - 针对 key 相同的请求，每次尝试调用的 handler 之前会使用对应 key 在 redis 设置一个锁
        - 设置成功则继续进行调用 handler
        - 设置失败则延迟一段时间后重新触发请求过程
    - lock key 的过期时间为 maxPendingTime (5min)
*/

/**
 * 自动包装路由缓存
 * 暂时只支持简单 JSON 请求
 * @param {Function} handler 路由处理函数
 * @param {Object} options 配置项
 * @param {number} [options.cacheTime=]                     - 缓存时间 s
 * @param {number} [options.validTime=]                     - 数据有效时间 s
 * @param {Object|Array<Object>} [options.initialization=]  - 缓存数据初始化
 * @param {string|Function} [options.key=]                  - 键值生成方式
 * @return {Function}
 * @example
 * // 缓存 60 秒
 * cacheWrapper(getData, {validTime: 60});
 */
function cacheWrapper(handler, options) {

    // 配置项预处理
    options.cacheTime = options.cacheTime || defaultCacheTime;
    options.validTime = options.validTime || defaultValidTime;
    if (options.key) {
        options.key = _.isFunction(options.key) ? options.key : () => options.key;
    }

    /**
     * 默认 key 生成方式
     * @param {*} req   - 请求体
     * @return {string}
     * @private
     */
    const _keyGenerate = function (req) {
        let params = Object.keys(req.params)
            .map(key => `${key}_${req.params[key]}`).join('&');
        let query = Object.keys(req.query).filter(key => !~['app_key'].indexOf(key))
            .map(key => `${key}_${req.query[key]}`).join('&');
        return [
            ((req.baseUrl || '') + req.path).replace(/^[/\\]*|[/\\]*$/g, '').replace(/[/\\]/g, ':'),
            config.get('app').port,
            ...(params ? [params] : []),
            ...(query ? [query] : []),
        ].join(':');
    };

    // 包装 req, res
    const wrappedHandler = async function (req, res) {
        let key = options.key ? options.key(req) : _keyGenerate(req);
        let lockKey = `lock:${key}`;
        let responseFlag = false;

        // 响应实际请求
        const _doResponse = (response) => {
            if (responseFlag) return;
            res.status(response.status);
            res.json(response.data);
            responseFlag = true;
        };

        // 截获响应内容
        const _getResponse = function () {
            return new Promise(async (resolve) => {
                let response = {};
                let fakeRes = {
                    status(status) {
                        response.status = status || 200;
                    },
                    json(data) {
                        response.data = data;
                        response.createTime = +new Date();
                        // 释放锁 🔒
                        rediser.redis.del(lockKey);
                        resolve(response);
                    },
                };
                // 尝试加锁 🔒 - 检查数据是否在刷新中
                let lock = await rediser.redis.set(lockKey, true, 'EX', maxPendingTime, 'NX');
                // 加锁成功 则开始请求接口数据
                if (lock) {
                    handler(req, fakeRes);
                    return;
                }
                // 加锁失败 则返回 null
                resolve(null);
            });
        };

        let cacheResponse = await rediser.get(key);
        // 如果存在数据
        if (cacheResponse) {
            // 则直接返回数据
            _doResponse(cacheResponse);
            // 之后检查数据是否过期，如果数据过期
            if (_.isNumber(cacheResponse.createTime) && (new Date() - cacheResponse.createTime > options.validTime * 1000)) {
                // 则刷新数据
                let response = await _getResponse();
                if (response) {
                    rediser.set(key, response, options.cacheTime);
                }
            }
        } else {
            // 如果不存在缓存数据 则请求接口数据
            let response = await _getResponse();
            // 如果请求接口数据成功则响应并缓存
            if (response) {
                _doResponse(response);
                rediser.set(key, response, options.cacheTime);
                return;
            }
            // 如果请求接口数据失败则 250ms 后重新模拟 handler 请求过程
            setTimeout(async () => {
                await wrappedHandler(req, res);
            }, 250);
        }
    };

    // 初始化缓存数据
    if (options.initialization) {
        let reqOptions = _.isArray(options.initialization) ? options.initialization : [options.initialization];
        reqOptions.forEach(reqOption => {
            workshop.tasks.push(() => {
                wrappedHandler({
                    path: reqOption.path,
                    params: reqOption.params || {},
                    query: reqOption.query || {},
                }, {
                    status: new Function(),
                    json: new Function(),
                }).then(e => void (e));
            });
        });
    }

    return wrappedHandler;
}

module.exports = cacheWrapper;