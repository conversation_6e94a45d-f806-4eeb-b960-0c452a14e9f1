/**
/**
 * 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 9500,
    app_key: 'iyunxiao_kbplat180305',
};

const getUrl = (pathname) => {
    let url = URL.format({
        protocol: 'http',
        hostname: localEnv.host,
        port: localEnv.port,
        pathname: pathname,
        search: qs.stringify({
            api_key: localEnv.app_key
        })
    });
    return url;
}

describe('获取叠加要素类别', function () {
    it('/kb_api/v2/element_categories', async function () {
        let pathname = `/kb_api/v2/element_categories`;
        let url = getUrl(pathname);
        let data = {
            period: '高中',
            subject: '数学',
            limit: 10,
            offset: 0
        }
        let result = await request.get(url).query(data);
        console.log(JSON.stringify(result.body));
        assert.ok(result.body.list.length >= 0);
        assert.ok(result.body.total_num >= 0);
    });
});

describe('获取叠加要素类别详情byID', function () {
    it('/kb_api/v2/element_categories/:id', async function () {
        let id = '2146566143';
        let pathname = `/kb_api/v2/element_categories/${id}`;
        let url = getUrl(pathname);
        let data = {}
        let result = await request.get(url).query(data);
        console.log(JSON.stringify(result.body));
        assert.ok(result.body);
    });
});

describe('判断叠加要素类别是否存在', function () {
    it('/kb_api/v2/element_categories/exist', async function () {
        let pathname = `/kb_api/v2/element_categories/exist`;
        let url = getUrl(pathname);
        let data = {
            period: '高中',
            subject: '数学',
            name: '哈哈'
        }
        let result = await request.get(url).query(data);
        console.log(JSON.stringify(result.body));
        assert.ok(result.body);
    });
});

describe('判断叠加要素是否存在', function () {
    it('/kb_api/v2/elements/exist', async function () {
        let pathname = `/kb_api/v2/elements/exist`;
        let url = getUrl(pathname);
        let data = {
            period: '高中',
            subject: '数学',
            name: '哈哈'
        }
        let result = await request.get(url).query(data);
        console.log(JSON.stringify(result.body));
        assert.ok(result.body);
    });
});

describe('新增叠加要素类别', function () {
    this.timeout(60000);
    it('/kb_api/v2/element_categories', async function () {
        let pathname = '/kb_api/v2/element_categories';
        let url = getUrl(pathname);
        let data = {
            period: '高中',
            subject: '数学',
            name: '新增高中数学叠加要素类别1',
            children: [{
                id: 0,
                name: '新增高中数学叠加要素1',
                operation_type: 'add'
            }, {
                id: 0,
                name: '新增高中数学叠加要素2',
                operation_type: 'add'
            }]
        };
        try {
            let result = await request.post(url).send(data);
            console.log(result.body); // {}
            assert.ok(!!result.body);
        } catch (e) {
            console.log(e.message);
        }
    });
});

describe('修改叠加要素类别', function () {
    it('/kb_api/v2/element_categories/:id', async function () {
        let id = "2146566143";
        let pathname = `/kb_api/v2/element_categories/${id}`;
        let url = getUrl(pathname);
        let data = {
            period: '高中',
            subject: '数学',
            name: '创新（测试修改1）',
            children: [{
                id: 2144927743,
                name: '新定义问题2（测试新增1）',
                operation_type: 'update'
            }, {
                id: 2144862207,
                name: '新定义问题（测试修改1）',
                operation_type: 'update'
            }, {
                id: 0,
                name: '作差法构造函数',
                operation_type: 'add'
            }, {
                id: 0,
                name: '作商法构造函数',
                operation_type: 'add'
            }]
        };
        try {
            let result = await request.put(url).send(data);
            console.log(result.body); // {}
            assert.ok(!!result.body);
        } catch (e) {
            console.log(e.message);
        }
    });
});

describe('删除叠加要素类别', function () {
    it('/kb_api/v2/element_categories/:id', async function () {
        let id = '2146566143';
        let pathname = `/kb_api/v2/element_categories/${id}`;
        let url = getUrl(pathname);
        let result = await request.delete(url);
        console.log(result.body);
        assert.ok(!!result.body);
    });
});