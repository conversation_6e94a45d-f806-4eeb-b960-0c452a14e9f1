// 试题标签路由
const elementCategory = require('../../../modules/kb_api/v2/element_category');

module.exports = function (router) {
    // 判断叠加要素类别是否存在
    router.get('/element_categories/exist', elementCategory.validateElementCategoryExist);
    // 判断叠加要素是否存在
    router.get('/elements/exist', elementCategory.validateElementExist);
    // 获取叠加要素类别
    router.get('/element_categories', elementCategory.getElementCategoryList);
    // 获取叠加要素类别详情
    router.get('/element_categories/:id', elementCategory.getElementCategoryDetail);
    // 新增叠加要素类别
    router.post('/element_categories', elementCategory.postElementCategory);
    // 修改叠加要素类别
    router.put('/element_categories/:id', elementCategory.updateElementCategory);
    // 删除叠加要素类别
    router.delete('/element_categories/:id', elementCategory.deleteElementCategory);
    router.post('/element_categories/submit/is_repeat_name', elementCategory.isRepeatName);
};