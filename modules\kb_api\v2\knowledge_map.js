const ObjectID = require("mongodb").ObjectID;
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const schemaKnowledgeMap = require('./schemas/knowledge_map');

// 更新映射
const updateKnowledgeMap = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let outId = req.params.out_id;
    let mapKnows = req.body.map_knows || [];
    try {
        schemaKnowledgeMap.putValidate(mapKnows);
        let cond = {_id: ObjectID(outId)};
        let setData = {$set: {map_knows: mapKnows, utime:new Date()}};
        let result = await db.collection('knowledge_map').updateOne(cond, setData);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '修改出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 待映射知识点列表
const getKnowledgeUnmapList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {period, subject, offset, limit} = req.query;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '传参缺少学段学科');
    }
    offset = offset ? parseInt(offset) : 0;
    limit = limit ? parseInt(limit) : 10;
    try {
        let cond = {period: period, subject: subject, map_knows: {$size: 0}}; // 长度等于0
        let know = await db.collection('knowledge_map').find(cond).skip(offset).limit(limit).sort({ctime: -1}).toArray();
        let totalNum = await db.collection('knowledge_map').count(cond);
        for (let item of know) {
            item.out_id = item._id;
            delete item._id;
        }
        responseWrapper.succ({total_num: totalNum, list: know});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 已映射知识点列表
const getKnowledgeMappedList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {period, subject, offset, limit} = req.query;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '传参缺少学段学科');
    }
    offset = offset ? parseInt(offset) : 0;
    limit = limit ? parseInt(limit) : 10;
    try {
        let cond = {period: period, subject: subject, 'map_knows.0': {$exists: 1}}; // 长度大于0
        let know = await db.collection('knowledge_map').find(cond).skip(offset).limit(limit).sort({ctime: -1}).toArray();
        let totalNum = await db.collection('knowledge_map').count(cond);
        for (let item of know) {
            item.out_id = item._id;
            delete item._id;
        }
        responseWrapper.succ({total_num: totalNum, list: know});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};


module.exports = {
    updateKnowledgeMap,    // 更新映射
    getKnowledgeUnmapList, // 待映射知识点列表
    getKnowledgeMappedList // 已映射知识点列表
};
