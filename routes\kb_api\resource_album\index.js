const resource_album = require('../../../modules/kb_api/v2/resource_album');

module.exports = router => {
    // 专辑汇编
    router.get('/resource_album_summary/list/', resource_album.getResourceAlbumSummaryList);
    router.get('/resource_album_summary/info/', resource_album.getResourceAlbumSummary);
    router.post('/resource_album_summary/', resource_album.createResourceAlbumSummary);
    router.put('/resource_album_summary/', resource_album.updateResourceAlbumSummary);
    router.delete('/resource_album_summary/', resource_album.deleteResourceAlbumSummary);
    // 专辑信息
    router.get('/resource_album/list/', resource_album.getResourceAlbumList);
    router.get('/resource_album/info/', resource_album.getResourceAlbum);
    router.post('/resource_album/', resource_album.createResourceAlbum);
    router.put('/resource_album/', resource_album.updateResourceAlbum);
    router.delete('/resource_album/', resource_album.deleteResourceAlbum);
    // 专辑数据
    router.get('/resource_album_data/list/', resource_album.getResourceAlbumDataList);
    router.get('/resource_album_data/info/', resource_album.getResourceAlbumData);
    router.post('/resource_album_data/', resource_album.createResourceAlbumData);
    router.put('/resource_album_data/', resource_album.updateResourceAlbumData);
    router.delete('/resource_album_data/', resource_album.deleteResourceAlbumData);
};