const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const ResponseWrapper = require('../../common/utils/response_wrapper');
let Logger = require('../../common/utils/logger');
const counter = require('../../common/utils/counter');
const catalogManager = require('./catalog');
const schemaTextBook = require('./schemas/text_book');
const _ = require('lodash');

const textbook = 'text_book';
const catalog = 'catalog';

//获取编目
const getCatelogs = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let period = req.query.period;
    let subject = req.query.subject;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    try {
        let result = await db.collection(catalog).findOne({ _id: 'text_book' });
        for (let per of result.periods) {
            if (per.name === period) {
                for (let sub of per.subjects) {
                    if (sub.name === subject) {
                        return responseWrapper.succ({ press_versions: sub.press_versions });
                    }
                }
            }
        }
        return responseWrapper.error('NULL_ERROR', '该学段学科下暂无教材');
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//获取编目下教材信息
const getTextBooks = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let limit = req.query.limit || 25;
    let offset = req.query.offset || 0;
    let period = req.query.period;
    let subject = req.query.subject;
    let press_version = req.query.press_version || '';
    let grade = req.query.grade || '';
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    let result = [];
    let cond = {
        period: period,
        subject: subject
    };
    if (press_version) {
        cond.press_version = press_version;
    }
    if (grade) {
        cond.grade = grade;
    }
    try {
        let data = await db.collection(textbook).find(cond).skip(parseInt(offset)).limit(parseInt(limit)).toArray();
        let total_num = await db.collection(textbook).find(cond).count();
        for (let profile of data) {
            let id = profile._id;
            let profile_url = profile.profile_url;
            let host = profile.host;
            let name = `${profile.press_version}-${profile.grade}-${profile.subject}`;
            let books = {
                id: id,
                profile_url: profile_url,
                name: name,
                host: host
            };
            result.push(books);
        }
        let final = {
            total_num: total_num,
            books: result
        };
        return responseWrapper.succ(final);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//获取教材详细信息
const getTextbookById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    try {
        let data = await db.collection(textbook).findOne({ _id: parseInt(id) });
        data.id = data._id;
        delete data._id;
        responseWrapper.succ(data);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//新增电子教材
const addTextbook = async (req, res) => {
    let can = await catalogManager.canUpdate(req,res);
    let responseWrapper = new ResponseWrapper(res);
    if (can) {
        let data = req.body;
        let now = new Date();
        try {
            // 格式校验
            schemaTextBook.postValidate(data);
            const seq = await counter.nextSeq('text_book', 1);
            data._id = seq[0];
    
            let period = data.period;
            let subject = data.subject;
            let press_version = data.press_version;
            let grade = data.grade;
            let grageObj = { id: seq[0], name: grade };
            let press_version_obj = { name: press_version, grades: [grageObj] };
            let dataCat = await db.collection(catalog).findOne({ _id: 'text_book' });
            let gradeflag = false;
            for (let per of dataCat.periods) {
                if (per.name === period) {
                    for (let sub of per.subjects) {
                        if (sub.name === subject) {
                            for (let pre of sub.press_versions) {
                                if (pre.name === press_version && !gradeflag) {
                                    gradeflag = true;
                                    //let is = _.isArray(pre.grades);
                                    pre.grades.push(grageObj);
                                }
                            }

                            if (!gradeflag) {
                                gradeflag = true;
                                //let is = _.isArray(sub);
                                sub.press_versions.push(press_version_obj);
                            }
                        }
                    }
                }
            }
            await db.collection(catalog).updateOne({ _id: 'text_book' }, dataCat);
    
            data.ctime = now;
            data.utime = now;
            let result = await db.collection(textbook).insertOne(data);
            if (result.result.ok === 1) {
                responseWrapper.succ({});
            } else {
                responseWrapper.error('HANDLE_ERROR', '新增出错了');
            }
        } catch (e) {
            Logger.error(e);
            return responseWrapper.error('HANDLE_ERROR', e.message);
        }
    } else {
        return responseWrapper.succ({msg:'已存在此编目'});
    }
};

//修改电子教材
const updateTextbook = async (req, res) => {
    let can = await catalogManager.canUpdate(req,res);
    let responseWrapper = new ResponseWrapper(res);
    if (can) {
        let data = req.body;
        let now = new Date();
        let ctime;
        try {
            // 格式校验
            schemaTextBook.putValidate(data);
            data._id = data.id;
    
            let period = data.period;
            let subject = data.subject;
            let press_version = data.press_version;
            let grade = data.grade;
            let grageObj = { id: data.id, name: grade };
            let press_version_obj = { name: press_version, grades: [grageObj] };
            let dataCat = await db.collection(catalog).findOne({ _id: 'text_book' });
            let dataSou = await db.collection(textbook).findOne({ _id: data._id });
            ctime = dataSou.ctime;
    
            for (let per of dataCat.periods) {
                if (per.name === period) {
                    for (let sub of per.subjects) {
                        if (sub.name === subject) {
                            for (let pre = 0; pre < sub.press_versions.length;pre++) {
                                if (sub.press_versions[pre].name === dataSou.press_version) {
                                    for (let gra = 0 ;gra < sub.press_versions[pre].grades.length;gra++) {
                                        if (sub.press_versions[pre].grades[gra].name === dataSou.grade) {
                                            sub.press_versions[pre].grades.splice(gra,1);
                                            if (sub.press_versions[pre].grades.length === 0) {
                                                sub.press_versions.splice(pre,1);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            await db.collection(catalog).updateOne({ _id: 'text_book' }, dataCat);
    
            let gradeflag = false;
            for (let per of dataCat.periods) {
                if (per.name === period) {
                    for (let sub of per.subjects) {
                        if (sub.name === subject) {
                            for (let pre of sub.press_versions) {
                                if (pre.name === press_version && !gradeflag) {
                                    gradeflag = true;
                                    //let is = _.isArray(pre.grades);
                                    pre.grades.push(grageObj);
                                }
                            }

                            if (!gradeflag) {
                                gradeflag = true;
                                //let is = _.isArray(sub);
                                sub.press_versions.push(press_version_obj);
                            }
                        }
                    }
                }
            }
            await db.collection(catalog).updateOne({ _id: 'text_book' }, dataCat);
    
            delete data.id;
            data.utime = now;
            data.has_modified = 1;
            data.ctime = ctime;
            await db.collection(textbook).updateOne({ _id: data._id }, data);
            return responseWrapper.succ({});
        } catch (e) {
            Logger.error(e);
            return responseWrapper.error('HANDLE_ERROR', e.message);
        }
    } else {
        return responseWrapper.succ({msg:'已存在此编目'});
    }
};

module.exports = {
    getCatelogs,
    getTextBooks,
    getTextbookById,
    addTextbook,
    updateTextbook,
};