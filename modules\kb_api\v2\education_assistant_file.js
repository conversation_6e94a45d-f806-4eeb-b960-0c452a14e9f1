const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const ResponseWrapper = require('../../common/utils/response_wrapper');
let Logger = require('../../common/utils/logger');
const schemaEducationAssistantBook = require('./schemas/education_assistant_book');
const counter = require('../../common/utils/counter');
const _ = require('lodash');
const Joi = require('@hapi/joi');

const schema_name = 'education_assistant_file';

module.exports = {
    getFilters,
    getList,
    getById,
    add,
    update,
    updateTimes,
    delByIds,
    getBookFileTotal,
    getByIds,
};

const JOI_GET_FILTERS = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    press_version: Joi.string().optional(),
    book_id: Joi.number().optional(),
    category: Joi.string().required(),
    page_img_status: Joi.number().optional(),
    preview_status: Joi.number().optional(),
}).unknown(true);

async function getFilters(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_GET_FILTERS.validate(req.query);
        // const result = [];
        const query = {};
        if (params.period) query.period = params.period;
        if (params.subject) query.subject = params.subject;
        if (params.category) query.category = params.category;
        if (params.preview_status) query.preview_status = params.preview_status;
        // if (params.press_version) query.press_version = params.press_version;
        // if (params.book_id) query.book_id = params.book_id;
        // if (params.category) query.category = params.category;
        // if (params.page_img_status) query.page_img_status = params.page_img_status;

        // const arr = await db.collection('education_assistant_file').distinct('parent_chapter_id', query);
        // if (arr && arr.length > 0) {
        //     const list = await db.collection('book_chapter').find({_id: {$in: arr}}).project({_id: 1, name: 1}).toArray();
        //     for (const d of list) {
        //         result.push({
        //             id: d._id,
        //             name: d.name
        //         })
        //     }
        // }
        const map = new Map();
        const arr = await db.collection('education_assistant_file').find(query).toArray();
        for (const d of arr) {
            if (!map.has(d.press_version)) {
                map.set(d.press_version, {
                    name: d.press_version,
                    key: 'press_version',
                    children: [],
                });
            }
            const version = map.get(d.press_version);
            const book = version.children.find(e => e.id === d.book_id);
            if (_.isEmpty(book)) {
                version.children.push({
                    id: d.book_id,
                    name: d.book_name,
                    key: 'grade'
                });
            }
        }
        const result = Array.from(map.values()) || [];
        return responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_LIST = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    press_version: Joi.string().optional(),
    book_id: Joi.number().optional(),
    parent_chapter_id: Joi.number().optional(),
    chapter_id: Joi.number().optional(),
    category: Joi.string().optional(),
    page_img_status: Joi.number().optional(),
    keyword: [
        Joi.number(),
        Joi.string()
    ],
    preview_status: Joi.number().optional(),
    sort_by: Joi.string().optional().default('utime')
}).unknown(true);

//获取编目下教材信息
async function getList(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_GET_LIST.validate(req.query);
        let limit = params.limit || 10;
        let offset = params.offset || 0;
        let result = [];
        let cond = {
            invalid: 0,
            period: params.period,
            subject: params.subject
        };
        if (params.keyword) {
            const keyword = params.keyword;
            if (_.isNumber(keyword)) {
                cond._id = parseInt(keyword);
            } else {
                cond.name = keyword;
            }
        }

        if (params.parent_chapter_id) {
            if (params.book_id) cond.book_id = params.book_id;
            const book = await db.collection('book').findOne({_id: params.book_id});
            const chapter_ids = [];
            const get_all_chapter_ids = (chapters, child = false) => {
                if (!_.size(chapters)) return;
                for (const c of chapters) {
                    if (c.id === params.parent_chapter_id || child) {
                        chapter_ids.push(c.id);
                        get_all_chapter_ids(c.chapters, true);
                    } else {
                        get_all_chapter_ids(c.chapters);
                    }
                }
            }
            get_all_chapter_ids(book.chapters);
            if (_.size(chapter_ids)) cond.chapter_id = {$in: chapter_ids};
        } else {
            if (params.press_version && !params.book_id) cond.press_version = params.press_version;
            if (params.press_version && params.book_id) {
                cond.book_id = params.book_id;
            }
        }

        if (params.chapter_id) {
            if (params.book_id) cond.book_id = params.book_id;
            cond.chapter_id = params.chapter_id;
        } else {
            if (params.press_version && !params.book_id) cond.press_version = params.press_version;
            if (params.press_version && params.book_id) {
                cond.book_id = params.book_id;
            }
        }
        if (params.category) cond.category = params.category;
        // if (params.page_img_status) cond.page_img_status = params.page_img_status;
        if (params.hasOwnProperty('preview_status')) cond.preview_status = params.preview_status;
        let total_num = await db.collection(schema_name).find(cond).count();
        if (!total_num) return responseWrapper.succ({total_num: 0, records: []});
        let list = await db.collection(schema_name).find(cond).sort({[params.sort_by]: -1}).skip(offset).limit(limit).toArray();

        for (let data of list) {
            data.id = data._id;
            data.ctime = data.ctime.getTime();
            data.utime = data.utime.getTime();
            delete data._id;
            delete data.invalid;
            result.push(data);
        }
        let final = {
            total_num: total_num,
            records: result
        };
        return responseWrapper.succ(final);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};


const JOI_GEI_BY_ID = Joi.object({
    id: Joi.number().required(),
});

async function getById(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_GEI_BY_ID.validate(req.params);
        let id = params.id;
        let data = await db.collection(schema_name).findOne({ _id: id });
        if (_.isEmpty(data) || data.invalid === 1) return responseWrapper.succ(null);
        data.id = data._id;
        data.ctime = data.ctime.getTime();
        data.utime = data.utime.getTime();
        delete data._id;
        delete data.invalid;
        responseWrapper.succ(data);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_POST_DATA = Joi.object({
    datas: Joi.array().items(Joi.object({
        name: Joi.string().required(),
        period: Joi.string().required(),
        subject: Joi.string().required(),
        press_version: Joi.string().required(),
        book_id: Joi.number().required(),
        book_name: Joi.string().required(),
        parent_chapter_id: Joi.number().required(),
        chapter_id: Joi.number().required(),
        category: Joi.string().required(),
        host: Joi.string().required(),
        url: Joi.string().required(),
        from: Joi.string().required(),
        user_id: Joi.number().required(),
        user_name: Joi.string().required(),
        size: Joi.number().required()
    })).required().min(1)
});
//新增教辅
async function add(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let now = new Date();
    try {
        // 格式校验
        const params = await JOI_POST_DATA.validate(req.body);
        let datas = _.get(params, 'datas', []);
        if (!_.size(datas)) return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
        const ids = await counter.nextSeq(schema_name, _.size(datas));
        for (let i = 0; i < _.size(datas); i++) {
            const data = datas[i];
            data._id = ids[i];
            data.invalid = 0;
            data.page_img_status = 0;
            data.ctime = now;
            data.utime = now;
            data.download_times = 0;
            data.view_times = 0;
            data.suffix = data.name.split('.').pop();
        }
        let result = await db.collection(schema_name).insertMany(datas);
        if (result.result.ok === 1) {
            responseWrapper.succ({ids: result.insertedIds});
        } else {
            responseWrapper.error('HANDLE_ERROR', '新增出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_UPDATE_DATA = Joi.object({
    id: Joi.number().required(),
    name: Joi.string().required(),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    press_version: Joi.string().required(),
    book_id: Joi.number().required(),
    book_name: Joi.string().required(),
    parent_chapter_id: Joi.number().required(),
    chapter_id: Joi.number().required(),
    category: Joi.string().required(),
    host: Joi.string().required(),
    url: Joi.string().required(),
    from: Joi.string().required(),
    size: Joi.number().required()
});
//修改
async function update(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        // 格式校验
        let update = await JOI_UPDATE_DATA.validate(req.body);
        let now = new Date();
        const id = update.id;
        const data = await db.collection(schema_name).findOne({_id: id});
        if (_.isEmpty(data)) return  responseWrapper.error('PARAMETERS_ERROR', '修改数据不存在');
        if (update.url !== data.url) { // 重新上传文件
            update.page_img_status = 0;
            update.page_img_url = [];
            update.page_num = 0;
            update.video_url = '';
            update.suffix = update.name.split('.').pop();

        }
        update.utime = now;
        delete update.id;
        await db.collection(schema_name).updateOne({ _id: id }, {$set: update});
        responseWrapper.succ({id});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

const JOI_UPDATE_TIMES = Joi.object({
    id: Joi.number().required(),
    action: Joi.string().required(),
});
async function updateTimes(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let {id, action} = await JOI_UPDATE_TIMES.validate(req.body);
        const actions = ['download_times', 'view_times'];
        if (!actions.includes(action)) return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
        let update = {'$inc': { [action]: 1 }};
        await db.collection(schema_name).updateOne({_id: id}, update);
        return responseWrapper.succ({id});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_DELETE_BY_IDS = Joi.object({
    ids: Joi.array().items(Joi.number()).min(1).required(),
});
// 批量删除
async function delByIds(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_DELETE_BY_IDS.validate(req.body);
        const ds = {
            invalid: 1,
            utime: new Date(),
        }
        await db.collection(schema_name).updateMany({_id: {$in: params.ids}}, {$set: ds});
        return responseWrapper.succ({ids: params.ids});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_BOOK_FILE_TOTAL = Joi.object({
    id: Joi.number().required(),
});

async function getBookFileTotal(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_BOOK_FILE_TOTAL.validate(req.params);
        const cond = {
            book_id: params.id,
            invalid: 0,
        }
        const total = await db.collection(schema_name).find(cond).count();
        return responseWrapper.succ({total});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GEI_BY_IDS = Joi.object({
    ids: Joi.string().required(),
});
async function getByIds(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_GEI_BY_IDS.validate(req.params);
        let ids = params.ids.split(',').map(e => Number(e));
        const cond = {
            _id: {$in: ids},
            invalid: 0,
        }
        const list = await db.collection(schema_name).find(cond).toArray();
        for (const data of list) {
            data.id = data._id;
            data.ctime = data.ctime.getTime();
            data.utime = data.utime.getTime();
            delete data._id;
            delete data.invalid;
        }
        return responseWrapper.succ(list);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

