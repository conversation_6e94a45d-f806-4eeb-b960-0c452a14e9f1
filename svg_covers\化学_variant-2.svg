<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
    
    <defs>
        <linearGradient id="grad_9087" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#135058;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#317b2c;stop-opacity:1" />
        </linearGradient>
    </defs>

    <!-- 主背景 -->
    <rect width="120" height="150" fill="url(#grad_9087)" rx="6"/>

    <!-- 装饰图案 -->
    <circle cx="25" cy="135" r="35" fill="none" stroke="white" stroke-width="3" opacity="0.1"/>

    <!-- 顶部标题区域背景 -->
    <rect x="0" y="0" width="120" height="18" fill="rgba(255,255,255,0.1)" rx="6 6 0 0"/>

    <!-- 标题分隔线 -->
    <line x1="10" y1="18" x2="110" y2="18" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>

    <!-- 标题文字 -->
    <text x="60" y="12" font-family="Noto Sans SC, Helvetica Neue, Arial, sans-serif"
          font-size="9" font-weight="500" fill="white" text-anchor="middle"
          letter-spacing="0.5px" style="text-transform: uppercase;">化学 | CHEMISTRY</text>

    <!-- 底部区域 -->
    <rect x="0" y="125" width="120" height="25" fill="rgba(0,0,0,0.1)" rx="0 0 6 6"/>

    <!-- 外边框 -->
    <rect width="120" height="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5" rx="6"/>

    <!-- 阴影效果（模拟box-shadow） -->
    <defs>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.1)"/>
            <feDropShadow dx="0" dy="5" stdDeviation="5" flood-color="rgba(0,0,0,0.1)"/>
        </filter>
    </defs>
    <rect width="120" height="150" fill="none" rx="6" style="filter: url(#shadow);"/>
</svg>