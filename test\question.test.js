/**
 * 知识点映射 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 9500,
    app_key: 'iyunxiao_kbplat180305',
};


// 获取试题的小题数和选项数
const getSubQuesCountAndOptionsCount = (question) => {
    try {
        if (question && question.subject === '英语') {
            if (question.blocks) {
                let stems = question.blocks.stems;
                if (Array.isArray(stems) && stems.length > 0) {
                    let countOptionsArr = [];
                    for (let stem of stems) {
                        if (stem.options && (typeof stem.options === 'object')) {
                            let len = Object.keys(stem.options).length;
                            if (len > 0 && !countOptionsArr.includes(len)) {
                                countOptionsArr.push(len);
                            }
                        }
                    }
                    if (!question.tags) {
                        question.tags = {};
                    }
                    question.tags.count_sub_ques = stems.length;
                    question.tags.count_options = countOptionsArr;
                }
            }
        }
    } catch (e) {
        Logger.error(e);
    }
};

describe('获取试题的小题数和选项数', function () {
    it('getSubQuesCountAndOptionsCount', async function () {
        let question = {
            subject: '英语',
            blocks: {
                "stems": [
                    {
                        "options": '',
                        "stem": "<span class=\"tiku-list-nr\">${(1)}$</span>该生态系统中的食物链条数和鹰同时占有的营养级别分别是（　　）"
                    },
                    {
                        "options": {},
                        "stem": "<span class=\"tiku-list-nr\">${(2)}$</span>除图中所示的生物类群外，要保证该生态系统的物质循环正常进行，还应有的生物成分为（　　）"
                    },
                    {
                        "options": {
                            "A": "捕食和竞争",
                            "B": "只有竞争",
                            "C": "只有捕食",
                        },
                        "stem": "<span class=\"tiku-list-nr\">${(3)}$</span>图中蛇与鹰的种间关系为（　　）"
                    },
                    {
                        "options": {
                            "A": "${8 \\rm{kJ} }$",
                            "B": "${10 \\rm{kJ} }$",
                            "C": "${40 \\rm{kJ} }$",
                            "D": "${80 \\rm{kJ} }$"
                        },
                        "stem": "<span class=\"tiku-list-nr\">${(4)}$</span>如果图中草能提供${1000 \\rm{kJ} }$的能量，营养级间的能量传递效率为${10\\% \\sim 20\\% }$，那么鹰占据的营养级能得到的最高能量值是（　　）"
                    }
                ]
            }
        };
        getSubQuesCountAndOptionsCount(question);
        console.log(question.tags);
        assert.deepStrictEqual(question.tags.count_sub_ques, 4);
        assert.deepStrictEqual(question.tags.count_options, [3, 4]);
    });
});


describe('试题', function () {
    describe('请求某知识点下试题信息', function () {
        it('/kb_api/v2/knowledges/:knowledge_ids/questions2', async function () {
            this.timeout(10000);
            let knowledge_ids = '1872429055';
            let pathname = `/kb_api/v2/knowledges/${knowledge_ids}/questions2`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                offset: 0,
                limit: 10,
                mult_know_use: "union",
                sort_by: "year",
                total: true
            };
            let result = await request.post(url).send(data);
            //console.log(result.body);
            console.log(result.body.total_num);
            assert.ok(Array.isArray(result.body.questions));
            assert.ok(result.body.total_num > -1);
        });
    });
    describe('请求某知识点下试题的筛选信息', function () {
        it('题型', async function () {
            this.timeout(10000);
            let knowledge_ids = '1872429055';
            let pathname = `/kb_api/v2/knowledges/${knowledge_ids}/questions_filter`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                type: '解答题'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            assert.ok(Array.isArray(result.body));
        });
        it('类型', async function () {
            this.timeout(10000);
            let knowledge_ids = '1872429055';
            let pathname = `/kb_api/v2/knowledges/${knowledge_ids}/questions_filter`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                category: '常考题'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            assert.ok(Array.isArray(result.body));
        });
        it('难度', async function () {
            this.timeout(10000);
            let knowledge_ids = '1872429055';
            let pathname = `/kb_api/v2/knowledges/${knowledge_ids}/questions_filter`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                difficulty: '较难'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            console.log(result.body.length);
            assert.ok(Array.isArray(result.body));
        });
        it('来源', async function () {
            this.timeout(10000);
            let knowledge_ids = '1872429055';
            let pathname = `/kb_api/v2/knowledges/${knowledge_ids}/questions_filter`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                exam_type: '期中试卷'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            console.log(result.body.length);
            assert.ok(Array.isArray(result.body));
        });
        it('省', async function () {
            this.timeout(10000);
            let knowledge_ids = '1872429055';
            let pathname = `/kb_api/v2/knowledges/${knowledge_ids}/questions_filter`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                province: '北京'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            console.log(result.body.length);
            assert.ok(Array.isArray(result.body));
        });
        it('市', async function () {
            this.timeout(10000);
            let knowledge_ids = '1872429055';
            let pathname = `/kb_api/v2/knowledges/${knowledge_ids}/questions_filter`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                city: '郑州'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            assert.ok(Array.isArray(result.body));
        });
        it('年级', async function () {
            this.timeout(10000);
            let knowledge_ids = '1084030975';
            let pathname = `/kb_api/v2/knowledges/${knowledge_ids}/questions_filter`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                grade: '高三'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            assert.ok(Array.isArray(result.body));
        });
        it('精品类型', async function () {
            this.timeout(10000);
            let knowledge_ids = '1084030975';
            let pathname = `/kb_api/v2/knowledges/${knowledge_ids}/questions_filter`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                elite: -1
            };
            let result = await request.get(url).query(data);
            console.log(result.body.length);
            assert.ok(Array.isArray(result.body));
        });
    });

    describe('试题信息根据试题id', function () {
        it('/kb_api/v2/questions/qids/kbp', async function () {
            this.timeout(10000);
            let pathname = `/kb_api/v2/questions/qids/kbp`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                qids: '2137063423,2136670207'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            assert.ok(Array.isArray(result.body));
        });
    });

    describe('按照更新时间获取试题', function () {
        it('/kb_api/v2/questions/qids/utime', async function () {
            this.timeout(10000);
            let pathname = `/kb_api/v2/questions/qids/utime`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let nowMinutes = new Date().getMinutes();
            let data = {
                start: new Date(new Date().setMinutes(nowMinutes - 600)),
                end: new Date()
            };
            console.log(data);
            let result = await request.get(url).query(data);
            console.log(result.body);
            console.log(result.body.length);
            assert.ok(Array.isArray(result.body));
        });
    });

    describe('获取drm试题数量', function () {
        it('/kb_api/v2/questions/drm/count', async function () {
            this.timeout(60000);
            let pathname = `/kb_api/v2/questions/drm/count`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                period: '高中',
                subject: '物理'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            assert.ok(result.body.total_num > -1);
            assert.ok(result.body.toady_num > -1);
        });
    });

    describe('post接口获取试题', function () {
        it.only('/kb_api/v2/questions', async function () {
            this.timeout(60000);
            let pathname = `/kb_api/v2/questions/`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key,
                    mobile_ready: 'true'
                })
            });
            let data = {
                question_ids: [131268607, 105054207, 1799094271],
                fields_type: 'full', // 'stem', 'solution', 'full', 'answer'
                device: 'mobile'
            };
            let result = await request.post(url).send(data);
            console.log(result.body);
            assert.ok(Array.isArray(result.body));
        });
    });

});

