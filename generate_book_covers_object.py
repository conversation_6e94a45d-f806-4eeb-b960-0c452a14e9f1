#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成bookCovers对象 - 将svg_covers文件夹中的文件转换为URL对象格式
"""

import os
import re
from collections import defaultdict

def generate_book_covers_object():
    """生成bookCovers对象"""
    
    # 基础URL模板
    base_url = "https://jzl-oss.yunxiao.com/jiaoyan/public/textbook-covers/"
    
    # 检查svg_covers目录
    svg_dir = "svg_covers"
    if not os.path.exists(svg_dir):
        print("错误: svg_covers 目录不存在")
        return
    
    # 获取所有SVG文件
    svg_files = [f for f in os.listdir(svg_dir) if f.endswith('.svg')]
    
    if not svg_files:
        print("错误: svg_covers 目录中没有找到SVG文件")
        return
    
    # 按科目分组
    subjects = defaultdict(list)
    
    for filename in svg_files:
        # 解析文件名，提取科目名和变体
        # 格式: 科目名_variant-数字.svg
        match = re.match(r'(.+)_variant-(\d+)\.svg', filename)
        if match:
            subject = match.group(1)
            variant = int(match.group(2))
            subjects[subject].append((variant, filename))
    
    # 对每个科目的变体进行排序
    for subject in subjects:
        subjects[subject].sort(key=lambda x: x[0])  # 按变体数字排序
    
    # 生成JavaScript对象
    js_object = "let bookCovers = {\n"
    
    # 科目排序（按照常见的学科顺序）
    subject_order = ['语文', '数学', '英语', '物理', '化学', '生物', '历史', '地理', '政治']
    
    # 按指定顺序处理科目
    processed_subjects = set()
    for subject in subject_order:
        if subject in subjects:
            variants = subjects[subject]
            js_object += f"    '{subject}': [\n"
            
            for i, (variant_num, filename) in enumerate(variants):
                url = f"{base_url}{filename}"
                comma = "," if i < len(variants) - 1 else ""
                js_object += f"        '{url}'{comma}\n"
            
            js_object += "    ],\n"
            processed_subjects.add(subject)
    
    # 处理剩余的科目（如果有的话）
    for subject in sorted(subjects.keys()):
        if subject not in processed_subjects:
            variants = subjects[subject]
            js_object += f"    '{subject}': [\n"
            
            for i, (variant_num, filename) in enumerate(variants):
                url = f"{base_url}{filename}"
                comma = "," if i < len(variants) - 1 else ""
                js_object += f"        '{url}'{comma}\n"
            
            js_object += "    ],\n"
    
    js_object = js_object.rstrip(',\n') + "\n};"
    
    return js_object

def main():
    """主函数"""
    print("正在生成bookCovers对象...")
    
    js_object = generate_book_covers_object()
    
    if js_object:
        # 保存到文件
        output_file = "bookCovers.js"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(js_object)
        
        print(f"✓ 已生成 {output_file}")
        print("\n生成的对象内容:")
        print("=" * 80)
        print(js_object)
        print("=" * 80)
        
        # 统计信息
        lines = js_object.split('\n')
        subject_count = len([line for line in lines if line.strip().startswith("'")])
        url_count = len([line for line in lines if 'https://' in line])
        
        print(f"\n📊 统计信息:")
        print(f"   科目数量: {subject_count}")
        print(f"   URL数量: {url_count}")
        print(f"   输出文件: {output_file}")

if __name__ == "__main__":
    main()
