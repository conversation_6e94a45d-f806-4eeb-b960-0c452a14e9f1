const logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const ObjectID = require('mongodb').ObjectID;

const resourceCol = db.collection('category_knowledge');

// 批量新增知识点卡片
const saveBatchCategoryKnowledge = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let {insertBody} = req.body;
    // 数据校验 todo:
    if (!Array.isArray(insertBody)) {
        return resWrapper.error('PARAMETERS_ERROR');
    }

    if (!insertBody.length) {
        return resWrapper.succ({result: false});
    }

    insertBody = insertBody.map(item => {
        item.contents = (item.contents || []).map(e => {
            e.useSceneList = (e.useSceneList || []).filter(ele => {
                return ['上课', '预习', '复习'].includes(ele)
            });
            return {
                name : e.name || '',
                content : e.content || '',
                useSceneList : e.useSceneList,
                value : e.value || '',
                image : e.image || ''
            };
        });

        item = {
            _id: item._id ? ObjectID(item._id) : new ObjectID(),
            name: '考法知识点',

            period: item.period || '',
            subject: item.subject || '',
            school_id: +item.school_id || '',

            contents: item.contents,

            ctime: item.ctime || new Date(),
            utime: item.utime || new Date()
        };
        return item;
    });


    await resourceCol.insertMany(insertBody);

    return resWrapper.succ({result: true});

};

// 批量修改知识点卡片
const setBatchCategoryKnowledge = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let {updateBody} = req.body;
    // 数据校验 todo:
    
    if (!Array.isArray(updateBody)) {
        return resWrapper.error('PARAMETERS_ERROR');
    }

    for (let item of updateBody) {
        let opKeys = Object.keys(item);
        for (let key of opKeys) {
            if (!item[key].filter || !item[key].update) {
                return resWrapper.error('PARAMETERS_ERROR');
            }
            if (item[key].filter._id) {
                item[key].filter._id = ObjectID(item[key].filter._id);
            }
            if (item[key].update.$set.utime) {
                item[key].update.$set.utime = new Date(item[key].update.$set.utime);
            }
        }
    }

    if (!updateBody.length) {
        return resWrapper.succ({result: false});
    }

    await resourceCol.bulkWrite(updateBody);

    return resWrapper.succ({result: true});

};

// 获取知识点卡片
const getCategoryKnowledge = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let {ids, school_id, knowledgeIds} = req.query;
    ids = (ids || '').split(',').map(e => {
        return e.length === 24 ? ObjectID(e) : null;
    }).filter(e => e);
    knowledgeIds = (knowledgeIds || '').split(',').map(e => +e).filter(e => e);
    school_id = +school_id;

    try {
        let cond = {
            is_del: {$ne: true}
        };
        if (ids && ids.length) {
            cond._id = {$in: ids};
        }
        if (school_id) {
            cond.school_id = school_id;
        }
        if (knowledgeIds && knowledgeIds.length) {
            cond.knowledge_id = {$in: knowledgeIds};
        }
        let resources = await resourceCol.find(cond).project({is_del: 0}).toArray(); ;
        return resWrapper.succ(resources);
    } catch (error) {
        logger.error(error);
        return resWrapper.error('HANDLE_ERROR');
    }
};


module.exports = {
    saveBatchCategoryKnowledge,
    setBatchCategoryKnowledge,
    getCategoryKnowledge
};
