/**
 * 教辅校验schema
 */
const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const Logger = require('../../../common/utils/logger');

const postSchema = {
    type: 'object',
    additionalProperties: false,
    required: ['period', 'subject', 'name', 'province', 
    'profile_url', 'host','catalogs','pages'],
    properties: {
        province: {
            type: 'array'
        },
        period: {
            type: 'string'
        },
        subject: {
            type: 'string'
        },
        name: {
            type: 'string'
        },
        profile_url: {
            type: 'string',
        },
        host: {
            type: 'string',
        },
        catalogs: {
            type: 'array',
        },
        pages: {
            type: 'array',
        },
    }
};

const putSchema = {
    type: 'object',
    additionalProperties: false,
    required: ['id','period', 'subject', 'name', 'province',
    'profile_url', 'host','catalogs','pages'],
    properties: {
        id: {
            type: 'number'
        },
        province: {
            type: 'array'
        },
        period: {
            type: 'string'
        },
        subject: {
            type: 'string'
        },
        name: {
            type: 'string'
        },
        profile_url: {
            type: 'string',
        },
        host: {
            type: 'string',
        },
        catalogs: {
            type: 'array',
        },
        pages: {
            type: 'array',
        }
    }
};

const postValidate = (data) => {
    if (!ajv.validate(postSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('教辅格式错误');
    }
};

const putValidate = (data) => {
    if (!ajv.validate(putSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('教辅格式错误');
    }
};


module.exports = {
    postValidate,
    putValidate
};
