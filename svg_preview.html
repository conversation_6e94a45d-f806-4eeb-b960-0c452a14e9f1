<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教辅封面SVG预览</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .subject-section {
            margin-bottom: 40px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .subject-title {
            font-size: 24px;
            font-weight: bold;
            color: #555;
            margin-bottom: 20px;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
        }
        
        .covers-row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: flex-start;
        }
        
        .cover-item {
            text-align: center;
        }
        
        .cover-item svg {
            border: 1px solid #ddd;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .cover-item svg:hover {
            transform: scale(1.05);
        }
        
        .cover-label {
            margin-top: 8px;
            font-size: 12px;
            color: #666;
        }
        
        .download-section {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .download-section h2 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .download-section p {
            color: #555;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 K12校本教辅封面 - SVG版本</h1>
        
        <div class="download-section">
            <h2>📁 文件位置</h2>
            <p>所有SVG文件已保存在 <strong>svg_covers</strong> 目录中</p>
            <p>每个科目都有不同的设计变体，尺寸为 120×150 像素</p>
            <p>SVG格式支持无损缩放，适合各种用途</p>
        </div>

        <!-- 语文 -->
        <div class="subject-section">
            <div class="subject-title">📖 语文 (Chinese)</div>
            <div class="covers-row">
                <div class="cover-item">
                    <svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad_chinese_1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#8c2318;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#5d001e;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="120" height="150" fill="url(#grad_chinese_1)" rx="6"/>
                        <pattern id="grid1" patternUnits="userSpaceOnUse" width="15" height="15"><path d="M 15 0 L 0 0 0 15" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/></pattern>
                        <rect x="0" y="0" width="120" height="18" fill="rgba(255,255,255,0.1)" rx="6 6 0 0"/>
                        <line x1="10" y1="18" x2="110" y2="18" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                        <text x="60" y="12" font-family="Arial, sans-serif" font-size="9" font-weight="500" fill="white" text-anchor="middle">语文 | CHINESE</text>
                        <rect x="0" y="125" width="120" height="25" fill="rgba(0,0,0,0.1)" rx="0 0 6 6"/>
                        <rect width="120" height="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1" rx="6"/>
                    </svg>
                    <div class="cover-label">变体1</div>
                </div>
                <div class="cover-item">
                    <svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad_chinese_2" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#a26d60;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#63372c;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="120" height="150" fill="url(#grad_chinese_2)" rx="6"/>
                        <circle cx="105" cy="125" r="35" fill="white" opacity="0.1"/>
                        <rect x="0" y="0" width="120" height="18" fill="rgba(255,255,255,0.1)" rx="6 6 0 0"/>
                        <line x1="10" y1="18" x2="110" y2="18" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                        <text x="60" y="12" font-family="Arial, sans-serif" font-size="9" font-weight="500" fill="white" text-anchor="middle">语文 | CHINESE</text>
                        <rect x="0" y="125" width="120" height="25" fill="rgba(0,0,0,0.1)" rx="0 0 6 6"/>
                        <rect width="120" height="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1" rx="6"/>
                    </svg>
                    <div class="cover-label">变体2</div>
                </div>
                <div class="cover-item">
                    <svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad_chinese_3" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#780000;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#c1121f;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="120" height="150" fill="url(#grad_chinese_3)" rx="6"/>
                        <ellipse cx="60" cy="75" rx="55" ry="55" fill="rgba(255,255,255,0.2)" transform="rotate(20 60 75)"/>
                        <rect x="0" y="0" width="120" height="18" fill="rgba(255,255,255,0.1)" rx="6 6 0 0"/>
                        <line x1="10" y1="18" x2="110" y2="18" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                        <text x="60" y="12" font-family="Arial, sans-serif" font-size="9" font-weight="500" fill="white" text-anchor="middle">语文 | CHINESE</text>
                        <rect x="0" y="125" width="120" height="25" fill="rgba(0,0,0,0.1)" rx="0 0 6 6"/>
                        <rect width="120" height="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1" rx="6"/>
                    </svg>
                    <div class="cover-label">变体3</div>
                </div>
            </div>
        </div>

        <!-- 数学 -->
        <div class="subject-section">
            <div class="subject-title">🔢 数学 (Mathematics)</div>
            <div class="covers-row">
                <div class="cover-item">
                    <svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad_math_1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#005aa7;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#3a7bd5;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="120" height="150" fill="url(#grad_math_1)" rx="6"/>
                        <rect x="0" y="0" width="120" height="18" fill="rgba(255,255,255,0.1)" rx="6 6 0 0"/>
                        <line x1="10" y1="18" x2="110" y2="18" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                        <text x="60" y="12" font-family="Arial, sans-serif" font-size="9" font-weight="500" fill="white" text-anchor="middle">数学 | MATHEMATICS</text>
                        <rect x="0" y="125" width="120" height="25" fill="rgba(0,0,0,0.1)" rx="0 0 6 6"/>
                        <rect width="120" height="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1" rx="6"/>
                    </svg>
                    <div class="cover-label">变体1</div>
                </div>
                <div class="cover-item">
                    <svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad_math_2" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#2c3e50;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#4ca1af;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="120" height="150" fill="url(#grad_math_2)" rx="6"/>
                        <circle cx="-10" cy="140" r="40" fill="none" stroke="rgba(255,255,255,0.5)" stroke-width="8" opacity="0.15"/>
                        <rect x="0" y="0" width="120" height="18" fill="rgba(255,255,255,0.1)" rx="6 6 0 0"/>
                        <line x1="10" y1="18" x2="110" y2="18" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                        <text x="60" y="12" font-family="Arial, sans-serif" font-size="9" font-weight="500" fill="white" text-anchor="middle">数学 | MATHEMATICS</text>
                        <rect x="0" y="125" width="120" height="25" fill="rgba(0,0,0,0.1)" rx="0 0 6 6"/>
                        <rect width="120" height="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1" rx="6"/>
                    </svg>
                    <div class="cover-label">变体2</div>
                </div>
                <div class="cover-item">
                    <svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="grad_math_3" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#1a237e;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#283593;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="120" height="150" fill="url(#grad_math_3)" rx="6"/>
                        <rect x="0" y="0" width="120" height="18" fill="rgba(255,255,255,0.1)" rx="6 6 0 0"/>
                        <line x1="10" y1="18" x2="110" y2="18" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                        <text x="60" y="12" font-family="Arial, sans-serif" font-size="9" font-weight="500" fill="white" text-anchor="middle">数学 | MATHEMATICS</text>
                        <rect x="0" y="125" width="120" height="25" fill="rgba(0,0,0,0.1)" rx="0 0 6 6"/>
                        <rect width="120" height="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1" rx="6"/>
                    </svg>
                    <div class="cover-label">变体3</div>
                </div>
            </div>
        </div>

        <!-- 其他科目说明 -->
        <div class="subject-section">
            <div class="subject-title">📋 其他科目</div>
            <p>以下科目的SVG文件也已生成，请在 <strong>svg_covers</strong> 目录中查看：</p>
            <ul style="columns: 2; column-gap: 40px; margin: 20px 0;">
                <li>🇬🇧 英语 (English) - 3个变体</li>
                <li>⚛️ 物理 (Physics) - 2个变体</li>
                <li>🧪 化学 (Chemistry) - 2个变体</li>
                <li>🌱 生物 (Biology) - 2个变体</li>
                <li>📜 历史 (History) - 2个变体</li>
                <li>🌍 地理 (Geography) - 2个变体</li>
                <li>🏛️ 政治 (Politics) - 2个变体</li>
            </ul>
        </div>

        <div class="subject-section">
            <div class="subject-title">💡 使用说明</div>
            <ul>
                <li><strong>文件格式：</strong>所有封面都是SVG矢量格式，支持无损缩放</li>
                <li><strong>尺寸规格：</strong>120×150像素，符合原始设计要求</li>
                <li><strong>颜色方案：</strong>每个科目都有独特的配色和装饰图案</li>
                <li><strong>文件命名：</strong>格式为"科目名_variant-数字.svg"</li>
                <li><strong>编辑建议：</strong>可以用任何支持SVG的软件进一步编辑</li>
            </ul>
        </div>
    </div>
</body>
</html>
