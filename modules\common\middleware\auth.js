var Config = require('config');
var pathToRegexp = require('path-to-regexp');
var Logger = require('../utils/logger');
const rediser = require('../utils/rediser');
const cache = rediser.getCache();
var utils = require('../utils/utils');

// load APP_KEYS
var APP_KEYS = {};
(function() {
 	var _keys = Config.get("app_keys");
 	for (var i in _keys){
 		APP_KEYS[_keys[i]] = 1;
	}
 } ());

// load FREQ_LIMITS
var FREQ_LIMITS = {
	'min': Config.get('visit_freq.FREQ_LIMIT_PER_MIN'),
	'hour': Config.get('visit_freq.FREQ_LIMIT_PER_HOUR'),
	'day': Config.get('visit_freq.FREQ_LIMIT_PER_DAY'),
	'times': Config.get('visit_freq.TIMES')
};

var RAND_FREQ_LIMITS = {
	'cur_date': null,
	'min': null,
	'hour': null,
	'day': null,
	'ratio': null,
	'times': Config.get('visit_freq.TIMES'),
};

// load METHOD_PATHREG
var METHOD_PATHREG = {};
(function() {
 	var apis = Config.get('visit_freq.APIS'); 
	for (var k in apis) {
		var arr = k.split(' ');
		var method = arr[0].toUpperCase();
		var reg = pathToRegexp(arr[1], []);
		if (METHOD_PATHREG.hasOwnProperty(method)) {
			METHOD_PATHREG[method].push({
				'reg': reg,
				'api_name': k,
			});
		} else {
			METHOD_PATHREG[method] = [{
				'reg': reg,
				'api_name': k,
			}];
		}
	}
 }());

/*
 * Desc:
 *      get api name by request.path and request.method
 * Params:
 *      req - request
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2017-03-29
*/
function get_api_name(req) {
	var method = req.method.toUpperCase();
	var path = req.path;
	var pathregs = METHOD_PATHREG[method];
	for (var i in pathregs) {
		var desc  = pathregs[i];
		var m = desc.reg.exec(path);
		if (m) {
			return desc.api_name;
		}
	}
}

/*
 * Desc:
 *      check certification
 * Params:
 *      req - request
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-29
*/
function auth_validator(req, callback) {	
	var path = req.path;
	if ("/" != path[path.length-1]) {
		path += "/";
	}
	var arr = path.split('/');
	if ("favicon.ico" == arr[1]) {
		return callback(null, null)
	}

	if ("download" == arr[1]) {
		return callback(null, null)
	}

	if ("health_check" == arr[1] || 
		"/kb_api/v2/exampaper/download/" == path ||
		"/kb_api/v2/questions/profile/" == path) {
		return callback(null, null);
	} else {
		var api_key = get_api_key(req);
		if (APP_KEYS.hasOwnProperty(api_key)) {
			req.inner_visit = true;
			return callback(null, null);
		} else {
			var api_name = get_api_name(req);

			check_api_key(api_key, api_name, req.query.freq === true, function(err, arg){
				return callback(err, arg);
			});
		}
	}
}

/*
 * Desc:
 *      add a random number on frequence limits
 * Params:
 *      param1 - comment
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2017-04-11
*/
function limits_random() {
	var date_str = new Date().toLocaleDateString();
	if (date_str != RAND_FREQ_LIMITS['cur_date']) {
		var ratio = Math.random() / 7.0 + 0.8;
		RAND_FREQ_LIMITS.cur_date = date_str;
		RAND_FREQ_LIMITS.ratio = ratio;
		RAND_FREQ_LIMITS.min = Math.floor(FREQ_LIMITS.min*ratio);
		RAND_FREQ_LIMITS.hour = Math.floor(FREQ_LIMITS.hour*ratio);
		RAND_FREQ_LIMITS.day = Math.floor(FREQ_LIMITS.day*ratio);
	}
	return ;
}

/*
 * Desc:
 *      获取通行证
 * Params:
 *      req - http请求信息 
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-29
*/
function get_api_key(req) {
	var api_key = req.query['api_key'];
	var app_key = req.query['app_key'];
	if (api_key) {
		return api_key;
	}
	if (app_key) {
		return app_key;
	}
	return null;
}

/*
 * Desc:
 *      check whether api key is legal
 * Params:
 *      api_key - api key
 *      api_name - api name 
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-29
*/
function check_api_key(api_key, api_name, userId, callback) {
	if (!api_key) {
		return callback('AUTH_ERROR');
	}
	freq_validator(`tiku:user:${userId}`, api_name, function(err, arg) {
		if (!!err === true) {
			Logger.warn({
				who: api_key,
				where: `${__filename} check_api_key`,
				what: 'AUTH_ERROR',
				why: err ? err.message : `arg is ${arg}`
			});
		}
		return callback(err, arg);
	});
}

/*
 * Desc:
 *      compare two time sequence
 * Params:
 *      l_seq - one sequence
 *      r_seq - the other sequence
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2017-03-29
*/
function compare_time_seq(l_seq, r_seq) {
	if (l_seq == r_seq) {
		return {day: true, hour:true, min:true};
	}
	var l_t = utils.parse_time_seq(l_seq);
	var r_t = utils.parse_time_seq(r_seq);

	// compare last visit time and current time
	var equality = {};
	equality.day = (l_t.day == r_t.day);
	equality.hour = (l_t.hour == r_t.hour);
	equality.min = (l_t.min == r_t.min);

	return equality;
}


/*
 * Desc:
 *      desc
 * Params:
 *      user_tag - user_tag
 *      api_name - api_name
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-29
*/
function freq_validator(user_tag, api_name, callback) {
	var seq = utils.get_time_seq(new Date());
	// incr counter
	cache.hgetall(user_tag, function(err, data) {
		if (err) {
			return callback('AUTH_ERROR');
		}
		var last_visit_time = parseInt(data.last_visit_time);
		var equality = compare_time_seq(seq, last_visit_time);
		// update redis counter
		if (equality.day) { // day
			cache.hincrby(user_tag, 'day', 1);
		} else {
			cache.hmset(user_tag, 'day', 1);
		}
		if (equality.hour) { // hour
			cache.hincrby(user_tag, 'hour', 1);
		} else {
			cache.hmset(user_tag, 'hour', 1);
		}
		if (equality.min) { // min
			cache.hincrby(user_tag, 'min', 1);
		} else {
			cache.hmset(user_tag, 'min', 1);
		}
		// update redis last_visit_time
		cache.hmset(user_tag, 'last_visit_time', seq);
	});

	is_illegal_user(user_tag, function(err) { // 是否在黑名单
		if (err) {
			return callback(err);
		}
		limits_random(); // 每天更新一次当天的超频限制数据
		is_exceed_global_freq(user_tag, function(err) { // 总数是否超限
			if (err) {
				return callback(err);
			}
			is_exceed_api_freq(user_tag, api_name, function(err) { // api访问是否超限
				if (err) {
					return callback(err);
				}
				return callback(null, null);
			});	
		});
	});
}

/*
 * Desc:
 *      判断用户是否在黑名单中
 * Params:
 *      user_tag - user_tag
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-30
*/
function is_illegal_user(user_tag, callback) {
	// if user in black list, check whether forbid this request
	cache.hget(user_tag, 'out_of_limits', function(err, out_of_limits) {
		if (err) {
			return callback('AUTH_ERROR');
		}
		var day_of_year	= utils.get_day_of_year(new Date());
		var exceed_cnt = 0;
		var out_of_limits = out_of_limits ? out_of_limits.split(',') : [];
		for (var i in out_of_limits) {
			if (parseInt(out_of_limits[i]) > day_of_year-7) 
				exceed_cnt++;
		}
		if (exceed_cnt >= RAND_FREQ_LIMITS.times) {
			return callback('ILLEGAL_USER');
		} else {
			return callback(null, null);
		}
	});	
}

/*
 * Desc:
 *      判断用户访问是否超过天、时、分的限制
 * Params:
 *      user_tag - user_tag 
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-30
*/
function is_exceed_global_freq(user_tag, callback) {
	// if frequence out of limit, then forbid this request
	cache.hgetall(user_tag, function(err, count_d){
		if (err) {
			return callback('AUTH_ERROR');
		}
		//var user_tag = count_d['user_tag'];
		var d_cnt = count_d.day;
		var h_cnt = count_d.hour;
		var m_cnt = count_d.min;
		if (m_cnt > RAND_FREQ_LIMITS.min ||
			h_cnt > RAND_FREQ_LIMITS.hour ||
			d_cnt > RAND_FREQ_LIMITS.day) { // 超频
			var out_of_limits = count_d.out_of_limits ? count_d.out_of_limits.split(',') : [];
			// 保留过去七天内超频数据
			if (d_cnt > RAND_FREQ_LIMITS.day) {
				var day_of_year	= utils.get_day_of_year(new Date());
				if (out_of_limits.indexOf(String(day_of_year)) < 0) {
					var f = function(out_of_limits){
						var arr = []; 
						for (var i in out_of_limits) {
							if (parseInt(out_of_limits[i]) > day_of_year-7){
								arr.push(out_of_limits[i]);
							}
						}
						return arr;	
					}
					var _out_of_limits = f(out_of_limits);
					_out_of_limits.push(String(day_of_year));
					// update ttl and content
					cache.ttl(user_tag, function(err, t) {
						if (err) {
							return callback('EXCEED_FRQ_ERROR');
						}
						cache.hmset(user_tag, 'out_of_limits', _out_of_limits.join(','));
						var left_days = parseInt(t / 24 / 3600);
					    cache.expire(user_tag, t + (7-left_days)*3600*24);
					});
				}
				return callback('EXCEED_FRQ_ERROR');
			} else {
				return callback('EXCEED_FRQ_ERROR');
			}
		} else {
			return callback(null, null);
		}
	});
}

/*
 * Desc:
 *      判断用户访问是否超过天、时、分的限制
 * Params:
 *      user_tag - user_tag 
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-30
*/
function is_exceed_api_freq(user_tag, api_name, callback) {
	// if frequence out of limit, then forbid this request
	var _limits = Config.get('visit_freq.APIS')[api_name];
	if (!_limits) {
		return process.nextTick(function(){
			return callback(null, null);
		});
	}
	// limits randomize
	var limits = {};
	limits.day = Math.floor(_limits.day * RAND_FREQ_LIMITS.ratio);
	limits.hour = Math.floor(_limits.hour * RAND_FREQ_LIMITS.ratio);
	limits.min = Math.floor(_limits.min * RAND_FREQ_LIMITS.ratio);
	// check
	cache.hget(user_tag, api_name, function(err, data){
		if (err) {
			return callback('AUTH_ERROR');
		}
		var arr = null;
		if (!data) {
			arr = [seq, 0];
		} else {
			arr = data.split('_');
		}
		var last_visit_time = parseInt(arr[0]);
		var cnt = parseInt(arr[1]);
		var seq = utils.get_time_seq(new Date());
		var equality = compare_time_seq(seq, last_visit_time);
		var d = utils.tiny_decode(cnt, 8192);
		// update counter
		d.day = equality.day ? d.day+1 : 1;
		d.hour = equality.hour ? d.hour+1 : 1;
		d.min = equality.min ? d.min+1 : 1;

		if (d.day > limits.day ||
			d.hour > limits.hour || 
			d.min > limits.min) {
			return callback('EXCEED_FRQ_ERROR');
		} else {
			var cnt = utils.tiny_encode(d.day, d.hour, d.min, 8192);
			data = seq + '_' + cnt;
			// update value 
			cache.hmset(user_tag, api_name, data);
			return callback(null, null);
		}
	});
}

module.exports = {
	auth_validator: auth_validator,
}
