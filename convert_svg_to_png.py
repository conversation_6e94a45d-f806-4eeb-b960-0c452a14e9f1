#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SVG转PNG工具 - 将生成的SVG封面转换为PNG格式
需要安装: pip install cairosvg pillow
"""

import os
import sys
from pathlib import Path

def check_dependencies():
    """检查必要的依赖"""
    try:
        import cairosvg
        from PIL import Image
        return True
    except ImportError as e:
        print(f"缺少必要的依赖: {e}")
        print("请运行以下命令安装依赖:")
        print("pip install cairosvg pillow")
        return False

def convert_svg_to_png(svg_path: str, png_path: str, scale: float = 1.0):
    """将SVG转换为PNG"""
    try:
        import cairosvg
        
        # 转换SVG到PNG
        cairosvg.svg2png(
            url=svg_path,
            write_to=png_path,
            output_width=int(120 * scale),
            output_height=int(150 * scale)
        )
        return True
    except Exception as e:
        print(f"转换失败 {svg_path}: {e}")
        return False

def main():
    """主函数"""
    if not check_dependencies():
        return
    
    # 检查SVG目录
    svg_dir = Path("svg_covers")
    if not svg_dir.exists():
        print("错误: svg_covers 目录不存在")
        print("请先运行 extract_covers_to_svg.py 生成SVG文件")
        return
    
    # 创建PNG输出目录
    png_dir = Path("png_covers")
    png_dir.mkdir(exist_ok=True)
    
    # 获取所有SVG文件
    svg_files = list(svg_dir.glob("*.svg"))
    if not svg_files:
        print("错误: svg_covers 目录中没有找到SVG文件")
        return
    
    print(f"找到 {len(svg_files)} 个SVG文件")
    
    # 转换选项
    scales = {
        "1x": 1.0,    # 120x150
        "2x": 2.0,    # 240x300
        "4x": 4.0,    # 480x600
    }
    
    print("选择转换分辨率:")
    print("1. 1x (120x150) - 原始尺寸")
    print("2. 2x (240x300) - 2倍尺寸")
    print("3. 4x (480x600) - 4倍尺寸")
    print("4. 全部尺寸")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == "1":
        selected_scales = {"1x": 1.0}
    elif choice == "2":
        selected_scales = {"2x": 2.0}
    elif choice == "3":
        selected_scales = {"4x": 4.0}
    elif choice == "4":
        selected_scales = scales
    else:
        print("无效选择，使用默认1x尺寸")
        selected_scales = {"1x": 1.0}
    
    # 转换文件
    total_files = len(svg_files) * len(selected_scales)
    current = 0
    
    for svg_file in svg_files:
        for scale_name, scale_value in selected_scales.items():
            current += 1
            
            # 生成PNG文件名
            base_name = svg_file.stem
            if len(selected_scales) > 1:
                png_name = f"{base_name}_{scale_name}.png"
            else:
                png_name = f"{base_name}.png"
            
            png_path = png_dir / png_name
            
            print(f"[{current}/{total_files}] 转换: {svg_file.name} -> {png_name}")
            
            if convert_svg_to_png(str(svg_file), str(png_path), scale_value):
                print(f"  ✓ 成功")
            else:
                print(f"  ✗ 失败")
    
    print(f"\n转换完成！PNG文件保存在 {png_dir} 目录中")

if __name__ == "__main__":
    main()
