const english = {
    type: 'object',
    additionalProperties: false,
    required: ['word', 'phonogram', 'word_types','period','subject'],
    properties: {
        word: {
            type: 'string'
        },
        phonogram: {
            type: 'string'
        },
        word_types: {
            type: 'array'
        },
        period: {
            type: 'string'
        },
        subject:{
            type: 'string'
        },
        formula: {
            type: 'string'
        },
        usage: {
            type: 'string'
        },
        collocation: {
            type: 'string'
        },
        sentence_pattern: {
            type: 'string'
        },
        example_sentence: {
            type: 'string'
        },
        discriminate: {
            type: 'string'
        },
        easily_wrong_point: {
            type: 'string'
        },
        conclude: {
            type: 'string'
        },
        derives: {
            type: 'array'
        },
        associates: {
            type: 'array'
        },
    }
};


module.exports = {
    english,
};
