/**
/**
 * 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 9500,
    app_key: 'iyunxiao_kbplat180305',
};

const getUrl = (pathname) => {
    let url = URL.format({
        protocol: 'http',
        hostname: localEnv.host,
        port: localEnv.port,
        pathname: pathname,
        search: qs.stringify({
            api_key: localEnv.app_key
        })
    });
    return url;
}

describe('对象列表', function () {
    it.only('/kb_api/v2/target_groups/list', async function () {
        let pathname = `/kb_api/v2/target_groups/list`;
        let url = getUrl(pathname);
        let data = {
            subject: '化学',
            period: '初中',
            limit: 3,
            offset: 0
        }
        let result = await request.get(url).query(data);
        console.log(result.body);
        //console.log(JSON.stringify(result.body));
        assert.ok(result.body.list.length >= 0);
        assert.ok(result.body.total_num >= 0);
    });
});

describe('详情byId', function () {
    it('/kb_api/v2/target_groups/:id', async function () {
        let id = '5db91086c5a8927891261ea0';
        let pathname = `/kb_api/v2/target_groups/${id}`;
        let url = getUrl(pathname);
        let data = {}
        let result = await request.get(url).query(data);
        console.log(JSON.stringify(result.body));
        assert.ok(result.body);
    });
});

describe('是否存在组名称', function () {
    it('/kb_api/v2/target_groups/:id', async function () {
        let pathname = `/kb_api/v2/target_groups/repeat`;
        let url = getUrl(pathname);
        let data = {
            subject: '化学',
            period: '初中',
            name: '化学组3',
            id: ''
        }
        let result = await request.get(url).query(data);
        console.log(JSON.stringify(result.body));
        assert.ok(result.body);
    });
});

describe('对象添加', function () {
    this.timeout(60000);
    it('/kb_api/v2/target_groups', async function () {
        let pathname = `/kb_api/v2/target_groups`;
        let url = getUrl(pathname);
        let data = {
            name: '10',
            subject: '化学',
            period: '初中',
            children: [{
                id: 1,
                name: '对象1',
            }, {
                id: 2,
                name: '对象2',
            }]
        };
        try {
            let result = await request.post(url).send(data);
            console.log(result.body); // {}
            assert.ok(!!result.body);
        } catch (e) {
            console.log(e.message);
        }
    });
});

describe('更新', function () {
    it('/kb_api/v2/target_groups/:id', async function () {
        let id = "5db91086c5a8927891261ea0";
        let pathname = `/kb_api/v2/target_groups/${id}`;
        let url = getUrl(pathname);
        let data = {
            name: '化学组3',
            subject: '化学',
            period: '初中',
            children: [{
                id: 222,
                name: '对象22',
            }, {
                id: 7,
                name: '对象42',
            }]
        };
        let result = await request.put(url).send(data);
        console.log(result.body);
        assert.ok(!!result.body);
    });
});

describe('删除', function () {
    it('/kb_api/v2/target_groups/:id', async function () {
        let id = '5db81b010f48a863cba0a006';
        let pathname = `/kb_api/v2/target_groups/${id}`;
        let url = getUrl(pathname);
        let result = await request.delete(url);
        console.log(result.body);
        assert.ok(!!result.body);
    });
});