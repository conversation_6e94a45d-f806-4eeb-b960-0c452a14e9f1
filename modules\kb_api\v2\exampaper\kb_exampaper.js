const Thenjs = require('thenjs');
const _ = require('underscore');
const Logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const utils = require('../../../common/utils/utils.js');
const params_utils = require('../../../common/utils/params.js');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../../common/utils/rediser');
const kb_api = require('../../config.js').kb_api['v2'];
const utilsBiz = require('../utils.js');
const query_questions = require('../question').query_questions;
const resource = require('../resource');
const builtInUtil = require('util');
const crypto = require('crypto');
const query_questionsPromise = builtInUtil.promisify(query_questions);
const axios = require('axios');
const url = require('url');
const qs = require('qs');
const Config = require('config');
const mapExampaperName = require('./mapExampaperName.js');
// const filterExampaperName = require('./filterExampaperName');

const DifficultyRange = {
    '不限': [1, 2, 3, 4, 5],
    '容易': [1, 2, 3],
    '普通': [2, 3, 4],
    '困难': [3, 4, 5]
};

/**
 * 获取试卷分类信息
 * @param {*} req
 * @param {*} res
 * @return {*}
 * @example
 * GET http://kboe.yunxiao.com/kb_api/v2/exampapers/categorys/
 */
function exampaper_cats(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.warn({
            who: req.query.api_key,
            where: `${__filename} exampaper_cats`,
            what: 'PARAMETERS_ERROR',
            why: e instanceof Error ? e.message : e,
            how: req.originalUrl
        });
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    const queryDbs = new Set((req.query.dbs || 'basic').split(','));
    // 数据查询 ----------------------------
    Thenjs(function (cont) {
        let cache_key = params['cache_key'];
        rediser.get(cache_key, function (err, items) {
            if (err) {
                return cont(err);
            }
            if (!items) {
                let coll = db.collection('exampaper_category');
                // 查询条件
                let cond = {};
                let keys = ['period', 'subject'];
                for (let k of keys) {
                    if (params[k]) {
                        cond[k] = params[k];
                    }
                }
                cond['invalid'] = { $in: [null, 0] };
                // 提取出的字段
                let proj = {};
                // 查询
                coll.find(cond).project(proj).sort({ _id: 1 }).toArray(function (err, items) {
                    if (err) {
                        return cont(err);
                    }
                    items = (items || []).filter(exampaper_category => (exampaper_category.dbs || ['basic']).some(db => queryDbs.has(db)));

                    if (items.length > 0) {
                        try {
                            let keys = [
                                ['period', 'period'],
                                ['subject', 'subject'],
                                ['press_version', 'press_version'],
                                ['grade', 'grade'],
                            ];
                            items = utilsBiz.cols_to_tree('category', items, keys, 'category_id');
                        } catch (e) {
                            return cont(e);
                        }
                        rediser.set(cache_key, items, 60 * 30);
                        return cont(null, items);
                    }
                    return cont(null, null);

                });
            } else {
                return cont(null, items);
            }
        });
    }).then(function (cont, result) {
        if (result) {
            return responseWrapper.succ(result);
        }
        return responseWrapper.error('NULL_ERROR');
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

/**
 * 通过 region 过滤试卷
 * @param {*} papers    - 待过滤的试卷
 * @param {*} province  - 省
 * @param {*} city      - 市
 * @return {*}
 */
function filter_papers(papers, province, city) {
    if (null == papers || papers.length === 0 ||
        ((null == province || '' === province) &&
            (null == city || '' === city))) {
        return papers;
    }
    let year_owndata_d = {}; // key:year, value:是否拥有本地区自主命题卷
    for (let paper of papers) {
        let year = paper['to_year'];
        if (!year_owndata_d.hasOwnProperty(year)) {
            year_owndata_d[year] = false;
        }
        let own = year_owndata_d[year];
        if (!own) {
            if (paper.province === province) {
                year_owndata_d[year] = true;
            }
        }
    }
    // filter data
    let result = [];
    for (let paper of papers) {
        let year = paper['to_year'];
        if (year_owndata_d[year] && province !== paper.province) {
            // 本年度存在自主命题试卷
            continue;
        }
        result.push(paper);
    }
    return result;
}

/**
 * 获取试卷中试题数量
 * @param {*} data    - 试卷结构
 * @return {*}
 */
function getQuesNum(data) {
    let num = 0;
    if (Array.isArray(data)) {
        for (let i = 0, l = data.length; i < l; i++) {
            if (Array.isArray(data[i].questions)) {
                let questions = data[i].questions.filter(item => !isNaN(Number(item.id)));
                num += questions.length;
            }
        }
    }
    return num;
}

/**
 * 通过试卷分类 id 获取试卷列表
 * GET http://kboe.yunxiao.com/kb_api/v2/exampapers/categorys/{category_id}/
 * @param {*} req
 * @param {*} res
 * @return {*}
 * @example
 */
function exampaper_category(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        let conf = kb_api[fun_name];
        params = params_utils.create_params(req, conf);
    } catch (e) {
        Logger.warn({
            who: req.query.api_key,
            where: `${__filename} exampaper_category`,
            what: 'PARAMETERS_ERROR',
            why: e instanceof Error ? e.message : e,
            how: req.originalUrl
        });
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    const queryDbs = new Set((req.query.dbs || 'basic').split(','));
    Thenjs(function (cont) {
        let coll = db.collection('exampaper');
        // 查询条件
        let cond = { 'category_id': params.category_id };
        let fromCond = new Map([
            ['kbp', 'kbp_new'],
            ['mkp', 'mkp'],
            ['drm', 'drm'],
            ['other', 'other'],
        ]).get(params.source_platform);
        if (fromCond) {
            cond.from = fromCond;
        }

        if (params.from === 'hfs') {
            cond.from = 'mkp';
        }
        if (params.is_elite) {
            cond.is_elite = params.is_elite;
        }
        if (params.year) {
            cond.from_year = { $lte: params.year };
            cond.to_year = { $gte: params.year };
        }
        utilsBiz.setCondProvinces(cond, params);
        let exam_type = params.exam_type;
        if (exam_type) {
            cond.type = params.exam_type;
        }
        let sort_by = params.sort_by;
        // 提取出的字段
        let proj = {
            '_id': 1,
            'name': 1,
            'province': 1,
            'provinces': 1,
            'city': 1,
            'from_year': 1,
            'to_year': 1,
            'is_elite': 1,
            'view_times': 1,
            'download_times': 1,
            'type': 1,
            'ctime': 1,
            'user_name': 1,
            'user_id': 1,
            'from': 1,
            'dbs': 1,
        };
        let max_no = 1000;
        let limit = params.limit;
        let offset = params.offset > max_no ? max_no : params.offset;
        // 查询
        let r = {
            'total_num': 0,
            'exampapers': [],
        };
        let query_cnt = 2;
        coll.count(cond, { limit: 1000 }).then(function (total_num) {
            r.total_num = total_num;
            if (--query_cnt === 0) {
                return cont(null, r);
            }
        });
        coll.find(cond).project(proj).limit(limit).skip(offset).sort(sort_by).toArray(function (err, items) {
            if (err) {
                return cont(err);
            }
            try {
                // 试卷 权限过滤
                items = (items || []).filter(exampaper => (exampaper.dbs || ['basic']).some(item => queryDbs.has(item)));
                if (!items || items.length === 0) {
                    cont(null, null);
                }
                items = filter_papers(items, params.province, params.city);
                let sourcePlatformMap = new Map([
                    ['kbp_new', '内容管理平台'],
                    ['mkp', '标注平台'],
                    ['drm', '数字化平台'],
                    [undefined, '其它平台'],
                    ['other', '其他平台'],
                    ['digital_exampaper', '标注平台'],
                ]);
                for (let item of items) {
                    item.source_platform = sourcePlatformMap.get(item.from);
                    item['id'] = item['_id'];
                    delete item['_id'];
                    utilsBiz.getProvinceCity(item, params);
                }
                if (req.out_of_limits) { // 超频
                    // 返回部分数据
                    let num = Math.floor(Math.random() * 6) + 5;
                    items = items.slice(0, num);
                    // 污染试卷 name, _id
                    for (let item of items) {
                        item.id = utilsBiz.dirty_id(item.id, 131071); // 131072=2^17
                        item.name = utilsBiz.dirty_string(item.name, 5);
                    }
                }
            } catch (e) {
                return cont(e);
            }
            if (items.length === 0) {
                return responseWrapper.succ({
                    'total_num': 0,
                    'exampapers': [],
                });
            }
            r.exampapers = items;
            if (--query_cnt === 0) {
                return cont(null, r);
            }
        });
    }).then(function (cont, result) {
        if (result) {
            return responseWrapper.succ(result);
        }
        return responseWrapper.error('NULL_ERROR');
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

// 通过搜索引擎获取试卷列表的条件
const exampaperBySearchParams = (params, api_key) => {
    let cond = {};
    if (params.period) {
        cond['period'] = params.period;
    }
    if (params.subject) {
        cond['subject'] = params.subject;
    }
    if (params.press_version) {
        cond['press_version'] = params.press_version;
    }
    if (params.grade) {
        cond['grade'] = params.grade;
    }
    if (params.name) {
        cond['name'] = params.name;
    }
    if (params.difficulty) {
        cond['difficulty'] = params.difficulty;
    }
    if (params.chapter_ids) {
        cond['chapter_ids'] = params.chapter_ids;
    }
    if (params.chapter_id) {
        cond['chapter_id'] = params.chapter_id;
    }
    utilsBiz.setSeProvinces(cond, params);
    if (params.type) {
        cond['type'] = params.type;
    }
    if (params.category_id) {
        cond['category_id'] = params.category_id;
    }
    if (params.need_ques_num) {
        cond['need_ques_num'] = params.need_ques_num;
    }
    if (params.filter_mkp) {
        cond['filter_mkp'] = params.filter_mkp;
    }
    if (params.from) {
        let from = {
            kbp: 'kbp_new',
            mkp: 'mkp',
            drm: 'drm',
            'drm,drm_yuanpei': 'drm,drm_yuanpei',
            mt: 'mt',
            other: { '$ne': 'kbp_new,mkp,drm,drm_yuanpei,mt' }
        };
        let _from = from[params.from];
        if (_from) {
            cond['from'] = _from;
        }
    }
    if (params.to_year) {
        cond['to_year'] = params.to_year;
    }
    if (params.sch_name) {
        cond['sch_name'] = params.sch_name;
    }
    if (Array.isArray(params.album_type) && params.album_type.length) {
        cond['album_type'] = params.album_type;
        // 如果album_type是[1]，则默认sort_by是rtime
        if (params.album_type.includes(1) || params.album_type.includes(2)) {
            params.sort_by = 'rtime';
        }
    } else {
        cond['album_type'] = [0];
    }
    if (params.is_show) {
        cond['is_show'] = params.is_show;
    }
    if (params.cyear) {
        cond['cyear'] = params.cyear;
    }
    if (params.cyear_range) {
        cond['cyear_range'] = params.cyear_range;
    }
    cond['dbs'] = params.dbs || ['basic'];


    if (params.sort_by) {
        let sort_by = {
            'year_utime': [{
                to_year: -1,
                utime: -1
            }],
            year: [{
                to_year: -1,
                ctime: -1
            }],
            view_times: [{
                view_times: -1
            }],
            ctime: [{
                ctime: -1
            }],
            utime: [{
                utime: -1
            }],
            rtime: [{
                rtime: -1
            }],
            integrated: [
                { to_year: -1 },
                { view_times: -1 },
                { download_times: -1 },
                { ctime: -1 }
            ],
        };
        let _sort_by = sort_by[params.sort_by];
        if (_sort_by) {
            cond['sort_by'] = _sort_by;
        }
    } else {
        cond['sort_by'] = [{
            to_year: -1
        }];
    }
    // 获取所属平台
    let platformArr = ['iyunxiao_mt', 'iyunxiao_tiku', 'iyunxiao_mkp', 'iyunxiao_kbp', 'iyunxiao_drm'];
    let platform = platformArr.find(item => api_key.indexOf(item) > -1) || 'iyunxiao_kbp';
    let platformFilter = {
        'iyunxiao_mt': 'mt',
        'iyunxiao_tiku': 'tiku',
        'iyunxiao_mkp': 'mkp',
        'iyunxiao_kbp': 'kbp',
        'iyunxiao_drm': 'drm'
    };
    platform = platformFilter[platform];
    // cond['platform'] = platform;
    cond[platform] = true;
    cond['offset'] = parseInt(params.offset) || 0;
    cond['limit'] = parseInt(params.limit) || 10;
    return cond;
};

// 查询出的字段
const getExampaperProject = (need_ques_num) => {
    let obj = {
        '_id': 1,
        'name': 1,
        'vague_name': 1,
        'province': 1,
        'provinces': 1,
        'city': 1,
        'from_year': 1,
        'to_year': 1,
        'is_elite': 1,
        'view_times': 1,
        'download_times': 1,
        'type': 1,
        'ctime': 1,
        'utime': 1,
        'rtime': 1,
        'user_name': 1,
        'user_id': 1,
        'from': 1,
        'press_version': 1,
        'grade': 1,
        'period': 1,
        'subject': 1,
        'album_type': 1, // 成品卷
        'cyear': 1, // 成品卷
        'is_show': 1, // 成品卷
        'category_id': 1, // 成品卷
        'chapter_id': 1 // 成品卷

    };
    if (need_ques_num) {
        obj.blocks = 1;
    }
    return obj;
};

/**
 * kbp3.3新增，对/se_kb/v2/filter/exampapers进行封装，通过搜索引擎实现各个字段获得试卷列表
 * POST http://kboe.yunxiao.com/kb_api/v2/exampapers/by_search
 * @param {*} req
 * @param {*} res
 * @return {*}
 * @example
 */
async function exampaperBySearch(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let params = params_utils.create_params(req, kb_api['exampaper_by_search']);
        let cond = exampaperBySearchParams(params, req.query.api_key);
        let uri = url.format({
            protocol: Config.SE_API_SERVER.protocol,
            hostname: Config.SE_API_SERVER.hostname,
            port: Config.SE_API_SERVER.port,
            pathname: '/se_kb/v2/filter/exampapers',
            search: qs.stringify({
                api_key: Config.SE_API_SERVER.appKey,
            })
        });
        if (req.query.api_key.indexOf('tiku') > -1) {
            cond['serv_range'] = 'public';
        }
        let result = await axios.post(uri, cond, { timeout: 10000 });
        let resultData = result.data;
        if (resultData.code === 0) {
            let searchData = resultData.data.datas || [];
            let proj = getExampaperProject(cond['need_ques_num']);
            // 不展示字段
            delete proj.user_id;
            delete proj.user_name;
            let exampapers = await db.collection('exampaper').find({ _id: { '$in': searchData } }, { readPreference: 'secondaryPreferred' }).project(proj).toArray();
            exampapers = filter_papers(exampapers, cond.province, cond.city);
            // 需要试题数量时，进行处理
            if (cond['need_ques_num']) {
                for (let i = 0, l = exampapers.length; i < l; i++) {
                    exampapers[i].ques_num = getQuesNum(exampapers[i].blocks);
                    delete exampapers[i].blocks;
                }
            }
            exampapers = utilsBiz.to_sort(searchData, exampapers, '_id');
            let sourcePlatformMap = new Map([
                ['kbp_new', '内容管理平台'],
                ['mkp', '标注平台'],
                ['drm', '先知工厂'],
                ['drm_yuanpei', '先知工厂'],
                ['mt', '命题平台'],
                [undefined, '其他平台'],
                ['other', '其他平台']
            ]);
            let today_time = new Date()
            for (let exampaper of exampapers) {
                exampaper.source_platform = sourcePlatformMap.get(exampaper.from);
                exampaper['id'] = exampaper['_id'];
                delete exampaper['_id'];
                // exampaper.vague_name = filterExampaperName(exampaper);
                exampaper.name = mapExampaperName(exampaper, req.query);
                utilsBiz.getProvinceCity(exampaper, params);
                exampaper.from = '';
            }
            if (req.out_of_limits) { // 超频
                // 返回部分数据
                let num = Math.floor(Math.random() * 6) + 5;
                exampapers = exampapers.slice(0, num);
                // 污染试卷 name, _id
                for (let exampaper of exampapers) {
                    exampaper.id = utilsBiz.dirty_id(exampaper.id, 131071); // 131072=2^17
                    exampaper.name = utilsBiz.dirty_string(exampaper.name, 5);
                }
            }
            responseWrapper.succ({ total_num: resultData.data.total, exampapers: exampapers });
        } else {
            responseWrapper.succ({ total_num: 0, exampapers: [] });
        }
    } catch (err) {
        Logger.error(err);
        responseWrapper.error('HANDLE_ERROR', err.message || err);
    }
}

/**
 * 根据试卷 id 取试卷内容信息
 * GET http://kboe.yunxiao.com/kb_api/v2/exampapers/{exampaper_id}/
 * @param {*} req
 * @param {*} res
 * @return {*}
 */
function exampaper(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.warn({
            who: req.query.api_key,
            where: `${__filename} exampaper`,
            what: 'PARAMETERS_ERROR',
            why: e instanceof Error ? e.message : e,
            how: req.originalUrl
        });
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    const queryDbs = new Set((req.query.dbs || 'basic').split(','));
    // 数据查询 ----------------------------
    Thenjs(function (cont) {
        // 更新view_times
        let coll = db.collection('exampaper');
        // 查询条件
        let cond = { '_id': params['exampaper_id'] };
        // 判断是否是好分数数据
        if (params.from === 'hfs') {
            cond.g_paper_id = { $exists: 1 };
        }
        // 字段更新
        let update_fields = { '$inc': { 'view_times': 1 }, };
        // 查询
        coll.updateOne(cond, update_fields, function (err, result) {
            if (err || result.result.n !== 1) {
                return cont(err);
            }
            return cont(null, null);
        });
    }).then(function (cont) {
        let coll = db.collection('exampaper');
        // 查询条件
        let cond = { '_id': params['exampaper_id'] };
        // 判断是否是好分数数据
        if (params.from === 'hfs') {
            cond.g_paper_id = { $exists: 1 };
        }
        // 提取出的字段
        let proj = params['fields_type'];
        proj.ctime = 1;
        proj.from = 1;
        proj.statistics = 1;
        proj.use_type = 1;
        /* kbp3.15成品卷 */
        proj.album_type = 1;
        proj.is_show = 1;
        proj.category_id = 1;
        proj.chapter_id = 1;
        proj.difficulty_proportion = 1;
        proj.cyear = 1;
        /* kbp3.15成品卷 */
        /* kbp3.18AI测评卷 */
        proj.grade_semester = 1;
        proj.chapter_ids = 1;
        proj.knowledges = 1;
        /* kbp3.18AI测评卷 */
        // 教辅资料
        proj.school_id = 1;
        proj.book_name = 1;
        proj.chapter_name = 1;
        proj.dbs = 1;
        // 查询
        coll.find(cond).project(proj).toArray(function (err, items) {
            if (err) {
                return cont(err);
            }
            // 试卷 权限过滤
            items = (items || []).filter(exampaper => (exampaper.dbs || ['basic']).some(db => queryDbs.has(db)));
            if (items.length > 0) {
                let item = items[0];
                // 2020年10月16日已放开限制
                /* if (req.query.serv_range === 'public' && ['mt'].includes(item.from)) {
                    return responseWrapper.error('NULL_ERROR');
                } */
                item['id'] = item['_id'];
                delete item['_id'];
                if (proj.statistics === 1) {
                    let keys = {
                        'statistics': 'knowledge',
                    };
                    let stat = { 'statistics': item['statistics'] };
                    utilsBiz.loosen_tree(stat, keys);
                    item['statistics'] = stat;
                }
                // item.vague_name = filterExampaperName(item);
                item.province = item.province || item.region;
                item.name = mapExampaperName(item, req.query);
                item.from = '';
                return cont(null, item);
            }
            return responseWrapper.error('NULL_ERROR');
        });
    }).then(function (cont, paper) {
        let period = paper.period;
        let subject = paper.subject;
        // 获取所有qid
        let qids = [];
        let blocks = paper['blocks'];
        for (let blk of blocks) {
            let qs = blk['questions'];
            for (let q of qs) {
                if (_.isObject(q) && q.id) {
                    qids.push(q.id);
                } else {
                    qids.push(q);
                }
            }
        }
        // 获取所有试题
        let cond = { '_id': { '$in': qids }, };
        let ques_type = req.inner_visit ? params['ques_type'] : 'stem';
        query_questions(cond, ques_type, params['device'], req.out_of_limits, null, function (err, result) {
            if (err) {
                return cont(err);
            }
            for (let blk of blocks) {
                let qs = blk['questions'];
                let _qs = [];

                blk.score = Number((Number(blk.score) || 0).toFixed(2));
                let blkScore = qs.length > 0 ? Number((blk.score / qs.length).toFixed(1)) : 0;
                for (let q of qs) {
                    let qid = '';
                    let score = 0;
                    if (_.isObject(q) && q.id) {
                        qid = q.id;
                        score = Number(Number(q.score || 0).toFixed(2));
                    } else {
                        qid = q;
                        score = blkScore;
                    }
                    if (result[0] && result[0]['id'] === qid) {
                        let ques = result.shift();
                        if (ques.reco_questions && ques.reco_questions instanceof Array) {
                            ques.reco_questions = ques.reco_questions.map(ele => {
                                if (typeof ele === 'number') {
                                    return { id: ele };
                                }
                                return ele;

                            });
                        }
                        ques.score = score;
                        ques.period = period; 	// when qid is  polluted, pollute its period
                        ques.subject = subject; // when qid is  polluted, pollute its period
                        _qs.push(ques);
                    }
                }
                blk['questions'] = _qs;
                // 处理题块没有 type 字段的数据，将该题块下的试题类型(唯一时)的信息同步到 block.type，
                if (!blk.type) {
                    let typeList = [...new Set(blk.questions.map(e => e.type))];
                    blk.type = typeList.length === 1 ? typeList[0] : '';
                }
            }
            return cont(null, paper);

        });
    }).then(function (cont, result) {
        if (result) {
            if (req.query.freq && req.query.device !== 'mobile') {
                result.blocks = freqExampaper(result.blocks);
                res.out_of_limits = false;
                // item.freq = '今日浏览次数超限，无法查看此处内容';
            }
            return responseWrapper.succ(result);
        }
        return responseWrapper.error('NULL_ERROR');
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

function freqExampaper(blocks) {
    try {
        blocks = blocks.map(block => {
            block.questions = block.questions.map(question => {
                question.blocks = {
                    types: question.blocks.types,
                    stems: question.blocks.stems,
                    knowledges: question.blocks.knowledges,
                    core_knowledges: question.blocks.core_knowledges,
                };
                return question;
            });
            return block;
        });
        return blocks;
    } catch (err) {
        return blocks;
    }
}

/**
 * Author: YuLei
 * Desc: 根据试卷id，获取试卷内容信息，但是不计入浏览量
 * @param {object} req
 * @param {object} res
 * @returns {object}
 */
function exampaperNoView(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
        // 数据查询 ----------------------------
        Thenjs(function (cont) {
            let coll = db.collection('exampaper');
            // 查询条件
            let cond = { '_id': params['exampaper_id'] };
            // 提取出的字段
            let proj = params['fields_type'];
            proj.from = 1;
            // 查询
            coll.find(cond).project(proj).toArray(function (err, items) {
                if (err) {
                    return cont(err);
                }
                if (items.length > 0) {
                    let item = items[0];
                    item['id'] = item['_id'];
                    delete item['_id'];
                    if (proj.statistics === 1) {
                        let keys = {
                            'statistics': 'knowledge',
                        };
                        let stat = { 'statistics': item['statistics'] };
                        utilsBiz.loosen_tree(stat, keys);
                        item['statistics'] = stat;
                    }
                    return cont(null, item);
                }
                return responseWrapper.error('NULL_ERROR');

            });
        }).then(function (cont, paper) {
            let period = paper.period;
            let subject = paper.subject;
            // 获取所有qid
            let qids = [];
            let blocks = paper['blocks'];
            if (req.out_of_limits) { // 超频
                paper.name = utilsBiz.dirty_string(paper.name, 5);
                for (let blk of blocks) {
                    let qs = blk['questions'];
                    for (let q of qs) {
                        let qid = '';
                        if (_.isObject(q) && q.id) {
                            qid = q.id;
                        } else {
                            qid = q;
                        }
                        qid = utilsBiz.dirty_id(qid, 4194303); // 4194304=2^22
                        qids.push(qid);
                    }
                }
            } else {
                for (let blk of blocks) {
                    let qs = blk['questions'];
                    for (let q of qs) {
                        if (_.isObject(q) && q.id) {
                            qids.push(q.id);
                        } else {
                            qids.push(q);
                        }
                    }
                }
            }
            // 获取所有试题
            let cond = { '_id': { '$in': qids }, };
            let ques_type = req.inner_visit ? params['ques_type'] : 'stem';
            query_questions(cond, ques_type, params['device'], req.out_of_limits, null, function (err, result) {
                if (err) {
                    return cont(err);
                }
                for (let blk of blocks) {
                    let qs = blk['questions'];
                    let _qs = [];
                    blk.score = Number(blk.score) || 0;
                    let blkScore = qs.length > 0 ? Number((blk.score / qs.length).toFixed(1)) : 0;
                    for (let q of qs) {
                        let qid = '';
                        let score = 0;
                        if (_.isObject(q) && q.id) {
                            qid = q.id;
                            score = q.score;
                        } else {
                            qid = q;
                            score = blkScore;
                        }
                        if (result[0] && result[0]['id'] === qid) {
                            let ques = result.shift();
                            if (ques.reco_questions && ques.reco_questions instanceof Array) {
                                ques.reco_questions = ques.reco_questions.map(ele => {
                                    if (typeof ele === 'number') {
                                        return { id: ele };
                                    }
                                    return ele;
                                });
                            }
                            ques.score = score;
                            ques.period = period; 	// when qid is  polluted, pollute its period
                            ques.subject = subject; // when qid is  polluted, pollute its period
                            _qs.push(ques);
                        }
                    }
                    blk['questions'] = _qs;
                }
                return cont(null, paper);
            });
        }).then(function (cont, result) {
            if (result) {
                return responseWrapper.succ(result);
            }
            return responseWrapper.error('NULL_ERROR');
        }).fail(function (cont, error) {
            Logger.error(error);
            return responseWrapper.error('HANDLE_ERROR');
        }).finally(function (cont, error) {
            Logger.error(error);
            return responseWrapper.error('HANDLE_ERROR');
        });
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
}

/**
 * 获取试卷的统计信息
 * GET /kb_api/v2/exampapers/profile/
 * @param {*} req
 * @param {*} res
 * @return {*}
 */
function profile(req, res) {
    req.query.resource = 'exampaper';
    return resource.profile_single(req, res);
}

/**
 * 获取试卷筛选项
 * GET /kb_api/v2/exampapers/filters/
 * @param {*} req
 * @param {*} res
 * @return {*}
 */
function filters(req, res) {
    req.query.resource = 'exampaper';
    return resource.filters(req, res);
}

/**
 * 获取最近试卷
 * GET /kb_api/v2/exampapers/recently/
 * @param {*} req
 * @param {*} res
 * @return {*}
 */
function recently(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let period, grade, offset, limit;
    try {
        period = req.query.period;
        if (!period) {
            throw new Error('period 不能为空');
        }
        grade = req.query.grade;
        offset = parseInt(req.query.offset || 0);
        limit = parseInt(req.query.limit || 30);
    } catch (err) {
        Logger.warn({
            who: req.query.api_key,
            where: `${__filename} recently`,
            what: 'PARAMETERS_ERROR',
            why: err instanceof Error ? err.message : err,
            how: req.originalUrl
        });
        return responseWrapper.error('PARAMETERS_ERROR', err.message);
    }

    const queryDbs = (req.query.dbs || 'basic').split(',');

    Thenjs(function (cont) {
        let coll = db.collection('exampaper');
        let year = new Date(new Date() - 86400000 * 30).getFullYear();
        let filter = {
            from_year: { $gte: year },
            period: period,
            g_paper_id: { $exists: 0 },
        };
        if (grade) {
            filter.grade = grade;
        }
        filter.dbs = { $in: queryDbs };
        let proj = {
            _id: 1, name: 1, period: 1, subject: 1, grade: 1, type: 1,
            region: 1, from_year: 1, to_year: 1, is_elite: 1,
            view_times: 1, download_times: 1, utime: 1
        };
        coll.count(filter, function (err, total_num) {
            coll.find(filter).project(proj).sort({ utime: -1 })
                .skip(offset).limit(limit)
                .toArray(function (err, items) {
                    if (err) {
                        return cont(err);
                    }
                    for (let item of items) {
                        item['id'] = item['_id'];
                        delete item['_id'];
                    }
                    let result = {
                        total_num: total_num,
                        exampapers: items,
                    };
                    cont(null, result);
                });
        });
    }).then(function (cont, result) {
        if (result) {
            utils.return_data(res, result);
        } else {
            return responseWrapper.error('NULL_ERROR');
        }
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

function checkBody(req) {
    if (!req.body.difficulty || (['容易', '普通', '困难', '不限'].indexOf(req.body.difficulty) === -1)) {
        throw new Error('difficulty字段有误');
    }
    if (req.body.knowledges_ids && Array.isArray(req.body.knowledges_ids) && req.body.knowledges_ids.length > 0) {
        let knowledges_ids = req.body.knowledges_ids = _.uniq(req.body.knowledges_ids);

        for (let kid of knowledges_ids) {
            if (typeof kid !== 'number') {
                throw new Error('knowledges_ids 含有非数字');
            }
        }
        let blocks = req.body.blocks;
        for (let curItem of blocks) {
            if (typeof curItem.type !== 'string') {
                throw new Error('type 类型不正确');
            }
            if (typeof curItem.num !== 'number' || curItem.num < 0) {
                throw new Error('num 不正确');
            }
        }
    } else {
        throw new Error('knowledges_ids 字段有误');
    }
}


// 给每个知识点分配题目数量  [{ id:_id , num:int},...]
function assignQuestionNumToKnowledges(req, questionNum, knowledgesInfo) {
    //console.time('assignQuestiontoknowledges');
    let knowledges_ids = req.body.knowledges_ids;
    let base = Math.floor(questionNum / knowledges_ids.length);
    let reminder = questionNum % knowledges_ids.length;
    if (base >= 2) {
        base = Math.floor(base / 2);
        reminder = questionNum - base * knowledges_ids.length;
    }
    let knowledges2NumArray = [];
    for (let index = 0; index < knowledges_ids.length; index++) {
        let curItem = {};
        curItem['id'] = knowledges_ids[index];
        curItem['num'] = base;
        knowledges2NumArray[index] = curItem;
    }
    let limit = 100000;
    while (reminder > 0 && --limit > 0) {
        for (let index = 0; index < knowledges2NumArray.length; index++) {
            let curItem = knowledges2NumArray[index];
            let curKnowledge = _.find(knowledgesInfo, function (item) {
                return item._id === curItem.id;
            });
            let randVar = Math.floor(Math.random() * 100);
            let importance = curKnowledge ? curKnowledge.importance : 0;
            if (randVar < (importance + 0.5) * 19) {
                reminder--;
                curItem.num++;
                if (reminder === 0) {
                    break;
                }
            }
        }
    }
    //console.timeEnd('assignQuestiontoknowledges');
    return knowledges2NumArray;
}

// 返回 [ {type:选择题, num:[1,2,1], ..}
function getDifficultyTable(req) {
    let difficultyTable = [];
    let diffRange = DifficultyRange[req.body.difficulty];
    for (let index = 0; index < req.body.blocks.length; index++) {
        let curBlock = req.body.blocks[index];
        let base = Math.floor(curBlock.num / diffRange.length);
        let reminder = curBlock.num % diffRange.length;
        let insertObj = {};
        let curDiff2NumArray = [];
        for (let index = 0; index < diffRange.length; ++index) {
            curDiff2NumArray[index] = base;
        }
        while (reminder > 0) {
            let chooseIndex = Math.floor(Math.random() * 100) % diffRange.length;
            reminder--;
            curDiff2NumArray[chooseIndex]++;
        }
        insertObj['type'] = curBlock.type;
        insertObj['num'] = curDiff2NumArray;
        difficultyTable.push(insertObj);
    }
    return difficultyTable;
}

function chooseQuestionFromQuestionIds(matchIds, candidateIds, preCandidateIds, findKnowledge, chooseIds) {
    for (let curId of matchIds) {
        let findQuestion = _.find(findKnowledge.questions, function (question) {
            return question.id === curId;
        });
        if (findQuestion) {
            findQuestion.id = 0;
            if (chooseIds.indexOf(curId) === -1) {
                chooseIds.push(curId);
                return curId;
            }
        }
    }
    for (let curId of candidateIds) {
        let findQuestion = _.find(findKnowledge.questions, function (question) {
            return question.id === curId;
        });
        if (findQuestion) {
            findQuestion.id = 0;
            if (chooseIds.indexOf(curId) === -1) {
                chooseIds.push(curId);
                //console.log('select from candidateIds');
                return curId;
            }
        }
    }
    for (let curId of preCandidateIds) {
        let findQuestion = _.find(findKnowledge.questions, function (question) {
            return question.id === curId;
        });
        if (findQuestion) {
            findQuestion.id = 0;
            if (chooseIds.indexOf(curId) === -1) {
                chooseIds.push(curId);
                return curId;
            }
        }
    }
    return 0;
}

function chooseQuestionFromKnowledge(findKnowledge, queueItem, originKids, chooseIds, { difficulty, province, city }) {
    let preCandidateQuestions = _.filter(findKnowledge.questions, function (question) {
        if ((DifficultyRange[difficulty].indexOf(question.diff) !== -1)
            && (question.type === queueItem.type) && question.id !== 0) {
            let pr = province === '全部' ? '' : province;
            city = city === '全部' ? '' : city;
            let cond = {};
            pr ? cond.pr = pr : '';
            city ? cond.city = city : '';
            let s_region = _.find(question.papers, cond);
            return (pr || city) ? s_region : true;
        }
        return false;
    });
    let preCandidateIds = _.shuffle(_.pluck(preCandidateQuestions, 'id'));

    let candidateQuestions = _.filter(preCandidateQuestions, function (question) {
        if (question.diff === queueItem.diff
            && question.type === queueItem.type) {
            return true;
        }
        return false;
    });
    let candidateIds = _.shuffle(_.pluck(candidateQuestions, 'id'));

    let matchIds = _.filter(candidateQuestions, function (question) {
        let interArray = _.intersection(question.kns, originKids);
        if (interArray.length === question.kns.length) {
            return true;
        }
        return false;

    });
    matchIds = _.shuffle(_.pluck(matchIds, 'id'));

    return chooseQuestionFromQuestionIds(matchIds, candidateIds, preCandidateIds, findKnowledge, chooseIds);
}

function chooseQuestionFromOtherKnowledges(knowledgesInfo, queueItem, originKids, body, chooseIds) {
    // 先从当前试卷 筛选出来的知识点question字段查找
    for (let curKnowledge of knowledgesInfo) {
        let chooseId = chooseQuestionFromKnowledge(curKnowledge, queueItem, originKids, chooseIds, body);
        if (chooseId !== 0) {
            queueItem['id'] = chooseId;
            queueItem['period'] = curKnowledge.period;
            queueItem['subject'] = curKnowledge.subject;
            return queueItem;
        }
    }
    queueItem['id'] = 0;
    return queueItem;
}

// 根据条件筛选出 question
function filterKnowledgesQuestion(queue, knowledgesInfo, originKids, body, callback) {
    // 记录所有 question id
    let chooseIds = [];
    let otherKnowledgesInfo = [];
    Thenjs.each(queue, function (cont, queueItem) {
        let findKnowledge = _.find(knowledgesInfo, function (info) {
            return info._id === queueItem.kid;
        });
        let chooseId = chooseQuestionFromKnowledge(findKnowledge, queueItem, originKids, chooseIds, body);
        // 当前知识点可以找到合适的题目 否则 去试卷选取的其他知识点下去寻找
        if (chooseId !== 0) {
            queueItem['id'] = chooseId;
            queueItem['period'] = findKnowledge.period;
            queueItem['subject'] = findKnowledge.subject;
            cont(null, queueItem);
        } else {
            queueItem = chooseQuestionFromOtherKnowledges(knowledgesInfo, queueItem, originKids, body, chooseIds);
            cont(null, queueItem);
        }
    }).then(function (cont, queue) {
        let notFoundTag = false;
        // 查找是否有没有查到到的情况 ，需要到未选取的知识点下寻找
        _.each(queue, function (queueItem) {
            if (queueItem.id === 0) {
                notFoundTag = true;
            }
        });
        if (notFoundTag) {
            Thenjs(function (cont) {
                let pickIds = _.pluck(knowledgesInfo, '_id');
                let unPickIds = _.difference(originKids, pickIds);
                // otherKnowledge 已经存在
                if ((unPickIds.length === 0) || (otherKnowledgesInfo.length > 0)) {
                    return cont(null, otherKnowledgesInfo);
                }
                // 需要从库中读取 otherKnowledge
                if (unPickIds.length > 20) {
                    unPickIds = unPickIds.slice(0, 20);
                }
                db.collection('knowledge').find({ _id: { $in: unPickIds } }).project({
                    '_id': 1,
                    'questions': 1,
                    'importance': 1,
                    'period': 1,
                    'subject': 1,
                    'questions.kns': 1,
                    'questions.id': 1,
                    'questions.diff': 1,
                    'questions.type': 1
                }).toArray(function (err, unpickKnowledgesInfo) {
                    if (err) {
                        return cont(null);
                    }
                    otherKnowledgesInfo = unpickKnowledgesInfo;
                    return cont(null, otherKnowledgesInfo);
                });
            }).then(function (cont, otherKnowledgesInfo) {
                for (let queueItem of queue) {
                    if (queueItem.id === 0) {
                        for (let curKnowledge of otherKnowledgesInfo) {
                            let chooseId = chooseQuestionFromKnowledge(curKnowledge, queueItem, originKids, chooseIds, body);
                            if (chooseId !== 0) {
                                queueItem['id'] = chooseId;
                                queueItem['period'] = curKnowledge.period;
                                queueItem['subject'] = curKnowledge.subject;
                                break;
                            }
                        }
                    }
                }
                queue = _.filter(queue, function (queueItem) {
                    return queueItem.id !== 0;
                });
                return callback(null, queue);
            }).fail(function (cont, error) {
                Logger.error(error);
                return callback(error);
            }).finally(function (cont, error) {
                Logger.error(error);
                return callback(error);
            });
        } else {
            return callback(null, queue);
        }
    }).fail(function (cont, error) {
        Logger.error(error);
        return callback(error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return callback(error);
    });
}

// 筛选试题填充知识点组成的试卷
function fillExampaper(body, type2NumArray, originKids, knowledgesInfo, callback) {
    let diffRange = DifficultyRange[body.difficulty];
    let queue = [];
    for (let index = 0; index < type2NumArray.length; index++) {
        let curTypeItem = type2NumArray[index];
        for (let kIndex = 0; kIndex < curTypeItem.knowledgeArray.length; kIndex++) {
            let curIds = curTypeItem.knowledgeArray[kIndex];
            for (let idIndex = 0; idIndex < curIds.length; idIndex++) {
                let pushObj = {};
                pushObj['type'] = curTypeItem.type;
                pushObj['diff'] = diffRange[kIndex];
                pushObj['kid'] = curIds[idIndex];
                queue.push(pushObj);
            }
        }
    }
    filterKnowledgesQuestion(queue, knowledgesInfo, originKids, body, function (err, result) {
        if (err) {
            return callback(err);
        }
        return callback(null, result);
    });
}

// 对应难度分配知识点
function assignKnowledgeToExampaper(req, type2NumArray, knowledge2NumArray) {
    function isFinish(type2NumArray) {
        let unset = 0;
        for (let index = 0; index < type2NumArray.length; index++) {
            let curArray = type2NumArray[index].num;
            _.each(curArray, function (num) {
                unset += num;
            });
            if (unset > 0) {
                break;
            }
        }
        return unset === 0;
    }

    let knowledgesIndex = 0;
    while (!isFinish(type2NumArray)) {
        for (let typeIndex = 0; typeIndex < type2NumArray.length; typeIndex++) {
            let curTypeItem = type2NumArray[typeIndex];
            if (!curTypeItem.hasOwnProperty('knowledgeArray')) {
                curTypeItem['knowledgeArray'] = [];
            }
            for (let numIndex = 0; numIndex < curTypeItem.num.length; numIndex++) {
                if (!curTypeItem.knowledgeArray[numIndex]) {
                    curTypeItem.knowledgeArray[numIndex] = [];
                }
                if (curTypeItem.num[numIndex] > 0) {
                    curTypeItem.num[numIndex] -= 1;
                    while (knowledge2NumArray[knowledgesIndex].num === 0) {
                        knowledgesIndex = ++knowledgesIndex % knowledge2NumArray.length;
                    }
                    knowledge2NumArray[knowledgesIndex].num -= 1;
                    curTypeItem.knowledgeArray[numIndex].push(knowledge2NumArray[knowledgesIndex].id);
                    knowledgesIndex = ++knowledgesIndex % knowledge2NumArray.length;
                }
            }
        }
    }
    return type2NumArray;
}

/**
 * 根据知识点创建试卷
 * @param {*} req
 * @param {*} res
 * @return {*}
 */
function createExampaperByKnowledges(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        checkBody(req);
    } catch (err) {
        Logger.warn({
            who: req.query.api_key,
            where: `${__filename} createExampaperByKnowledges`,
            what: 'PARAMETERS_ERROR',
            why: err instanceof Error ? err.message : err,
            how: req.originalUrl
        });
        return responseWrapper.error('PARAMETERS_ERROR', err.message);
    }
    let questionNum, type2Num, type2NumArray, knowledge2NumArray, originKids;
    try {
        questionNum = 0;
        type2Num = req.body.blocks;
        for (let index in type2Num) {
            questionNum += type2Num[index].num;
        }
        // 题型 对应 每个难度的数组
        type2NumArray = getDifficultyTable(req);
        knowledge2NumArray = [];
        originKids = req.body.knowledges_ids.slice(0);
    } catch (error) {
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }
    Thenjs(function (cont) {
        db.collection('knowledge').find({ _id: { $in: req.body.knowledges_ids } }).project({
            '_id': 1,
            'questions': 1,
            'importance': 1,
            'period': 1,
            'subject': 1,
            'questions.kns': 1,
            'questions.id': 1,
            'questions.diff': 1,
            'questions.type': 1,
            'questions.papers': 1
        }).toArray(function (err, knowledgesInfo) {
            if (err) {
                return cont(err);
            }
            let ids = req.body.knowledges_ids;
            knowledgesInfo.sort(function (x, y) {
                return ids.indexOf(x._id) - ids.indexOf(y._id);
            });
            return cont(null, knowledgesInfo);
        });
    }).then(function (cont, knowledgesInfo) {
        knowledge2NumArray = assignQuestionNumToKnowledges(req, questionNum, knowledgesInfo);
        return cont(null, knowledgesInfo);
    }).then(function (cont, knowledgesInfo) {
        type2NumArray = assignKnowledgeToExampaper(req, type2NumArray, knowledge2NumArray);
        return cont(null, knowledgesInfo, type2NumArray);
    }).then(function (cont, knowledgesInfo, type2NumArray) {
        fillExampaper(req.body, type2NumArray, originKids, knowledgesInfo, function (err, result) {
            if (err) {
                return responseWrapper.error('HANDLE_ERROR');
            }
            return responseWrapper.succ(result);
        });
    }).fail(function (cont, err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

/**
 * 根据全局试卷 id g_paper_id 查询试卷
 * date: 2018-07-19 for kbp 2.5 电子试卷
 * @param {*} req
 * @param {*} res
 */
const getExampaperByGPaperId = async function (req, res) {
    const response_wrapper = new ResponseWrapper(res);
    try {
        let g_paper_id = req.params.g_paper_id;
        if (!g_paper_id) {
            Logger.warn({
                who: req.query.api_key,
                where: `${__filename} getExampaperByGPaperId`,
                what: 'PARAMETERS_ERROR',
                why: '没有g_paper_id字段',
                how: req.originalUrl
            });
            return response_wrapper.error('PARAMETERS_ERROR', 'g_paper_id必须');
        }
        g_paper_id = g_paper_id.toString();
        const dbs = (req.query.dbs || 'basic').split(',');

        const exampaper = await db.collection('exampaper').findOne({ g_paper_id: g_paper_id, dbs: { $in: dbs } }, {
            statistics: 0,
            has_modified: 0,
            is_elite: 0,
            source_url: 0,
            from: 0,
            category_id: 0,
        });
        if (!exampaper) {
            return response_wrapper.error('NULL_ERROR');
        }
        // 收集所有的问题 id
        let qids = [];
        let blocks = exampaper['blocks'];
        for (let blk of blocks) {
            let qs = blk['questions'];
            for (let q of qs) {
                if (_.isObject(q) && q.id) {
                    qids.push(q.id);
                } else {
                    qids.push(q);
                }
            }
        }
        // 获取所有试题
        let cond = {
            '_id': { '$in': qids },
        };
        let ques_type = 'stem';
        let device = 'pc';

        const result = await query_questionsPromise(cond, ques_type, device, null, null);
        for (let blk of blocks) {
            let qs = blk['questions'];
            let _qs = [];
            for (let q of qs) {
                let qid = '';
                if (_.isObject(q) && q.id) {
                    qid = q.id;
                } else {
                    qid = q;
                }
                if (result[0] && result[0]['id'] === qid) {
                    let ques = result.shift();
                    delete ques['refer_exampapers'];
                    delete ques['refer_times'];
                    delete ques['knowledges'];
                    _qs.push(ques);
                }
            }
            blk['questions'] = _qs;
        }
        response_wrapper.succ(exampaper);
    } catch (e) {
        Logger.error(e);
        return response_wrapper.error('HANDLE_ERROR');
    }
};

const getExampapersByIds = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let exampaperIds = [];
    let mode = req.query.mode;
    if (req.params.exampaper_ids)
        exampaperIds = decodeURIComponent(req.params.exampaper_ids).split(',');

    const queryDbs = new Set((req.query.dbs || 'basic').split(','));
    try {
        let exampaperArr = [];
        _.each(exampaperIds, function (exampaper) {
            exampaperArr.push(Number(exampaper));
        });
        let data = await db.collection('exampaper').find({ _id: { $in: exampaperArr } }).sort({ ctime: -1 }).toArray();
        let resObj = [];
        for (let exampaper of data) {
            // 试卷 权限过滤
            if (!(exampaper.dbs || ['basic']).some(db => queryDbs.has(db))) continue;

            let tmpObj = {};
            tmpObj.id = exampaper._id;
            tmpObj.ctime = exampaper.ctime ? exampaper.ctime : '';
            tmpObj.grade = exampaper.grade ? exampaper.grade : '';
            // exampaper.vague_name = filterExampaperName(exampaper);
            tmpObj.name = mapExampaperName(exampaper, req.query);
            tmpObj.period = exampaper.period ? exampaper.period : '';
            tmpObj.subject = exampaper.subject ? exampaper.subject : '';
            tmpObj.province = exampaper.region ? exampaper.region : '';
            tmpObj.city = exampaper.city ? exampaper.city : '';
            tmpObj.type = exampaper.type ? exampaper.type : '';
            tmpObj.view_times = exampaper.view_times ? exampaper.view_times : 0;
            tmpObj.from_year = exampaper.from_year ? exampaper.from_year : '';
            tmpObj.to_year = exampaper.to_year ? exampaper.to_year : '';
            // tmpObj.from = exampaper.from ? exampaper.from : '';
            tmpObj.from = '';

            if (req.query.request_all) {
                tmpObj.blocks = exampaper.blocks ? exampaper.blocks : [];
                tmpObj.statistics = exampaper.statistics ? exampaper.statistics : [];
                tmpObj.is_show = exampaper.is_show;
                tmpObj.grade_semester = exampaper.grade_semester;
                tmpObj.difficulty_proportion = exampaper.difficulty_proportion;
                tmpObj.press_version = exampaper.press_version ? exampaper.press_version : '';
                tmpObj.score = exampaper.score ? exampaper.score : null;
                tmpObj.chapter_ids = exampaper.chapter_ids;
            }

            if (exampaper.to_year)
                tmpObj.year = exampaper.to_year;
            if (mode) {
                tmpObj.download_times = exampaper.download_times;
                tmpObj.blocks = exampaper.blocks;
            }
            tmpObj.ques_num = _getExampaperQuestionNum(exampaper);
            resObj.push(tmpObj);
        }

        return responseWrapper.succ(resObj);
    } catch (error) {
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }
};

const getExampaperFromCached = async (cond, sort, options, params) => {
    try {
        const md5 = crypto.createHash('md5');
        let result = md5.update(`${JSON.stringify(cond)}:${JSON.stringify(sort)}:${options.limit}:${options.offset}`).digest('hex');
        let cacheKey = `kb_api:v2:exampapers:list:${result}`;
        let retval = await rediser.get(cacheKey);
        if (!!retval === true) {
            return retval;
        }
        retval = {};
        retval.total_num = await db.collection('exampaper').count(cond, { limit: 1000 });
        retval.exampapers = await db.collection('exampaper').find(cond).sort(sort).limit(options.limit).skip(options.offset).project(options.project).toArray();
        let exampapers = retval.exampapers;
        for (let ix = 0; ix < exampapers.length; ix++) {
            let exampaper = exampapers[ix];
            exampaper.id = exampaper._id;
            delete exampaper._id;
            exampaper.province = exampaper.province || exampaper.region;
            delete exampaper.region;
            utilsBiz.getProvinceCity(exampaper, params);
        }
        rediser.set(cacheKey, retval, 60 * 60 * 2);
        return retval;
    } catch (err) {
        return {
            total_num: 0,
            exampapers: []
        };
    }
};

// 根据条件获取条件列表
const getExampapersByCondition = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let cond = {};
        if (!req.query.subject) {
            Logger.warn({
                who: req.query.api_key,
                where: `${__filename} getExampapersByCondition`,
                what: 'PARAMETERS_ERROR',
                why: 'subject is required',
                how: req.originalUrl
            });
            return responseWrapper.error('PARAMETERS_ERROR', 'subject is required');
        }
        cond.subject = req.query.subject;
        if (!req.query.period) {
            Logger.warn({
                who: req.query.api_key,
                where: `${__filename} getExampapersByCondition`,
                what: 'PARAMETERS_ERROR',
                why: 'period is required',
                how: req.originalUrl
            });
            return responseWrapper.error('PARAMETERS_ERROR', 'period is required');
        }
        cond.period = req.query.period;
        if (!req.query.type) {
            Logger.warn({
                who: req.query.api_key,
                where: `${__filename} getExampapersByCondition`,
                what: 'PARAMETERS_ERROR',
                why: 'type is required',
                how: req.originalUrl
            });
            return responseWrapper.error('PARAMETERS_ERROR', 'type is required');
        }
        if (req.query.type !== '全部') {
            cond.type = req.query.type;
        }
        if (req.query.press_version && req.query.press_version !== '全部') {
            cond.press_version = req.query.press_version;
        }
        //判断type如果为真卷、模拟、复习，则press_version=all_version
        if (cond.type && (cond.type.indexOf('真卷') > 0 || cond.type.indexOf('模拟') > 0 || cond.type.indexOf('复习') > 0)) {
            cond.press_version = 'all_version';
        }
        if (req.query.grade && req.query.grade !== '全部') {
            cond.grade = req.query.grade;
        }
        utilsBiz.setCondProvinces(cond, req.query);

        if (req.query.year) {
            req.query.year = req.query.year * 1;
            cond.to_year = req.query.year;
        }
        if (req.query.serv_range === 'public') {
            cond.from = { $in: ['ai_exam', 'kbp_new', 'other'] };
        }
        let offset = req.query.offset ? req.query.offset * 1 : 0;
        let limit = req.query.limit ? req.query.limit * 1 : 10;
        offset = offset > 980 ? 980 : offset;
        limit = limit > 20 ? 20 : limit;
        let sortBy = req.query.sort_by;
        let retval = {
            total_num: 0,
            exampapers: [],
        };
        let sort = {};
        if (sortBy !== undefined) {
            sort[sortBy] = -1;
        }
        retval = await getExampaperFromCached(cond, sort, {
            limit: limit,
            offset: offset,
            project: {
                ctime: 1,
                download_times: 1,
                from_year: 1,
                is_elite: 1,
                from: 1,
                name: 1,
                to_year: 1,
                province: 1,
                provinces: 1,
                region: 1,
                grade: 1,
                type: 1,
                view_times: 1,
            }
        }, req.query);
        return responseWrapper.succ(retval);
    } catch (err) {
        Logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * Author: YuLei
 * Description: 通过‘知识点id’和‘试题难度’，获取‘选择题’、‘多选题’、‘填空题’和‘解答题’数量。
 * @param {*} req
 * @param {*} res
 */
const getQuestionNumByKnowledge = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        if (!req.body.diff || !req.body.kids || !req.body.type) {
            return responseWrapper.error('PARAMETERS_ERROR');
        }
        const queryDbs = new Set(req.body.dbs || ['basic']);
        let diff = DifficultyRange[req.body.diff];
        let type = req.body.type;
        let cond = {};
        if (req.body.kids)
            cond._id = { $in: req.body.kids };


        let knowledges = await db.collection('knowledge').find(cond).project({
            'questions.type': 1,
            'questions.diff': 1,
            'questions.id': 1,
            'questions.papers': 1,
            'dbs': 1
        }).toArray();
        // 知识点 权限过滤
        knowledges = (knowledges || []).filter(know => (know.dbs || ['basic']).some(db => queryDbs.has(db)));
        if (!knowledges || knowledges.length === 0) {
            return responseWrapper.error('HANDLE_ERROR', '未找到知识点');
        }
        let mapQuestion = new Map();
        for (let i = 0; i < knowledges.length; i++) {
            if (knowledges[i].questions) {
                for (let j = 0; j < knowledges[i].questions.length; j++) {
                    let question = knowledges[i].questions[j];
                    if (diff.indexOf(question.diff) === -1 || type.indexOf(question.type) === -1)
                        continue;

                    // if (!retObj[question.type])
                    //     retObj[question.type] = 0;
                    // retObj[question.type] ++;
                    let pr = req.body.province;
                    let city = req.body.city;
                    pr = pr === '全部' ? '' : pr;
                    city = city === '全部' ? '' : city;
                    if (pr || city) {
                        let _cond = {};
                        pr ? _cond.pr = pr : '';
                        city ? _cond.city = city : '';
                        if (!_.find(question.papers, _cond)) {
                            continue;
                        }
                    }

                    if (mapQuestion.has(question.type)) {
                        let quesVal = mapQuestion.get(question.type);
                        quesVal.push(question.id);
                        mapQuestion.set(question.type, quesVal);
                    } else {
                        let quesVal = [];
                        quesVal.push(question.id);
                        mapQuestion.set(question.type, quesVal);
                    }
                }
            }
        }

        let retObj = {};
        for (let [key, val] of mapQuestion) {
            val = Array.from(new Set(val));
            retObj[key] = val.length;
        }

        responseWrapper.succ(retObj);
    } catch (err) {
        Logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

/*
 * 通过试卷 id 数组查询对应的创建时间
 * @param {*} req
 * @param {*} res
 */
const getExampaperCtimeByIds = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let exampaperIds = [];
    if (req.params.exampaper_ids) {
        exampaperIds = decodeURIComponent(req.params.exampaper_ids).split(',');
    }
    try {
        let exampaperArr = [];
        _.each(exampaperIds, function (exampaper) {
            exampaperArr.push(Number(exampaper));
        });
        // 查询条件
        let cond = { '_id': { '$in': exampaperArr } };
        let proj = {
            '_id': 1,
            'ctime': 1
        };
        let items = await db.collection('exampaper').find(cond).project(proj).toArray();
        if (!items) {
            responseWrapper.error('NULL_ERROR', '没有对应的数据');
            return;
        }
        // 根据 exampaperArr 中 id 的顺序返回结果
        items.sort(function (x, y) {
            return exampaperArr.indexOf(x._id) - exampaperArr.indexOf(y._id);
        });
        _.forEach(items, function (item) {
            item.id = item._id;
            delete item._id;
        });
        responseWrapper.succ(items);
        return;
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e.toString());
    }
};

/*
 * 通过试卷 id 数组查询对应的创建时间
 * @param {*} req
 * @param {*} res
 */
const getExampapersAbstract = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let exampaperIds = [];
    if (req.params.exampaper_ids) {
        exampaperIds = decodeURIComponent(req.params.exampaper_ids).split(',');
    }
    try {
        let exampaperArr = [];
        _.each(exampaperIds, function (exampaper) {
            exampaperArr.push(Number(exampaper));
        });
        // 查询条件
        let cond = { '_id': { '$in': exampaperArr } };
        let proj = {
            '_id': 1,
            'period': 1,
            'subject': 1,
            'province': 1,
            'provinces': 1,
            'press_version': 1,
            'name': 1,
            'from_year': 1,
            'to_year': 1,
            'city': 1,
            'type': 1,
            'grade': 1,
            'blocks': 1,
        };
        let items = await db.collection('exampaper').find(cond).project(proj).toArray();
        if (!items) {
            responseWrapper.error('NULL_ERROR', '没有对应的数据');
            return;
        }
        _.forEach(items, function (item) {
            item.ques_num = getQuesNum(item.blocks);
            item.id = item._id;
            let blocks = [];
            item.blocks.forEach(block => {
                blocks.push(`${block.type || block.title}（${block.questions.length}题${block.score}分）`);
            });
            item.blocks = blocks;
            item.year = item.from_year;
            delete item._id;
        });
        responseWrapper.succ(items);
        return;
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e.toString());
    }
};

function _getExampaperQuestionNum(exampaper) {
    let num = 0;
    if (!exampaper) return num;
    const blocks = exampaper.blocks || [];
    for (const block of blocks) {
        num += _.size(block.questions || []);
    }
    return num;
}

module.exports = {
    exampaper_cats: exampaper_cats,
    exampaper_category: exampaper_category,
    exampaperBySearch: exampaperBySearch,
    //createExampaperCategory: createExampaperCategory,
    //createCategory: createCategory,
    exampaper: exampaper,
    exampaperNoView: exampaperNoView,
    profile: profile,
    filters: filters,
    recently: recently,
    createExampaperByKnowledges,
    getExampaperByGPaperId,
    getExampapersByCondition,
    getExampapersByIds,
    getQuestionNumByKnowledge,
    getExampaperCtimeByIds,
    getExampapersAbstract,
};
