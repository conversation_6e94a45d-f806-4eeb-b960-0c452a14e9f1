// 视频标签，视频专辑标签
const tag = require('../../../modules/kb_api/v2/video/tag');

module.exports = function (router) {

    /*  视频标签 */
    // 检查标签是否已使用
    router.get('/video_tags/is_used', tag.checkVideoTagIsUsed);
    // 检查标签组名称是否重复
    router.get('/video_tags/group_name/is_exists', tag.checkVideoGroupNameExists);
    // 试题标签列表
    router.get('/video_tags/list', tag.getVideoTagsList);
    // 获取试题标签详情
    router.get('/video_tags/:id', tag.getVideoTagDetail);
    // kbp审核通过，推送过来，更新
    router.put('/video_tags/:id', tag.updateVideoTag);
    // kbp审核通过，推送过来,添加
    router.post('/video_tags', tag.postVideoTag);


    /* 视频专辑标签 */
    // 检查标签是否已使用
    router.get('/video_category_tags/is_used', tag.checkVideoCateTagIsUsed);
    // 检查标签组名称是否重复
    router.get('/video_category_tags/group_name/is_exists', tag.checkVideoCateGroupNameExists);
    // 试题标签列表
    router.get('/video_category_tags/list', tag.getVideoCateTagsList);
    // 获取试题标签详情
    router.get('/video_category_tags/:id', tag.getVideoCateTagDetail);
    // kbp审核通过，推送过来，更新
    router.put('/video_category_tags/:id', tag.updateVideoCateTag);
    // kbp审核通过，推送过来,添加
    router.post('/video_category_tags', tag.postVideoCateTag);

};