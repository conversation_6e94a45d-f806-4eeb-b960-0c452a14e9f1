const {
    createExampaper,
    createExampaper2,
    updateExampaper,
    createExampaperInstance,
    createExampaperAlbum,
    createHfsExampaperInstance,
    updateExampaperInstance,
    updateExampaperAlbum,
    updateExampaperStatus,
    getExampaperAlbumById,
    getExampaperAlbumIsExists,
    updateHfsExampaperInstance,
    updateExampaperUseType,
    updateMtExampaper,
    getExampaperGradeList,
} = require('./kbp_exampaper_management.js');

const {
    assembleExampaper,
    downloadExampaper,
    downloadQuestions,
    updateQuestionsUseTimes,
    getAlbumList,
    getAlbumInfoById
} = require('./tiku_exampaper_assembly');

const {
    exampaper_cats,
    exampaper_category,
    exampaperBySearch, // kbp3.3新增，对/se_kb/v2/filter/exampapers进行封装
    //createExampaperCategory,
    //createCategory,
    exampaper,
    exampaperNoView,
    profile,
    filters,
    recently,
    createExampaperByKnowledges,
    getQuestionNumByKnowledge,
    getExampaperByGPaperId,
    getExampapersByCondition,
    getExampapersByIds,
    getExampaperCtimeByIds,
    getExampapersAbstract,
} = require('./kb_exampaper.js');

module.exports = {
    // kb
    exampaper_cats,
    exampaper_category,
    exampaperBySearch, // kbp3.3新增，对/se_kb/v2/filter/exampapers进行封装
    //createExampaperCategory,
    //createCategory,
    exampaper,
    exampaperNoView,
    profile,
    filters,
    recently,
    createExampaperByKnowledges,
    getQuestionNumByKnowledge,
    getExampaperByGPaperId,
    getExampapersByCondition,
    getExampapersByIds,
    getExampaperCtimeByIds,
    // 题库组卷
    assembleExampaper,
    downloadExampaper,
    downloadQuestions,
    updateQuestionsUseTimes,
    // kbp 试卷库
    createExampaper,
    createExampaper2,
    updateExampaper,
    createExampaperInstance,
    createExampaperAlbum,
    createHfsExampaperInstance,
    updateExampaperInstance,
    updateExampaperAlbum,
    updateExampaperStatus,
    getExampaperAlbumById,
    getExampaperAlbumIsExists,
    updateHfsExampaperInstance,
    updateExampaperUseType,
    getExampapersAbstract,
    updateMtExampaper,
    getAlbumList,
    getAlbumInfoById,
    getExampaperGradeList,
};