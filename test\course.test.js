/* eslint-disable no-undef */
const config = require('config');
//const cookies = require('./config.js').config;
//const DMPSERVER = config.get('KB_API_SERVER');
const expect = require('chai').expect;
const superagent = require('superagent');
const url = require('url');

const _ = require('underscore');
const port = process.env.NODE_PORT || 80;

const host = url.format({
    protocol: 'http',
    hostname: config.get('host'),
    port: port
});
const apikey = config.get('app_keys_private')[0];
const apiKeyStr = `app_key=${apikey}`;
var learnCourseId;
var reviewCourseId;
var lessonId;
var lessonContent;
const addKnowledge = 1512964095;
const addQuestionId = 1431744767;

describe('教研平台API:获取课程打包信息', function () {
    let url = [host, '/kb_api/v2/courses', '?', apiKeyStr].join('');

    it('获取课程打包信息', function (done) {
        superagent
            .get(url)
            .end(function (err, res) {

                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.course.children).to.be.an('array');
                if (ret.data.course.children.length > 0) {
                    for (let index = 0; index < ret.data.length; ++index) {
                        expect(ret.data.course.children[index]).to.have.keys('key', 'name', 'children');
                    }
                }
                //console.log('获取课程打包信息完成：' + new Date().getTime());
                done();
            });
    });
    let learningUrl = [host, `/kb_api/v2/learning/courses`, '?', apiKeyStr].join('');

    it('根据同步课程打包信息', function (done) {
        superagent
            .get(learningUrl)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);

                expect(ret.data.course.children).to.be.an('array');
                if (ret.data.course.children.length > 0) {
                    for (let index = 0; index < ret.data.course.children.length; ++index) {
                        //expect(ret.data.course.children[index]).to.have.keys('key','name', 'children');
                    }
                }
                learnCourseId = ret.data.course.children[0].children[0].children[0].children[0].children[0].id;
                console.log('获取到的同步课程ID: ' + learnCourseId);
                done();
            });
    });
    let reviewUrl = [host, `/kb_api/v2/review/courses`, '?', apiKeyStr].join('');
    it('获取复习课程打包信息', function (done) {
        superagent
            .get(reviewUrl)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);

                expect(ret.data.course.children).to.be.an('array');
                if (ret.data.course.children.length > 0) {
                    for (let index = 0; index < ret.data.course.children.length; ++index) {
                        //expect(ret.data.course.children[index]).to.have.keys('key','name', 'children');
                    }
                }
                reviewCourseId = ret.data.course.children[0].children[0].children[0].children[0].children[0].id;
                console.log('获取到的复习课程ID: ' + reviewCourseId);
                done();
            });
    });
});



describe('教研平台API:根据同步课程ID获取课程目录 & 根据复习课程ID获取课程目录', function () {

    //url = 'http://tikuserv-test.yunxiao.com:8904/kb_api/v2/learning/courses/2147090431?app_key=iyunxiao_teach20171116';
    it('正例测试：同步课程', function (done) {
        let url = [host, `/kb_api/v2/learning/courses/${learnCourseId}`, '?', apiKeyStr].join('');

        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);

                expect(ret.data.course.children).to.be.an('array');
                if (ret.data.course.children.length > 0) {
                    for (let index = 0; index < ret.data.course.children.length; ++index) {
                        //expect(ret.data.course.children[index]).to.have.keys('key','name', 'children');
                    }
                }
                //console.log('第二个des获取同步课程目录结束'+ new Date().getTime());
                lessonId = ret.data.course.children[0].children[0].children[0].id;

                console.log('获取到的课件ID: ' + lessonId);
                done();
            });
    });

    it('正例测试：复习课程', function (done) {
        let url = [host, `/kb_api/v2/review/courses/${reviewCourseId}`, '?', apiKeyStr].join('');

        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);

                expect(ret.data.course.children).to.be.an('array');
                if (ret.data.course.children.length > 0) {
                    for (let index = 0; index < ret.data.course.children.length; ++index) {
                        //expect(ret.data.course.children[index]).to.have.keys('key','name', 'children');
                    }
                }

                done();
            });
    });
});



describe('教研平台API:根据课件ID获取内容', function () {

    it('正例测试 all:easy', function (done) {
        let url = [host, `/kb_api/v2/lessons/${lessonId}?${apiKeyStr}&difficulty=easy&`].join('');

        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.knowledges).to.be.an('array');
                if (ret.data.knowledges.length > 0) {
                    for (let index = 0; index < ret.data.knowledges.length; ++index) {
                        expect(ret.data.knowledges[index]).to.have.keys('id', 'name', 'content');
                    }
                }
                expect(ret.data.template).to.have.keys('easy');
                lessonContent = ret.data;
                console.log('获取课件内容');
                //expect(ret.data.template.hard).to.have.keys('prepare','concise','homework');
                done();
            });
    });
});

describe('教研平台API:根据课件ID获取内容', function () {


    it('正例测试 all:hard', function (done) {
        let url = [host, `/kb_api/v2/lessons/${lessonId}?difficulty=hard&${apiKeyStr}`].join('');
        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.knowledges).to.be.an('array');
                if (ret.data.knowledges.length > 0) {
                    for (let index = 0; index < ret.data.knowledges.length; ++index) {
                        expect(ret.data.knowledges[index]).to.have.keys('id', 'name', 'content');
                    }
                }
                expect(ret.data.template).to.have.keys('hard');

                //expect(ret.data.template.hard).to.have.keys('prepare','concise','homework');
                done();
            });
    });
});

describe('教研平台API:根据课件ID获取内容', function () {


    it('正例测试 all:normal', function (done) {
        let url = [host, `/kb_api/v2/lessons/${lessonId}?difficulty=normal&${apiKeyStr}`].join('');
        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.knowledges).to.be.an('array');
                if (ret.data.knowledges.length > 0) {
                    for (let index = 0; index < ret.data.knowledges.length; ++index) {
                        expect(ret.data.knowledges[index]).to.have.keys('id', 'name', 'content');
                    }
                }
                expect(ret.data.template).to.have.keys('normal');

                //expect(ret.data.template.normal).to.have.keys('prepare','concise','homework');
                done();
            });
    });
});

describe('教研平台API:根据课件ID获取内容', function () {



    it('正例测试 template:normal ', function (done) {
        let url = [host, `/kb_api/v2/lessons/${lessonId}?difficulty=normal&field=template&${apiKeyStr}`].join('');
        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.knowledges).to.be.an('array');
                if (ret.data.knowledges.length > 0) {
                    for (let index = 0; index < ret.data.knowledges.length; ++index) {
                        expect(ret.data.knowledges[index]).to.have.keys('id', 'name', 'content');
                    }
                }
                expect(ret.data.template).to.have.keys('normal');

                //expect(ret.data.template.normal).to.have.keys('prepare','concise','homework');
                done();

            });
    });
});



describe('教研平台API:根据课件ID增加【知识点】】', function () {



    let body = {
        "id": addKnowledge,
        "difficulty": "easy"
    };
    let bodyStr = JSON.stringify(body);

    it('正例测试 增加知识点 ', function (done) {
        let knowledgeurl = [host, `/kb_api/v2/lessons/${lessonId}/knowledges?${apiKeyStr}`].join('');

        superagent
            .post(knowledgeurl)
            .set('Content-Type', 'application/json')
            .send(bodyStr)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.knowledges).to.be.an('array');
                var ids = _.pluck(ret.data.knowledges, 'id')
                expect(ids.indexOf(addKnowledge)).to.not.be.equal(-1);
                if (ret.data.knowledges.length > 0) {
                    for (let index = 0; index < ret.data.knowledges.length; ++index) {
                        expect(ret.data.knowledges[index]).to.have.keys('id', 'name', 'content');
                    }
                }
                console.log('增加知识点' + addKnowledge);
                done();
            });


    });

    it('正例测试 删除知识点 ', function (done) {
        let knowledgeurl = [host, `/kb_api/v2/lessons/${lessonId}/knowledges?${apiKeyStr}`, `&id=${addKnowledge}`].join('');
        superagent
            .delete(knowledgeurl)
            .set('Content-Type', 'application/json')
            .end(function (err, res) {

                console.log("删除知识点返回")
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.knowledges).to.be.an('array');
                var ids = _.pluck(ret.data.knowledges, 'id')
                expect(ids.indexOf(addKnowledge)).to.be.equal(-1);
                if (ret.data.knowledges.length > 0) {
                    for (let index = 0; index < ret.data.knowledges.length; ++index) {
                        expect(ret.data.knowledges[index]).to.have.keys('id', 'name', 'content');
                    }
                }
                console.log('删除知识点' + addKnowledge);
                done();
            });
    });
});





describe('教研平台API:根据课件ID增加【题目】删除题目', function () {



    let quesBody = {
        "id": addQuestionId,
        "difficulty": "easy",
        "type": "homework"
    };

    let quesBodyStr = JSON.stringify(quesBody);

    it('正例测试 增加题目 ', function (done) {
        let questionUrl = [host, `/kb_api/v2/lessons/${lessonId}/questions?${apiKeyStr}`].join('');
        superagent
            .post(questionUrl)
            .set('Content-Type', 'application/json')
            .send(quesBodyStr)
            .end(function (err, res) {
                console.log("增加题目返回")
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                if (ret.code == 3) done();
                expect(ret.code).to.be.equal(0);
                expect(ret.data.knowledges).to.be.an('array');
                let ids = _.pluck(ret.data.template.easy.homework, 'id')
                expect(ids.indexOf(addQuestionId)).to.not.be.equal(-1);

                done();
            });
    });

    it('正例测试 删除题目 ', function (done) {
        let questionUrl = [host, `/kb_api/v2/lessons/${lessonId}/questions?${apiKeyStr}`, '&difficulty=easy', '&type=homework', `&id=${addQuestionId}`].join('');
        superagent
            .delete(questionUrl)
            .set('Content-Type', 'application/json')
            .end(function (err, res) {
                console.log("删除题目返回")
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.knowledges).to.be.an('array');
                let ids = _.pluck(ret.data.template.easy.homework, 'id')
                expect(ids.indexOf(addQuestionId)).to.be.equal(-1);

                done();
            });
    });
});


describe('教研平台API:根据课件ID获取复习课程目录', function () {


    it('正例测试 ', function (done) {
        let url = [host, `/kb_api/v2/review/courses/${reviewCourseId}?${apiKeyStr}`].join('');
        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data).to.have.keys('course');
                //expect(ret.data.course).to.have.keys('name');
                expect(ret.data.course.children).to.be.an('array');
                if (ret.data.course.children.length > 0) {
                    for (let index = 0; index < ret.data.course.children.length; ++index) {
                        expect(ret.data.course.children[index]).to.have.keys('key', 'name', 'children');
                    }
                }
                done();
            });
    });
});


describe('教研平台API:根据课件ID获取同步课程目录', function () {


    it('正例测试 ', function (done) {
        let url = [host, `/kb_api/v2/learning/courses/${learnCourseId}?${apiKeyStr}`].join('');
        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data).to.have.keys('course');
                //expect(ret.data.course).to.have.keys('name','children');
                expect(ret.data.course.children).to.be.an('array');
                if (ret.data.course.children.length > 0) {
                    for (let index = 0; index < ret.data.course.children.length; ++index) {
                        expect(ret.data.course.children[index]).to.have.keys('key', 'name', 'children');
                    }
                }
                done();
            });
    });
});


describe('教研云平台API:根据lesson_id，考法操作', () => {

    let old_category = '测试考点，等下回删除';
    let new_category = '新考点';

    it('正例测试，添加考法', (done) => {
        let url = [host, `/kb_api/v2/lessons/${lessonId}/categorys`, '?', apiKeyStr].join('');
        superagent
            .post(url)
            .send({
                category: old_category
            })
            .end((err, res) => {

                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                done();
            })
    });


    it('正例测试，修改考法名称', (done) => {
        let url = [host, `/kb_api/v2/lessons/${lessonId}/categorys/name`, '?', apiKeyStr].join('');
        superagent
            .put(url)
            .send({
                old_category: old_category, new_category: new_category
            })
            .end((err, res) => {

                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                done();
            })
    });


    it('正例测试，删除考法', (done) => {
        let url = [host, `/kb_api/v2/lessons/${lessonId}/categorys`, '?', apiKeyStr].join('');
        superagent
            .delete(url)
            .send({
                category: new_category
            })
            .end((err, res) => {

                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                done();
            })
    });
});

describe('教研平台API:配置课程难度显示', function () {


    it('正例测试:配置难度显示 ', function (done) {
        let url = [host, `/kb_api/v2/lessons/${lessonId}/difficulty?${apiKeyStr}`].join('');
        superagent
            .put(url)
            .send({ difficulty: ['简单', '中等', '困难'] })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                done();
            });
    });
});

describe('教研平台API:修改知识卡片', function () {


    it('正例测试:修改知识卡片', function (done) {
        let url = [host, `/kb_api/v2/lessons/${lessonId}/contents?${apiKeyStr}`].join('');
        superagent
            .put(url)
            .send({ text: '知识卡片描述测试' })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                done();
            });
    });
});

describe('教研平台API:修改试题描述', function () {
    it('正例测试:修改试题描述', function (done) {
        let url = [host, `/kb_api/v2/lessons/2147418111/questions/3928357375/comments?${apiKeyStr}`].join('');
        superagent
            .put(url)
            .send({
                difficulty: 'easy',
                type: 'homework',
                comment: '修改试题描述测试'
            })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                done();
            });
    });
});

describe('教研平台API:修改试题关联考法', function () {


    it('正例测试:修改试题考法', function (done) {
        let url = [host, `/kb_api/v2/lessons/2147418111/questions/3928357375/category?${apiKeyStr}`].join('');
        superagent
            .put(url)
            .send({
                difficulty: 'easy',
                type: 'homework',
                category: '新考点'
            })
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                let ret = JSON.parse(res.text);
                if (ret.code == 3) return done();
                expect(ret.code).to.be.equal(0);
                done();
            });
    });
});


//describe('教研平台API:根据课件ID修改课件信息',function(){


//     it('正例测试 增加知识点 ',function(done){
//     let putUrl = [host, `/kb_api/v2/lessons/${lessonId}?${apiKeyStr}`].join(''); 
//     let body = {"difficulty":"normal","knowledges":[{"id":2140930047},{"id":1988624383,"content":"<p>声音在传播过程中遇到障碍物要发生反射，形成回声现象．根据这一事实，我们可以根据${s= vt}$测量高大障碍物的远近、海底的深度、远处冰山的距离等..<br/></p>"}]}
//     let bodyStr = JSON.stringify(body);
//         superagent
//         .put(putUrl)
//         .set('Content-Type', 'application/json')
//         .send(bodyStr)
//         .end(function(err, res){

//             expect(err).to.be.an('null');    
//             expect(res).to.not.be.an('null');
//             expect(res.status).to.be.equal(200);
//             var ret = JSON.parse(res.text);
//             expect(ret.code).to.be.equal(0);
//             expect(ret.data.knowledges).to.be.an('array');
//             var ids = _.pluck(ret.data.knowledges,'id')
//             expect(ids.indexOf(addKnowledge)).to.not.be.equal(-1);
//             if (ret.data.knowledges.length > 0){
//                 for (let index = 0; index < ret.data.knowledges.length; ++index){
//                     expect(ret.data.knowledges[index]).to.have.keys('id','name', 'content');
//                 }
//             }
//             done();
//         });
//     });
// });


