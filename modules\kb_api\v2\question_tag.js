const ObjectID = require('mongodb').ObjectID;
const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const schemaQuestionTag = require('./schemas/question_tag');

// 数据库集合名
const questionTagColl = 'kb_tag';
const questionColl = 'question';
const kbFilterColl = 'kb_filter';


const addTagId2kbFilter = (kbFilter, body, id) => {
    for (let filter of kbFilter.filter) {
        // 查找此学段
        if (filter.period === body.period) {
            if (filter.subjects && Array.isArray(filter.subjects)) {
                for (let subject of filter.subjects) {
                    // 查找此学科
                    if (subject.subject === body.subject) {
                        if (subject.types && Array.isArray(subject.types)) {
                            for (let type of subject.types) {
                                // 查找此题型
                                if (type.name === body.type) {
                                    if (type.tag_ids && Array.isArray(type.tag_ids)) {
                                        if (!type.tag_ids.includes(id)) {
                                            type.tag_ids.push(id);
                                        }
                                    } else {
                                        type.tag_ids = [id];
                                    }
                                    break;
                                }
                            }
                        }
                        break;
                    }
                }
            }
            break;
        }
    }
};

// 更新 kb_filter 试题标签id
const updateKbFilterTagId = async (body, id) => {
    let dbs = body.dbs;
    if (!dbs) {
        dbs = 'basic';
    } else if (dbs instanceof Array) {
        dbs = dbs.length > 1 ? dbs[0] : dbs[1];
    } else if (dbs instanceof String) {
        dbs = dbs.split(',')[0];
    }
    delete body.dbs;
    let kbFilter = await db.collection(kbFilterColl).findOne({ resource: 'question', dbs });
    if (kbFilter && Array.isArray(kbFilter.filter)) {
        addTagId2kbFilter(kbFilter, body, id);
        let set = { $set: { filter: kbFilter.filter, utime: new Date() } };
        await db.collection(kbFilterColl).updateOne({ resource: 'question', dbs }, set);
    }
};

const getDataTemplate = (body) => {
    return {
        period: body.period,
        subject: body.subject,
        type: body.type,
        name: body.name,
        tag_type: body.tag_type,
        values: body.values
    }
};

// kbp审核通过，推送过来
const postTag = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    try {
        let data = getDataTemplate(body);
        // 格式校验 map_knows
        if (!ajv.validate(schemaQuestionTag.putQuestionTag, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '试题标签格式错误');
        }
        data.ctime = new Date();
        data.utime = new Date();
        let result = await db.collection(questionTagColl).insertOne(data);
        if (result.insertedId) {
            let id = result.insertedId.toString();
            await updateKbFilterTagId(body, id); // 更新 kb_filter的 试题标签id
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '修改出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};


// kbp审核通过，推送过来
const updateTag = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    let body = req.body;
    try {
        let data = getDataTemplate(body);
        // 格式校验 map_knows
        if (!ajv.validate(schemaQuestionTag.putQuestionTag, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '试题标签格式错误');
        }
        data.utime = new Date();
        let cond = { _id: ObjectID(id) };
        let setData = { $set: data };
        let result = await db.collection(questionTagColl).updateOne(cond, setData, { upsert: true });
        if (result.result.ok === 1) {
            await updateKbFilterTagId(body, id); // 更新 kb_filter的 试题标签id
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '修改出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 过滤出已使用的标签名
const getTagIsUsed = async (id, tagNameArr) => {
    let result = [];
    for (let k = 0, kLen = tagNameArr.length; k < kLen; k++) {
        let tagName = tagNameArr[k];
        let eleMatch = { id: id, $or: [{ 'values.name': tagName }, { 'values.sub_values': tagName }] };
        let cond = { 'type_tags': { $elemMatch: eleMatch } };
        let question = await db.collection(questionColl).findOne(cond, { fields: { _id: 1 } });
        if (question) {
            if (!result.includes(tagName)) {
                result.push(tagName);
            }
        }
    }
    return result;
};

// 检查标签是否已使用
const checkTagIsUsed = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.query.id; // 标签组 id 必传，因为是检查某个标签组下的标签是否使用。
    let name = req.query.name; // 参数标签名称，逗号分割
    if (!id || !name) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数缺少id，标签名称');
    }
    let tagNameArr = name.split(',');
    try {
        let result = await getTagIsUsed(id, tagNameArr);
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 检查标签组名称是否重复
const checkGroupNameExists = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let { id, name, period, subject, type } = req.query;
    if (!name || !period || !subject || !type) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数缺少标签组名称,学段，学科，题型');
    }
    let cond = { period, subject, type, name };
    if (id) {
        cond['_id'] = { $ne: ObjectID(id) };
    }
    let project = { _id: 1 };
    let isExists = false;
    try {
        let questionTag = await db.collection(questionTagColl).findOne(cond, { fields: project });
        if (questionTag) {
            isExists = true;
        }
        responseWrapper.succ({ status: isExists });
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 试题标签列表
const getTagsList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let { period, subject, offset, limit } = req.query;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '传参缺少学段学科');
    }
    offset = offset ? parseInt(offset) : 0;
    limit = limit ? parseInt(limit) : 10;
    try {
        let cond = { period: period, subject: subject };
        let result = await db.collection(questionTagColl).find(cond).skip(offset).limit(limit).sort({ ctime: -1 }).toArray();
        let totalNum = await db.collection(questionTagColl).count(cond);
        for (let item of result) {
            item.id = item._id;
            delete item._id;
        }
        responseWrapper.succ({ total_num: totalNum, list: result });
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 获取所有标签名
const getAllTagNamesArr = (result) => {
    let allTagNameArr = [];
    if (result && Array.isArray(result.values)) {
        for (let value of result.values) {
            if (value.name && !allTagNameArr.includes(value.name)) {
                allTagNameArr.push(value.name);
            }
            if (value.sub_values && Array.isArray(value.sub_values)) {
                for (let sub_value of value.sub_values) {
                    if (sub_value && !allTagNameArr.includes(sub_value)) {
                        allTagNameArr.push(sub_value);
                    }
                }
            }
        }
    }
    return allTagNameArr;
};

// 获取试题标签详情
const getTagDetail = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    try {
        let cond = { _id: ObjectID(id) };
        let result = await db.collection(questionTagColl).findOne(cond);
        if (result && result._id) {
            result.id = id;
            delete result._id;
            let allTagNameArr = getAllTagNamesArr(result); // 获取所有标签名称
            result.tags_used = await getTagIsUsed(id, allTagNameArr); // 获取已经使用的标签
        }
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};


module.exports = {
    postTag,
    updateTag,
    checkTagIsUsed,    // 检查标签是否已使用
    checkGroupNameExists, // 检查标签组名称是否重复
    getTagsList, // 试题标签列表
    getTagDetail // 获取试题标签详情
};
