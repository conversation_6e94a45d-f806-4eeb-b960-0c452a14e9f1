<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校本教辅默认封面设计（新版 - 120x150px）</title>
    <style>
        /* Google Fonts for a more modern look, optional */
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

        body {
            font-family: 'Noto Sans SC', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f0f2f5;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 960px; /* Adjusted for smaller items */
            margin: 0 auto;
        }

        h1, h2 {
            text-align: center;
            margin-bottom: 10px;
        }
        
        h1 {
            color: #1a237e;
        }

        h2 {
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            margin-top: 40px;
            margin-bottom: 20px;
            color: #555;
        }

        .subject-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px; /* Adjusted gap */
            justify-content: center;
        }

        /* --- Core Book Cover Style (MODIFIED) --- */
        /* 尺寸已根据要求修改为 120x150px，并对相关样式进行了等比缩放以保持美观 */
        .book-cover {
            width: 120px; /* MODIFIED */
            height: 150px; /* MODIFIED */
            background-color: #fff;
            border-radius: 6px; /* Adjusted */
            box-shadow: 0 2px 4px rgba(0,0,0,0.1), 0 5px 10px rgba(0,0,0,0.1); /* Adjusted */
            display: flex;
            flex-direction: column;
            color: white;
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .book-cover:hover {
            transform: translateY(-5px) scale(1.03); /* Adjusted */
            box-shadow: 0 4px 8px rgba(0,0,0,0.15), 0 8px 16px rgba(0,0,0,0.15); /* Adjusted */
        }

        .book-header {
            padding: 8px 10px 5px; /* Adjusted */
            font-size: 9px; /* Adjusted */
            font-weight: 500;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px; /* Adjusted */
            flex-shrink: 0;
        }

        .book-title-section {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px; /* Adjusted */
        }

        .main-title {
            font-size: 15px; /* Adjusted */
            font-weight: 700;
            margin: 0;
            line-height: 1.3; /* Adjusted */
        }

        .book-footer {
            padding: 8px 10px; /* Adjusted */
            height: 25px; /* Adjusted */
            box-sizing: border-box;
            flex-shrink: 0;
        }

        /* --- Decorative Elements using Pseudo-elements (MODIFIED for new size) --- */
        .book-cover::before, .book-cover::after {
            content: '';
            position: absolute;
            z-index: 0;
            opacity: 0.1;
        }
        
        .book-header, .book-title-section, .book-footer {
            position: relative;
            z-index: 1;
        }

        /* --- Subject Specific Styles (Adjusted for 120x150px) --- */

        /* 语文 (Chinese) - Red/Brown Tones */
        .subject-chinese.variant-1 { background: linear-gradient(135deg, #8c2318, #5d001e); }
        .subject-chinese.variant-1::before { width: 100%; height: 100%; top: 0; left: 0; background-image: linear-gradient(rgba(255,255,255,.3) 1px, transparent 1px), linear-gradient(90deg, rgba(255,255,255,.3) 1px, transparent 1px); background-size: 15px 15px; opacity: 0.08; }
        .subject-chinese.variant-2 { background: linear-gradient(135deg, #a26d60, #63372c); }
        .subject-chinese.variant-2::after { width: 70px; height: 70px; bottom: -25px; right: -25px; background-color: white; border-radius: 50%; }
        .subject-chinese.variant-3 { background: linear-gradient(135deg, #780000, #c1121f); }
        .subject-chinese.variant-3::before { width: 110px; height: 110px; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(20deg); background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%); }

        /* 数学 (Math) - Blue Tones */
        .subject-math.variant-1 { background: linear-gradient(135deg, #005aa7, #3a7bd5); }
        .subject-math.variant-1::before { width: 100%; height: 100%; top: 0; left: 0; background-image: linear-gradient(rgba(255,255,255,.5) 1px, transparent 1px), linear-gradient(90deg, rgba(255,255,255,.5) 1px, transparent 1px); background-size: 12px 12px; opacity: 0.1; }
        .subject-math.variant-2 { background: linear-gradient(135deg, #2c3e50, #4ca1af); }
        .subject-math.variant-2::after { width: 80px; height: 80px; bottom: -30px; left: -30px; border: 8px solid rgba(255,255,255,0.5); border-radius: 50%; opacity: 0.15; }
        .subject-math.variant-3 { background: linear-gradient(135deg, #1a237e, #283593); }
        .subject-math.variant-3::before { width: 200%; height: 200%; top: -50%; left: -50%; background: repeating-linear-gradient(45deg, transparent, transparent 8px, rgba(255,255,255,0.2) 8px, rgba(255,255,255,0.2) 16px); opacity: 0.05; }
        
        /* 英语 (English) - Purple/Deep Blue Tones */
        .subject-english.variant-1 { background: linear-gradient(135deg, #4a00e0, #8e2de2); }
        .subject-english.variant-1::before { content: 'A B C'; font-size: 80px; font-weight: 700; color: white; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-15deg); opacity: 0.07; }
        .subject-english.variant-2 { background: linear-gradient(135deg, #141e30, #243b55); }
        .subject-english.variant-2::after { width: 100%; height: 45px; bottom: 30px; left: 0; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent, rgba(255,255,255,0.3), transparent); background-size: 30px 100%; opacity: 0.1; }
        .subject-english.variant-3 { background: linear-gradient(135deg, #302b63, #0f0c29); }
        .subject-english.variant-3::before { width: 45px; height: 110px; bottom: 0; right: 10px; background: white; opacity: 0.05; clip-path: polygon(40% 0, 60% 0, 60% 80%, 100% 80%, 100% 100%, 0 100%, 0 80%, 40% 80%); }

        /* 物理 (Physics) - Dark Gray/Cyan Tones */
        .subject-physics.variant-1 { background: linear-gradient(135deg, #0f2027, #203a43, #2c5364); }
        .subject-physics.variant-1::before { width: 110px; height: 110px; top: 50%; left: 50%; transform: translate(-50%, -50%); border: 2px solid white; border-radius: 50%; opacity: 0.15; }
        .subject-physics.variant-1::after { width: 110px; height: 110px; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(60deg); border: 2px solid white; border-radius: 50%; opacity: 0.15; }
        .subject-physics.variant-2 { background: linear-gradient(135deg, #1d2b64, #00d2ff); }
        .subject-physics.variant-2::before { width: 100%; height: 100%; background: linear-gradient(to right, rgba(255,255,255,0.2) 1px, transparent 1px) 0 0/30px 30px, linear-gradient(to bottom, rgba(255,255,255,0.2) 1px, transparent 1px) 0 0/30px 30px; opacity: 0.1; }

        /* 化学 (Chemistry) - Green/Teal Tones */
        .subject-chemistry.variant-1 { background: linear-gradient(135deg, #004d40, #00796b); }
        .subject-chemistry.variant-1::before { width: 60px; height: 70px; top: 20px; right: -20px; background: rgba(255,255,255,0.2); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); opacity: 0.2; }
        .subject-chemistry.variant-2 { background: linear-gradient(135deg, #135058, #317b2c); }
        .subject-chemistry.variant-2::after { width: 70px; height: 70px; bottom: 10px; left: 10px; border: 3px solid white; border-radius: 50%; opacity: 0.1; }

        /* 生物 (Biology) - Green/Brown Tones */
        .subject-biology.variant-1 { background: linear-gradient(135deg, #1b5e20, #4c8c4a); }
        .subject-biology.variant-1::before { width: 50px; height: 125px; top: 10px; left: 50%; transform: translateX(-50%); background: url("data:image/svg+xml,%3Csvg width='100' height='250' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20,0 C80,50 20,100 80,150 C20,200 80,250 20,300' stroke='white' stroke-width='4' fill='none'/%3E%3Cpath d='M80,0 C20,50 80,100 20,150 C80,200 20,250 80,300' stroke='white' stroke-width='4' fill='none'/%3E%3C/svg%3E") no-repeat center center; background-size: contain; opacity: 0.15; }
        .subject-biology.variant-2 { background: linear-gradient(135deg, #386641, #6a994e); }
        .subject-biology.variant-2::after { width: 150%; height: 150%; top: -25%; left: -25%; background-image: repeating-linear-gradient(15deg, transparent, transparent 5px, rgba(255,255,255,0.1) 5px, rgba(255,255,255,0.1) 6px); opacity: 0.2; }
        
        /* 历史 (History) - Sepia/Brown Tones */
        .subject-history.variant-1 { background: linear-gradient(135deg, #5d4037, #8d6e63); }
        .subject-history.variant-1::before { width: 100%; height: 100%; background-color: #fdf6e3; opacity: 0.08; mix-blend-mode: overlay; }
        .subject-history.variant-2 { background: linear-gradient(135deg, #4e342e, #795548); }
        .subject-history.variant-2::after { width: 100%; height: 30px; top: 50%; left: 0; transform: translateY(-50%); background: rgba(255,255,255,0.1); border-top: 1px solid rgba(255,255,255,0.2); border-bottom: 1px solid rgba(255,255,255,0.2); }
        
        /* 地理 (Geography) - Blue/Green/Earth Tones */
        .subject-geography.variant-1 { background: linear-gradient(135deg, #0277bd, #2e7d32); }
        .subject-geography.variant-1::before { width: 110px; height: 110px; top: -30px; right: -30px; border: 1px solid white; border-radius: 50%; box-shadow: 0 0 0 5px #0000, 0 0 0 6px white, 0 0 0 13px #0000, 0 0 0 14px white; opacity: 0.1; }
        .subject-geography.variant-2 { background: linear-gradient(135deg, #547c84, #a3b18a); }
        .subject-geography.variant-2::after { width: 140px; height: 140px; top: 50%; left: 50%; transform: translate(-50%, -50%); background: radial-gradient(circle at center, transparent 40%, rgba(255,255,255,0.2) 41%, rgba(255,255,255,0.2) 42%, transparent 43%), radial-gradient(circle at center, transparent 60%, rgba(255,255,255,0.2) 61%, rgba(255,255,255,0.2) 62%, transparent 63%); opacity: 0.2; }
        
        /* 政治 (Politics) - Red/Gold Tones */
        .subject-politics.variant-1 { background: linear-gradient(135deg, #b71c1c, #d32f2f); }
        .subject-politics.variant-1::before { width: 160px; height: 160px; top: -80px; left: -80px; background: radial-gradient(circle, rgba(255,215,0,0.2), transparent 60%); opacity: 0.5; }
        .subject-politics.variant-2 { background: linear-gradient(135deg, #c62828, #fbc02d); }
        .subject-politics.variant-2::after { content: '★'; font-size: 80px; color: white; top: 10px; right: 10px; opacity: 0.15; transform: rotate(15deg); }

    </style>
</head>
<body>

    <div class="container">
        <h1>K12校本教辅 · 默认封面设计</h1>

        <!-- 语文 -->
        <h2>语文 (Chinese)</h2>
        <div class="subject-row">
            <div class="book-cover subject-chinese variant-1">
                <div class="book-header">语文 | CHINESE</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-chinese variant-2">
                <div class="book-header">语文 | CHINESE</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-chinese variant-3">
                <div class="book-header">语文 | CHINESE</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
        </div>

        <!-- 数学 -->
        <h2>数学 (Mathematics)</h2>
        <div class="subject-row">
            <div class="book-cover subject-math variant-1">
                <div class="book-header">数学 | MATHEMATICS</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-math variant-2">
                <div class="book-header">数学 | MATHEMATICS</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-math variant-3">
                <div class="book-header">数学 | MATHEMATICS</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
        </div>
        
        <!-- 英语 -->
        <h2>英语 (English)</h2>
        <div class="subject-row">
            <div class="book-cover subject-english variant-1">
                <div class="book-header">英语 | ENGLISH</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-english variant-2">
                <div class="book-header">英语 | ENGLISH</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-english variant-3">
                <div class="book-header">英语 | ENGLISH</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
        </div>

        <!-- 物理 -->
        <h2>物理 (Physics)</h2>
        <div class="subject-row">
            <div class="book-cover subject-physics variant-1">
                <div class="book-header">物理 | PHYSICS</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-physics variant-2">
                <div class="book-header">物理 | PHYSICS</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
        </div>

        <!-- 化学 -->
        <h2>化学 (Chemistry)</h2>
        <div class="subject-row">
            <div class="book-cover subject-chemistry variant-1">
                <div class="book-header">化学 | CHEMISTRY</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-chemistry variant-2">
                <div class="book-header">化学 | CHEMISTRY</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
        </div>

        <!-- 生物 -->
        <h2>生物 (Biology)</h2>
        <div class="subject-row">
            <div class="book-cover subject-biology variant-1">
                <div class="book-header">生物 | BIOLOGY</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-biology variant-2">
                <div class="book-header">生物 | BIOLOGY</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
        </div>
        
        <!-- 历史 -->
        <h2>历史 (History)</h2>
        <div class="subject-row">
            <div class="book-cover subject-history variant-1">
                <div class="book-header">历史 | HISTORY</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-history variant-2">
                <div class="book-header">历史 | HISTORY</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
        </div>
        
        <!-- 地理 -->
        <h2>地理 (Geography)</h2>
        <div class="subject-row">
            <div class="book-cover subject-geography variant-1">
                <div class="book-header">地理 | GEOGRAPHY</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-geography variant-2">
                <div class="book-header">地理 | GEOGRAPHY</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
        </div>
        
        <!-- 政治 -->
        <h2>政治 (Politics)</h2>
        <div class="subject-row">
            <div class="book-cover subject-politics variant-1">
                <div class="book-header">政治 | POLITICS</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
            <div class="book-cover subject-politics variant-2">
                <div class="book-header">政治 | POLITICS</div>
                <div class="book-title-section">
                    <h3 class="main-title"></h3>
                </div>
                <div class="book-footer"></div>
            </div>
        </div>

    </div>

</body>
</html>
