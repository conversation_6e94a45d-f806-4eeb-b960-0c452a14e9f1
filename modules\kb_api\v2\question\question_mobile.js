let config = require('config');
let URL = require('url');
let request = require('request');
let Logger = require('../../../common/utils/logger');
let mongodber = require('../../../common/utils/mongodber');
let db = mongodber.use('KB');
let IMAGESERV = config.get('image_serv');

/**
 * 查询需要更新到question_mobile表的question id
 * @param cond
 * @param device
 */
function updateQuestionMobile(cond, device) {
    if ('mobile' === device) {
        let questionColl = db.collection('question');
        let questionMobileColl = db.collection('question_mobile');
        let getQuestion = questionColl.find(cond).project({'utime': 1}).toArray();
        let getQuestionMobile = questionMobileColl.find(cond).project({'utime': 1}).toArray();
        Promise.all([getQuestion, getQuestionMobile]).then(function (result) {
            let question = result[0];
            let questionMobile = result[1];
            for (let i = 0, n = question.length; i < n; i++) {
                let isExist = false;
                for (let j = 0, m = questionMobile.length; j < m; j++) {
                    if (question[i]._id === questionMobile[j]._id) {
                        if (question[i].utime.getTime() === questionMobile[j].utime.getTime()) {
                            isExist = true;
                        }
                        break;
                    }
                }
                if (!isExist) {
                    _requestUpdateQuestionMobile(question[i]._id);
                }
            }
        });
    }
}

/**
 * 发送更新question_mobile请求
 * @param {*} qid
 * @private
 */
function _requestUpdateQuestionMobile(qid) {
    let imageUrl = URL.format({
        protocol: IMAGESERV.protocol,
        hostname: IMAGESERV.hostname,
        port: IMAGESERV.port,
        pathname: `utilbox_api/v1/question_mobile/${qid}`
    });
    request.put({
        url: imageUrl,
        headers: {
            'content-type': 'application/json'
        },
        timeout: 50000
    }, function (error, response) {
        if (error || (response && response.statusCode !== 200)) {
            Logger.warn({
                who: `queston_id:${qid}`,
                where: `${__filename} _requestUpdateQuestionMobile`,
                what: error,
                how: response
            });
        }
    });
}

// 传递参数是查询出来的试题列表
function updateQuestionMobileByQuesion(quesionsArr, device) {
    if (device === 'mobile' && Array.isArray(quesionsArr) && quesionsArr.length > 0) {
        let questionIdsArr = quesionsArr.map(function (q) {
            return q.id;
        });
        let cond = {_id: {$in: questionIdsArr}};
        updateQuestionMobile(cond, device);
    }
}

module.exports = {
    updateQuestionMobile,
    updateQuestionMobileByQuesion
};
