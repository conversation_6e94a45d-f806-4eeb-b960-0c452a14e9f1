// 试题标签路由
const questionTag = require('../../../modules/kb_api/v2/question_tag');

module.exports = function (router) {
    // 检查标签是否已使用
    router.get('/question_tags/is_used', questionTag.checkTagIsUsed);
    // 检查标签组名称是否重复
    router.get('/question_tags/group_name/is_exists', questionTag.checkGroupNameExists);
    // 试题标签列表
    router.get('/question_tags/list', questionTag.getTagsList);
    // 获取试题标签详情
    router.get('/question_tags/:id', questionTag.getTagDetail);
    // kbp审核通过，推送过来，更新
    router.put('/question_tags/:id', questionTag.updateTag);
    // kbp审核通过，推送过来,添加
    router.post('/question_tags', questionTag.postTag);
};