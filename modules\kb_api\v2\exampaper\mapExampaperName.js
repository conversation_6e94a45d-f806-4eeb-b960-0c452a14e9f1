const mapGrade = function (data) {
    let mapObj = {
        '小升初专题': '六年级下',
        '中考专题': '九年级下',
        '高考专题': '高三下'
    };
    return mapObj[data] || data;
};

const mapYear = function (from, to) {
    let year = [from, to].filter(i => i);
    let l = year.length;
    if (l) {
        switch (l) {
            case 1:
                year = year.join('') + '年';
                break;
            case 2:
                if (Number(from) === Number(to)) {
                    year = from + '年';
                } else {
                    year = year.join('-') + '学年';
                }
                break;
        }
    } else {
        year = '';
    }
    return year;
};

// 题库取模糊的试卷名称
const mapExampaperName = function (data, query) {
    // if ('mine' === query.access) { // 如果是我的，则展示真实名称
    //     return data.name;
    // }
    // let key = query.api_key || query.app_key;
    // return key.includes('tiku') ? data.vague_name || data.name : data.name;
    let name = data.vague_name || data.name;
    if (name.includes('某校')) name = name.replace('某校', '');
    data.vague_name = name;
    return name;
};

module.exports = mapExampaperName;
