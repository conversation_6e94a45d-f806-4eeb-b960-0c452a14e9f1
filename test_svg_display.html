<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG封面测试显示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .covers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .cover-item {
            text-align: center;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .cover-item svg {
            border-radius: 6px;
            transition: transform 0.3s ease;
        }
        
        .cover-item:hover svg {
            transform: scale(1.05);
        }
        
        .cover-label {
            margin-top: 10px;
            font-size: 14px;
            font-weight: bold;
            color: #555;
        }
        
        .subject-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 30px 0 20px 0;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 优化后的SVG封面效果展示</h1>
        
        <div class="subject-title">🔤 语文 (Chinese)</div>
        <div class="covers-grid">
            <div class="cover-item">
                <object data="svg_covers/语文_variant-1.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/语文_variant-1.svg" alt="语文 variant-1" width="120" height="150">
                </object>
                <div class="cover-label">语文 - 变体1</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/语文_variant-2.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/语文_variant-2.svg" alt="语文 variant-2" width="120" height="150">
                </object>
                <div class="cover-label">语文 - 变体2</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/语文_variant-3.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/语文_variant-3.svg" alt="语文 variant-3" width="120" height="150">
                </object>
                <div class="cover-label">语文 - 变体3</div>
            </div>
        </div>
        
        <div class="subject-title">🔢 数学 (Mathematics)</div>
        <div class="covers-grid">
            <div class="cover-item">
                <object data="svg_covers/数学_variant-1.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/数学_variant-1.svg" alt="数学 variant-1" width="120" height="150">
                </object>
                <div class="cover-label">数学 - 变体1</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/数学_variant-2.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/数学_variant-2.svg" alt="数学 variant-2" width="120" height="150">
                </object>
                <div class="cover-label">数学 - 变体2</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/数学_variant-3.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/数学_variant-3.svg" alt="数学 variant-3" width="120" height="150">
                </object>
                <div class="cover-label">数学 - 变体3</div>
            </div>
        </div>
        
        <div class="subject-title">🇬🇧 英语 (English)</div>
        <div class="covers-grid">
            <div class="cover-item">
                <object data="svg_covers/英语_variant-1.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/英语_variant-1.svg" alt="英语 variant-1" width="120" height="150">
                </object>
                <div class="cover-label">英语 - 变体1</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/英语_variant-2.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/英语_variant-2.svg" alt="英语 variant-2" width="120" height="150">
                </object>
                <div class="cover-label">英语 - 变体2</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/英语_variant-3.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/英语_variant-3.svg" alt="英语 variant-3" width="120" height="150">
                </object>
                <div class="cover-label">英语 - 变体3</div>
            </div>
        </div>
        
        <div class="subject-title">⚛️ 物理 (Physics)</div>
        <div class="covers-grid">
            <div class="cover-item">
                <object data="svg_covers/物理_variant-1.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/物理_variant-1.svg" alt="物理 variant-1" width="120" height="150">
                </object>
                <div class="cover-label">物理 - 变体1</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/物理_variant-2.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/物理_variant-2.svg" alt="物理 variant-2" width="120" height="150">
                </object>
                <div class="cover-label">物理 - 变体2</div>
            </div>
        </div>
        
        <div class="subject-title">🧪 化学 (Chemistry)</div>
        <div class="covers-grid">
            <div class="cover-item">
                <object data="svg_covers/化学_variant-1.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/化学_variant-1.svg" alt="化学 variant-1" width="120" height="150">
                </object>
                <div class="cover-label">化学 - 变体1</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/化学_variant-2.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/化学_variant-2.svg" alt="化学 variant-2" width="120" height="150">
                </object>
                <div class="cover-label">化学 - 变体2</div>
            </div>
        </div>
        
        <div class="subject-title">🌱 生物 (Biology)</div>
        <div class="covers-grid">
            <div class="cover-item">
                <object data="svg_covers/生物_variant-1.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/生物_variant-1.svg" alt="生物 variant-1" width="120" height="150">
                </object>
                <div class="cover-label">生物 - 变体1</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/生物_variant-2.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/生物_variant-2.svg" alt="生物 variant-2" width="120" height="150">
                </object>
                <div class="cover-label">生物 - 变体2</div>
            </div>
        </div>
        
        <div class="subject-title">📜 历史 (History)</div>
        <div class="covers-grid">
            <div class="cover-item">
                <object data="svg_covers/历史_variant-1.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/历史_variant-1.svg" alt="历史 variant-1" width="120" height="150">
                </object>
                <div class="cover-label">历史 - 变体1</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/历史_variant-2.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/历史_variant-2.svg" alt="历史 variant-2" width="120" height="150">
                </object>
                <div class="cover-label">历史 - 变体2</div>
            </div>
        </div>
        
        <div class="subject-title">🌍 地理 (Geography)</div>
        <div class="covers-grid">
            <div class="cover-item">
                <object data="svg_covers/地理_variant-1.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/地理_variant-1.svg" alt="地理 variant-1" width="120" height="150">
                </object>
                <div class="cover-label">地理 - 变体1</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/地理_variant-2.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/地理_variant-2.svg" alt="地理 variant-2" width="120" height="150">
                </object>
                <div class="cover-label">地理 - 变体2</div>
            </div>
        </div>
        
        <div class="subject-title">🏛️ 政治 (Politics)</div>
        <div class="covers-grid">
            <div class="cover-item">
                <object data="svg_covers/政治_variant-1.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/政治_variant-1.svg" alt="政治 variant-1" width="120" height="150">
                </object>
                <div class="cover-label">政治 - 变体1</div>
            </div>
            <div class="cover-item">
                <object data="svg_covers/政治_variant-2.svg" type="image/svg+xml" width="120" height="150">
                    <img src="svg_covers/政治_variant-2.svg" alt="政治 variant-2" width="120" height="150">
                </object>
                <div class="cover-label">政治 - 变体2</div>
            </div>
        </div>
        
        <div style="background: #e8f4fd; padding: 20px; border-radius: 8px; margin-top: 40px; text-align: center;">
            <h2 style="color: #1976d2; margin-bottom: 10px;">✨ 优化说明</h2>
            <p style="color: #555; margin: 5px 0;">本次优化更精确地还原了原始HTML的样式效果</p>
            <p style="color: #555; margin: 5px 0;">包括：渐变背景、装饰图案、阴影效果、字体样式等</p>
            <p style="color: #555; margin: 5px 0;">所有SVG文件都支持无损缩放，适合各种应用场景</p>
        </div>
    </div>
</body>
</html>
