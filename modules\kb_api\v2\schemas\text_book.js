/**
 * 教材校验schema
 */
const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const Logger = require('../../../common/utils/logger');

const postSchema = {
    type: 'object',
    additionalProperties: false,
    required: ['period', 'subject','grade', 'press_version', 
    'profile_url', 'host','catalogs','pages'],
    properties: {
        period: {
            type: 'string'
        },
        subject: {
            type: 'string'
        },
        press_version: {
            type: 'string',
        },
        profile_url: {
            type: 'string',
        },
        host: {
            type: 'string',
        },
        catalogs: {
            type: 'array',
        },
        pages: {
            type: 'array',
        },
        grade: {
            type: 'string',
        }
    }
};

const putSchema = {
    type: 'object',
    additionalProperties: false,
    required: ['id','period', 'subject','grade', 'press_version', 
    'profile_url', 'host','catalogs','pages'],
    properties: {
        id: {
            type: 'number'
        },
        period: {
            type: 'string'
        },
        subject: {
            type: 'string'
        },
        press_version: {
            type: 'string',
        },
        profile_url: {
            type: 'string',
        },
        host: {
            type: 'string',
        },
        catalogs: {
            type: 'array',
        },
        pages: {
            type: 'array',
        },
        grade: {
            type: 'string',
        }
    }
};

const postValidate = (data) => {
    if (!ajv.validate(postSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('教材格式错误');
    }
};

const putValidate = (data) => {
    if (!ajv.validate(putSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('教材格式错误');
    }
};


module.exports = {
    postValidate,
    putValidate
};
