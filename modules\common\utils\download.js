const { ObjectID } = require('mongodb');
const mongodber = require('./mongodber');
const db = mongodber.use('KB');

/**
 * 更新资源的查看次数
 * @param {*} ids 
 * @param {*} type 
 */
const inscreaceDownTimes = async (ids, type, album_id) => {
	try {
		if (['question', 'exampaper'].indexOf(type) === -1) {
			return;
		}
		await db.collection(type).updateMany(
			{ _id: { $in: ids } },
			{ '$inc': { 'download_times': 1 } },
		);
		if (album_id) {
			await db.collection('album').updateOne({ _id: ObjectID(album_id) }, {
				'$inc': { 'download_times': 1 }
			});
		}
	} catch (err) {
		throw err;
	}
}

module.exports = {
	inscreaceDownTimes,
}
