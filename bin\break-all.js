const MongoClient = require('mongodb').MongoClient;
const url = process.argv[2];
 
// Database Name
const dbName = 'kb';

const replace = (data) => {
    if (data instanceof Date) {
        return;
    }
    if (typeof data === 'object' && data !== null) {
        for (let ix in data) {
            if (typeof data[ix] === 'string') {
                data[ix] = data[ix].replace(/word-break: break-all;/g, '');
            } else {
                replace(data[ix]);
            }
        }
    }
}
 
// Use connect method to connect to the server
const run = async (list) => {
    let client = await MongoClient.connect(url);
    const db = client.db(dbName);
    for (let id of list) {
        let question = await db.collection('question').findOne({_id: Number(id)});
        replace(question);
        // await db.collection('question').updateOne({_id: Number(id)}, {$set: question});
        console.log(question);
    }
    client.close();
}

run(process.argv.slice(3));