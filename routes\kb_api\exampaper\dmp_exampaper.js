// dmp试卷路由
const dmp_exampaper = require('../../../modules/kb_api/v2/exampaper/dmp_exampaper');


module.exports = function (router) {

    // dmp 根据试卷ID获取试卷试题细目和知识点分值/考频统计
    router.get('/exampaper_statistics/briefs', dmp_exampaper.getExampapersBrief);
    router.get('/exampaper_statistics/parent_knowledges', dmp_exampaper.getParentKnowledgeMap);
    router.get('/exampaper_statistics/:exampaper_id', dmp_exampaper.getExampaperStatistics);
    router.get('/exampaper_statistics/second_knowledges/:exampaper_ids', dmp_exampaper.getExamScopeByManyExampapers);

};