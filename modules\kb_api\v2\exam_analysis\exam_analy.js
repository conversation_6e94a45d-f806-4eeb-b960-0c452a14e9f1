/**
 * 给辅导提供的学校考情分析
 */
const logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');

/**
 * 获取所有学校列表
 * @param {object} req 
 * @param {object} res 
 */
const getSchools = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);
    let { period, subject, grade, exam_type, event_time, city } = req.query;
    if (!period || !subject || !grade || !city) {
        return resWrapper.error('PARAMETERS_ERROR', '参数缺少:period,subject,grade,city');
    }
    let pro = { sch_id: 1, sch_name: 1 };
    let cond = {
        subject, period, grade, city,
        aggregate_mode: 'school_five_year',
    };
    if (exam_type) {
        cond.exam_type = exam_type;
    }
    if (event_time) {
        cond.event_time = event_time;
    }
    let objUniq = {};
    let result = [];
    try {
        let dataArr = await db.collection('aggregate_exam_analy').find(cond).project(pro).toArray();
        for (let sch of dataArr) {
            if (!objUniq[sch.sch_id.toString()]) {
                objUniq[sch.sch_id.toString()] = true;
                result.push({ sch_id: sch.sch_id, sch_name: sch.sch_name });
            }
        }
        result = result || [];
        resWrapper.succ(result);
    } catch (err) {
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * 考情分析汇总
 * @param {object} req 
 * @param {object} res 
 */
const getAggregate = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);
    let { period, subject, grade, exam_type, event_time, sch_id, city } = req.query;
    if (!period || !subject || !grade || !exam_type || !event_time) {
        return resWrapper.error('PARAMETERS_ERROR', '参数缺少:period,subject,grade,exam_type,event_time');
    }
    let schOrCity = '';
    if (sch_id) {
        schOrCity = sch_id;
    } else if (city) {
        schOrCity = city;
    } else {
        return resWrapper.error('PARAMETERS_ERROR', '缺少sch_id或者city');
    }
    let _id = `${period}-${subject}-${grade}-${exam_type}-${event_time}-${schOrCity}`;
    let cond = { _id: _id };
    try {
        let result = await db.collection('aggregate_exam_analy').findOne(cond);
        result = result || {};
        resWrapper.succ(result);
    } catch (err) {
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * 考情分析单个试卷
 * @param {object} req 
 * @param {object} res 
 */
const getSingle = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);
    let { single_id } = req.query;
    if (!single_id) {
        return resWrapper.error('PARAMETERS_ERROR', '参数缺少single_id');
    }
    let cond = { _id: single_id };
    try {
        let result = await db.collection('single_exam_analy').findOne(cond);
        result = result || {};
        resWrapper.succ(result);
    } catch (err) {
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getSingle,
    getAggregate,
    getSchools,
};