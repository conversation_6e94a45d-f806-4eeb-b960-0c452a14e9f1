const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const ResponseWrapper = require('../../common/utils/response_wrapper');
let Logger = require('../../common/utils/logger');
const counter = require('../../common/utils/counter');
const _ = require('lodash');
const Joi = require('@hapi/joi');

// 教学辅助工具分类
const schema_category = 'education_assistant_tool_category';
// 教学辅助工具
const schema = 'education_assistant_tool';

module.exports = {
    getCategory,
    addCategory,
    updateCategory,
    deleteCategory,
    updateCategoryOrder,
    getList,
    getById,
    getByIds,
    add,
    update,
    updateTimes,
    delByIds,
};

async function getCategory(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const query = {
            invalid: 0,
        };
        const list = await db.collection(schema_category).find(query).project({_id: 1, name: 1, parent_id: 1}).sort({order_no: 1}).toArray();
        if (_.size(list)) {
            for (const data of list) {
                data.id = data._id;
                delete data._id;
            }
        }
        const result = buildTree(list);
        return responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

function buildTree(data, parentId = 0) {
    const tree = [];
    for (const item of data) {
        if (item.parent_id === parentId) {
            const children = buildTree(data, item.id);
            if (children.length) {
                item.children = children;
            }
            tree.push(item);
        }
    }
    return tree;
}

const JOI_ADD_CATEGORY = Joi.object({
    parent_id: Joi.number().required(),
    name: Joi.string().required(),
}).unknown(true);

async function addCategory(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_ADD_CATEGORY.validate(req.body);
        const ids = await counter.nextSeq(schema_category, 1);
        const total = await db.collection(schema_category).find({}).count()
        const date = new Date();
        const doc = {
            _id: ids[0],
            name: params.name,
            parent_id: params.parent_id,
            order_no: total + 1,
            invalid: 0,
            ctime: date,
            utime: date
        };
        await db.collection(schema_category).insertOne(doc);
        return responseWrapper.succ({id: doc._id});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


const JOI_UPDATE_CATEGORY = Joi.object({
    id: Joi.number().required(),
    parent_id: Joi.number().required(),
    name: Joi.string().required(),
}).unknown(true);

async function updateCategory(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const {id, parent_id, name} = await JOI_UPDATE_CATEGORY.validate(_.assign(req.params, req.body));
        const data = await db.collection(schema_category).findOne({_id: id});
        if (_.isEmpty(data) || data.invalid === 1) return responseWrapper.error('PARAMETERS_ERROR', '修改数据不存在');
        const doc = {
            name: name,
            parent_id: parent_id,
            utime: new Date()
        };
        await db.collection(schema_category).updateOne({_id: id}, {$set: doc});
        return responseWrapper.succ({id});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


const JOI_DELETE_CATEGORY = Joi.object({
    id: Joi.number().required(),
}).unknown(true);

async function deleteCategory(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const { id } = await JOI_DELETE_CATEGORY.validate(req.params);
        const data = await db.collection(schema_category).findOne({_id: id});
        if (_.isEmpty(data) || data.invalid === 1) return responseWrapper.error('PARAMETERS_ERROR', '修改数据不存在');
        const doc = {
            invalid: 1,
            utime: new Date()
        };
        await db.collection(schema_category).updateOne({_id: id}, {$set: doc});
        return responseWrapper.succ({id});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_UPDATE_CATEGORY_ORDER = Joi.object({
    id: Joi.number().required(),
    hover_id: Joi.number().required(),
    parent_id: Joi.number().required(),
}).unknown(true);

async function updateCategoryOrder(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const { id, hover_id, parent_id } = await JOI_UPDATE_CATEGORY_ORDER.validate(_.assign(req.params, req.body));
        const data = await db.collection(schema_category).findOne({_id: id});
        if (_.isEmpty(data) || data.invalid === 1) return responseWrapper.error('PARAMETERS_ERROR', '修改数据不存在');
        const hover_data = await db.collection(schema_category).findOne({_id: hover_id});
        if (_.isEmpty(hover_data) || hover_data.invalid === 1) return responseWrapper.error('PARAMETERS_ERROR', '目标数据不存在');
        if (data.order_no === hover_data.order_no) {
            return responseWrapper.succ({id});
        }
        let up = true;
        if (data.order_no < hover_data.order_no) {
            up = false;
        }
        if (up) {
            await db.collection(schema_category).updateMany({order_no: {$gte: hover_data.order_no, $lt: data.order_no}}, {$inc: {order_no: 1}});
        } else {
            await db.collection(schema_category).updateMany({order_no: {$gt: data.order_no, $lte: hover_data.order_no}}, {$inc: {order_no: -1}});
        }
        const date = new Date();
        const doc = {
            order_no: hover_data.order_no,
            parent_id: parent_id,
            utime: date
        };
        await db.collection(schema_category).updateOne({_id: id}, {$set: doc});
        return responseWrapper.succ({id});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

function getPathName(el, list, pathname= '') {
    if (!pathname) pathname = el.name;
    const parent = list.find(e => e.id === el.parent_id);
    if (parent) {
        pathname = `${parent.name}/${pathname}`;
        return getPathName(parent, list, pathname);
    }
    return pathname;
}

const JOI_GET_LIST = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    keyword: [
        Joi.number(),
        Joi.string()
    ],
    category_id: Joi.number().optional(),
    ctime_from: Joi.number().optional(),
    ctime_to: Joi.number().optional(),
    user: [
        Joi.number(),
        Joi.string()
    ]
}).unknown(true);

//获取编目下教材信息
async function getList(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_GET_LIST.validate(req.query);
        const isTiku = req.query.api_key.indexOf('tiku') > -1;
        let limit = params.limit || 10;
        let offset = params.offset || 0;
        let cond = {
            invalid: 0,
        };
        if (params.keyword) {
            const keyword = params.keyword;
            if (_.isNumber(keyword)) {
                cond._id = parseInt(keyword);
            } else {
                cond.name = keyword;
            }
        }
        const category_list = await db.collection(schema_category).find({}).toArray();
        for (const cat of category_list) {
            cat.id = cat._id;
            delete cat._id;
        }
        if (params.category_id) {
            // 题库处理
            if (isTiku) { // 题库查询所有子节点
                const treeList = buildTree(category_list);
                const category_ids = [];
                const get_category_ids = (items, child = false) => {
                    if (!_.size(items)) return;
                    for (const c of items) {
                        if (c.id === params.category_id || child) {
                            category_ids.push(c.id);
                            get_category_ids(c.children, true);
                        } else {
                            get_category_ids(c.children, false);
                        }
                    }
                }
                get_category_ids(treeList);
                cond.category_id = {
                    $in: category_ids
                }
            } else {
                cond.category_id = params.category_id;
            }
        }
        if (params.ctime_from && params.ctime_to) {
            cond.ctime = {
                $gte: new Date(params.ctime_from),
                $lte: new Date(params.ctime_to)
            }
        }

        if (params.user) {
            if (_.isNumber(params.user)) {
                cond.user_id = parseInt(params.user);
            } else {
                cond.user_name = params.user;
            }
        }
        const final = {
            total_num: 0,
            records: []
        }
        let total_num = await db.collection(schema).find(cond).count();
        if (!total_num) return responseWrapper.succ(final);
        final.total_num = total_num;

        const list = await db.collection(schema).find(cond).sort({ctime: -1}).skip(offset).limit(limit).toArray();
        for (const data of list) {
            const category = category_list.find(e => e.id === data.category_id);
            final.records.push({
                id: data._id,
                name: data.name,
                category_id: data.category_id,
                category_name: category.name,
                category_path_name: getPathName(category, category_list),
                host: data.host,
                url: data.url,
                suffix: data.suffix,
                size: data.size || 0,
                ctime: data.ctime.getTime(),
                user_id: data.user_id,
                user_name: data.user_name,
                download_times: data.download_times,
                view_times: data.view_times,
            });
        }
        return responseWrapper.succ(final);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


const JOI_GEI_BY_ID = Joi.object({
    id: Joi.number().required(),
});

async function getById(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_GEI_BY_ID.validate(req.params);
        let id = params.id;
        let data = await db.collection(schema).findOne({ _id: id });
        if (_.isEmpty(data) || data.invalid === 1) return responseWrapper.succ(null);
        const category_list = await db.collection(schema_category).find({}).toArray();
        for (const cat of category_list) {
            cat.id = cat._id;
            delete cat._id;
        }
        const category = category_list.find(e => e.id === data.category_id);
        data.id = data._id;
        data.ctime = data.ctime.getTime();
        data.utime = data.utime.getTime();
        delete data._id;
        delete data.invalid;
        data.category_name = category.name;
        data.category_path_name = getPathName(category, category_list);
        responseWrapper.succ(data);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


const JOI_GEI_BY_IDS = Joi.object({
    ids: Joi.string().required(),
});

async function getByIds(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_GEI_BY_IDS.validate(req.params);
        let ids = params.ids.split(',');
        let datas = await db.collection(schema).find({ _id: {$in: ids.map(e => Number(e))} }).toArray();
        const category_list = await db.collection(schema_category).find({}).toArray();
        for (const cat of category_list) {
            cat.id = cat._id;
            delete cat._id;
        }
        const list = [];
        for (const data of datas) {
            const category = category_list.find(e => e.id === data.category_id);
            data.id = data._id;
            data.ctime = data.ctime.getTime();
            data.utime = data.utime.getTime();
            delete data._id;
            delete data.invalid;
            data.category_name = category.name;
            data.category_path_name = getPathName(category, category_list);
            list.push(data);
        }
        return responseWrapper.succ(list);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


const JOI_POST_DATA = Joi.array().items(Joi.object({
    name: Joi.string().required(),
    category_id: Joi.number().required(),
    host: Joi.string().required(),
    url: Joi.string().required(),
    suffix: Joi.string().required(),
    from: Joi.string().optional().default(''),
    size: Joi.number().integer().required(),
    user_id: Joi.number().required(),
    user_name: Joi.string().required(),
})).required().min(1);
//新增教辅
async function add(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let now = new Date();
    try {
        // 格式校验
        const params = await JOI_POST_DATA.validate(req.body);
        if (!_.size(params)) return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
        const ids = await counter.nextSeq(schema, _.size(params));
        for (let i = 0; i < _.size(params); i++) {
            const data = params[i];
            data._id = ids[i];
            data.invalid = 0;
            data.ctime = now;
            data.utime = now;
            data.download_times = 0;
            data.view_times = 0;
        }
        let result = await db.collection(schema).insertMany(params);
        if (result.result.ok === 1) {
            responseWrapper.succ({ids: result.insertedIds});
        } else {
            responseWrapper.error('HANDLE_ERROR', '新增出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_UPDATE_DATA = Joi.object({
    id: Joi.number().required(),
    name: Joi.string().required(),
    // period: Joi.string().required(),
    // subject: Joi.string().required(),
    // press_version: Joi.string().required(),
    // book_id: Joi.number().required(),
    // book_name: Joi.string().required(),
    // parent_chapter_id: Joi.number().required(),
    // chapter_id: Joi.number().required(),
    category_id: Joi.number().required(),
    // category: Joi.string().required(),
    host: Joi.string().required(),
    url: Joi.string().required(),
    from: Joi.string().optional().default(''),
    suffix: Joi.string().required(),
    size: Joi.number().integer().required(),
});
//修改
async function update(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        // 格式校验
        let update = await JOI_UPDATE_DATA.validate(req.body);
        let now = new Date();
        const id = update.id;
        const data = await db.collection(schema).findOne({_id: id});
        if (_.isEmpty(data)) return  responseWrapper.error('PARAMETERS_ERROR', '修改数据不存在');
        update.utime = now;
        delete update.id;
        await db.collection(schema).updateOne({ _id: id }, {$set: update});
        responseWrapper.succ({id});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_UPDATE_TIMES = Joi.object({
    id: Joi.number().required(),
    action: Joi.string().required(),
});
async function updateTimes(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let {id, action} = await JOI_UPDATE_TIMES.validate(req.body);
        const actions = ['download_times', 'view_times'];
        if (!actions.includes(action)) return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
        let update = {'$inc': { [action]: 1 }};
        await db.collection(schema).updateOne({_id: id}, update);
        return responseWrapper.succ({id});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_DELETE_BY_IDS = Joi.object({
    ids: Joi.array().items(Joi.number()).min(1).required(),
});
// 批量删除
async function delByIds(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const params = await JOI_DELETE_BY_IDS.validate(req.body);
        const ds = {
            invalid: 1,
            utime: new Date()
        }
        await db.collection(schema).updateMany({_id: {$in: params.ids}}, {$set: ds});
        return responseWrapper.succ({ids: params.ids});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

