var validator = require('./validator.js');
var tighten_tree = require('./v2/utils.js').tighten_tree;
var _ = require('underscore');
let params = {
    /*
     * // 参数描述
     * 'param_name': {
     * 	'type': 'int', 	 	 // 参数类型
     * 	'default': null, 	 // 默认值为空
     * 	'ranges': [1, 2, 5], // 取值范围描述，有效值为1,2,5
     * 	'post_process': function (p) {return 2*p;}, 	 // 后置处理函数
     * },
     * 'param_name': {
     * 	'type': 'string', 	 // 参数类型
     * 	'default': null, 	 // 默认值为空
     * 	'ranges': ['初中', '高中'], // 取值范围描述，有效值为'初中'，'高中'
     * 	'post_process': function (p) {
     * 		if ('初中' == p)
     * 			return 1;
     * 		else
     * 			return 2;
     * 	}, 	 // 后置处理函数
     * },
     */
    'app_key': {
        'type': 'string',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'common_str': {
        'type': 'string',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'is_show': {
        'type': 'boolean',
        'default': false,
        'ranges': function (p) {
            return true;
        },
    },
    'attach_is_mobile': {
        'type': 'int',
        'default': 0,
        'ranges': function (p) {
            return true;
        },
    },
    'common_int': {
        'type': 'int',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'user_tag': {
        'type': 'string',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'period': {
        'type': 'string',
        'default': null,
        'ranges': function (p) {
            return true;
        },
        // 'ranges': ['小学', '初中', '高中'],
    },
    'subject': {
        'type': 'string',
        'default': null,
        'ranges': function (p) {
            return true;
        },
        // 'ranges': [
        // 	'语文', '数学', '英语',
        // 	'物理', '化学', '生物',
        // 	'政治', '历史', '地理',
        // 	'科学', '社会',
        // ],
    },
    'press_version': {
        'type': 'string',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'grade': {
        'type': 'string',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },

    'name': {
        'type': 'string',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'guide_year': {
        'type': 'string',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'guide_app_name': {
        'type': 'string',
        'default': null,
        'ranges': [
            '天天练习', '超级听写',
        ],
    },
    'guide_book_id': {
        'type': 'int',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },

    'chapter_id': {
        'type': 'int',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'guide_fields_type': {
        'type': 'string',
        'default': 'all',
        'ranges': ['all', 'questions', 'chinese_words', 'eng_words']
    },

    'book_id': {
        'type': 'int',
        'default': null,
        'ranges': validator.check_book_id,
    },
    'book_fields_type': {
        'type': 'string',
        'default': 'all',
        'ranges': ['all', 'knowledge', 'eng_book'],
    },
    'knowledge_tree_id': {
        'type': 'int',
        'default': null,
        'ranges': validator.check_knowledge_tree_id,
    },
    'knowledge_id': {
        'type': 'int',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'knowledge_ids': {
        'type': 'string',
        'default': null,
        'ranges': validator.check_ids,
        'post_process': validator.split_ids,
    },
    'question_id': {
        'type': 'int',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'category_id': {
        'type': 'int',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'exampaper_id': {
        'type': 'int',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'video_id': {
        'type': 'int',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'ebook_id': {
        'type': 'int',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'difficulty': {
        'type': 'string',
        'default': null,
        'ranges': validator.check_diff,
        'post_process': validator.diff_post,
    },
    'type': {
        'type': 'string',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'limit': {
        'type': 'int',
        'default': 10,
        'ranges': function (p) {
            return (p >= 0) ? true : false;
        },
        'post_process': function (p) {
            if (p > 200) {
                return 200;
            } else {
                return p;
            }
        },
    },
    'offset': {
        'type': 'int',
        'default': 0,
        'ranges': function (p) {
            return (p >= 0) ? true : false;
        },
    },
    'ques_fields_type': {
        'type': 'string',
        'default': 'full',
        'ranges': ['stem', 'solution', 'full', 'answer', 'full_dmp'],
    },
    'ques_cat': {
        'type': 'string',
        'default': '全部',
        'ranges': ['全部', '教研精品题', '非教研精品题',
            '名校精品题', '常考题', '压轴题'],
        'post_process': function (p) {
            var mapping = {
                '全部': 'all',
                '教研精品题': 'elite',
                '非教研精品题': 'unelite',
                '名校精品题': 'sch_elite',
                '常考题': 'common',
                '压轴题': 'final',
            };
            return mapping[p];
        },
    },
    'kn_q_sort_by': {
        'type': 'string',
        'default': 'year',
        'ranges': ['integrated', 'year', 'cite_num', 'test_num', 'refer_times', 'use_times', 'quality'],
        'post_process': validator.kn_q_sort_by_post,
    },
    'question_sort_by': {
        'type': 'string',
        'default': 'year',
        'ranges': ['integrated', 'year', 'cite_num', 'test_num', 'refer_times', 'use_times'],
        'post_process': validator.question_sort_by_post,
    },
    'question_ids': {
        'type': 'string',
        'default': null,
        'ranges': validator.check_ids,
        'post_process': validator.split_ids,
    },
    'question_ids_arr': {
        'type': 'array',
        'default': null,
        'ranges': validator.check_ids,
        'post_process': validator.split_ids,
    },
    'ids_arr': {
        'type': 'array',
        'default': null,
        'ranges': validator.check_ids,
        'post_process': validator.split_ids,
    },
    'landmark_point_ids': {
        'type': 'string',
        'default': null,
        'ranges': validator.check_ids,
        'post_process': validator.split_ids,
    },
    'device': {
        'type': 'string',
        'default': 'pc',
        'ranges': ['pc', 'mobile', 'desktop'],
    },
    'year': {
        'type': 'int',
        'default': null,
        'ranges': function (p) {
            return (p >= 1950 & p <= 2050) ? true : false;
        },
    },
    'exam_type': {
        'type': 'string',
        'default': null,
        'ranges': function () {
            return true;
        }
    },
    'is_elite': {
        'type': 'int',
        'default': null,
        'ranges': [0, 1],
    },
    'paper_sort_by': {
        'type': 'string',
        'default': 'fyear_des',
        'ranges': ['vt_asc', 'vt_des', 'dt_asc', 'dt_des', 'fyear_des', 'ctime', 'ctime_des'],
        'post_process': function (p) {
            var arr = p.split('_');
            var col = arr[0];
            var cending = arr[1];
            if ('vt' == col) {
                col = 'view_times';
            } else if ('dt' == col) {
                col = 'download_times';
            } else if ('fyear' == col) {
                col = 'from_year';
            }
            if (cending == 'asc') {
                cending = 1;
            } else {
                cending = -1;
            }
            var sort_by = {};
            sort_by[col] = cending;
            if (p === 'fyear_des') {
                sort_by['ctime'] = -1;
            }
            return sort_by;
        },
    },
    'ebook_sort_by': {
        'type': 'string',
        'default': 'view_times',
        'ranges': ['view_times', 'download_times'],
        'post_process': function (p) {
            var sort_by = {};
            if ('view_times' == p) {
                sort_by['view_times'] = -1;
            }
            if ('download_times' == p) {
                sort_by['download_times'] = -1;
            }
            return sort_by;
        },
    },
    'resource_type': {
        'type': 'string',
        'default': null,
        'ranges': ['book', 'knowledge_tree', 'knowledge', 'exampaper', 'question'],
    },
    'level': {
        'type': 'string',
        'default': 'country',
        'ranges': ['province', 'city', 'country'],
        'post_process': validator.level_post,
    },
    'video_source': {
        'type': 'string',
        'default': null,
        'ranges': ['yangcong', 'suoluo', 'youku'],
    },
    'book_fields': {
        'type': 'object',
        'default': null,
        'ranges': function (fields) {
            var keys = ['chapters', 'name'];
            // 验证包含的字段名是否合法
            for (var k in fields) {
                if (keys.indexOf(k) < 0) {
                    return false;
                }
            }
            return true;
        },
        post_process: function (fields) {
            var keys = {
                'chapter': 'chapters',
                'knowledge': 'knowledges',
            };
            tighten_tree({ 'children': fields['chapters'] }, keys);
            return fields;
        },
    },
    'kn_tree_fields': {
        'type': 'object',
        'default': null,
        'ranges': function (fields) {
            var keys = ['knowledge_tree'];
            // 验证包含的字段名是否合法
            for (var k in fields) {
                if (keys.indexOf(k) < 0) {
                    return false;
                }
            }
            return true;
        }
    },
    'kn_fields': {
        'type': 'object',
        'default': null,
        'ranges': function (fields) {
            var keys = ['contents', 'name', 'card_contents', 'period', 'subject', 'videos',
                'dimensions', 'cognitions', 'make_crises', 'human_importance', 'card_contents_b', 'dimensions_b', 'cognitions_b', 'make_crises_b', 'freq_ques_type', 'knowledge_summary'];
            delete fields.id;
            // 验证包含的字段名是否合法
            for (var k in fields) {
                if (keys.indexOf(k) < 0) {
                    delete fields[k];
                    // return false;
                }
            }
            // 验证contents格式是否合法
            var conts = fields['contents'];
            var keys = ['content', 'name'];
            for (var i in conts) {
                var cont = conts[i];
                for (var k in cont) {
                    if (keys.indexOf(k) < 0) {
                        delete cont[k];
                    }
                }
            }

            // 验证contents格式是否合法
            var cardContents = fields['card_contents'];
            var keys = ['basic', 'learning', 'pitfall', 'exam_strategy', 'advance', 'excelent'];
            for (var i in cardContents) {
                if (keys.indexOf(i) < 0) {
                    delete cardContents[i];
                }
            }

            // 验证contents_b格式是否合法
            var card_contents_b = fields['card_contents_b'];
            var keys = ['basic', 'learning', 'pitfall', 'exam_strategy', 'advance', 'excelent'];
            for (var i in card_contents_b) {
                if (keys.indexOf(i) < 0) {
                    delete card_contents_b[i];
                }
            }

            if (fields.hasOwnProperty('name')) {
                if (typeof fields['name'] !== 'string') {
                    return false;
                }
                fields.name = fields.name.trim();
            }
            return true;
        },
    },
    'insert_kn_fields': {
        'type': 'object',
        'default': null,
        'ranges': function (fields) {
            var keys = ['contents', 'subject', 'period', 'name', 'card_contents'];
            // 验证包含的字段名是否合法
            for (var k in fields) {
                if (keys.indexOf(k) < 0 && k != 'id') {
                    delete fields[k];
                    // throw ('不能包含' + k);
                }
            }

            // 验证contents格式是否合法
            var conts = fields['contents'];
            var keys = ['content', 'name'];
            for (var i in conts) {
                var cont = conts[i];
                for (var k in cont) {
                    if (keys.indexOf(k) < 0) {
                        delete cont[k];
                    }
                }
            }

            // 验证contents格式是否合法
            var cardContents = fields['card_contents'];
            var keys = ['basic', 'learning', 'pitfall', 'exam_strategy', 'advance', 'excelent'];
            for (var i in cardContents) {
                if (keys.indexOf(i) < 0) {
                    delete cardContents[i];
                }
            }
            return true;
        },
        'post_process': function (fields) {
            fields['rate'] = 0;
            fields['chance'] = 0;
            fields['importance'] = 0;
            fields['score'] = 0;
            fields['card_name'] = '';
            fields['has_modified'] = 1;
            fields['from_internet'] = false;
            return fields;
        },
    },
    'ques_fields': {
        'type': 'object',
        'default': null,
        'ranges': function (fields) {
            var keys = ['blocks', 'comment', 'description', 'knowledges',
                'period', 'subject', 'type', 'difficulty', 'use_type',
                'core_knowledges', 'elite', 'reco_questions', 'grade',
                'tags', 'type_tags', 'elements', 'attach', 'dbs'];
            delete fields.id;
            // 验证包含的字段名是否合法
            for (let k in fields) {
                if (fields.hasOwnProperty(k) && keys.indexOf(k) < 0) {
                    delete fields[k];
                }
            }

            // 验证blocks格式是否合法
            var blks = fields['blocks'];

            //blocks 兼容 'knowledges', 'core_knowledges'
            let compatible = ['knowledges', 'core_knowledges'];
            for (let ckey of compatible) {
                if (!blks[ckey]) {
                    blks[ckey] = _.map(blks.stems, () => fields[ckey] || []);
                } else {
                    if (!Array.isArray(blks[ckey])) {
                        throw 'blocks.' + ckey + '应该是个数组';
                        //blks[ckey] = _.map(blks.stems, () => []);
                    }
                }
                if (blks[ckey].length !== blks.stems.length) {
                    let kArr = [];
                    for (let i = 0; i < blks.stems.length; i++) {
                        let ks = blks[ckey][i] || fields[ckey] || [];
                        kArr.push(ks);
                    }
                    blks[ckey] = kArr;
                }
            }

            var keys = ['stems', 'answers', 'explanations', 'solutions', 'types', 'knowledges', 'core_knowledges', 'elements'];
            for (let k in blks) {
                if (blks.hasOwnProperty(k)) {
                    if (keys.indexOf(k) < 0) {
                        delete blks[k];
                        continue;
                    }
                    if (blks[k] == null) {
                        throw (' blocks.' + k + '不能为空');
                    }
                    if ('elements' === k) { // 可选的，如果传了必须是数组
                        if (!Array.isArray(blks[k])) {
                            throw (' blocks.' + k + '应该是数组');
                        }
                        continue;
                    }
                    if ((blks[k] instanceof Array) && blks[k].length == 0) {
                        throw (' blocks.' + k + '不能为空');
                    }
                }
            }

            //选择题：‘’，多选题：[''],填空题：[''],解答题：''
            for (let i = 0; i < blks.types.length; i++) {
                let t_type = blks.types[i];
                let ans = blks.answers[i] || '';
                if (['选择题', '解答题'].indexOf(t_type) !== -1) {
                    if (Array.isArray(ans)) {
                        blks.answers[i] = ans.join(' ');
                    }
                }
                if (['多选题', '填空题'].indexOf(t_type) !== -1) {
                    if (typeof (ans) == 'string') {
                        blks.answers[i] = [ans];
                    }
                }
            }
            if (fields.hasOwnProperty('comment') && (typeof fields['comment'] !== 'string')) {
                throw ('comment 字段有误');
            }
            if (fields.hasOwnProperty('description') && (typeof fields['description'] !== 'string')) {
                throw ('description 字段有误');
            }

            let knowledgeKeys = ['id', 'name', 'chance', 'score', 'know_methods', 'targets'];

            //把小题的知识点内容拼装到大题知识点
            //全部知识点和核心知识点的缓存
            let _buf = { k_buf: {}, kc_buf: {} };
            let knowledge_keys = { knowledges: 'k_buf', core_knowledges: 'kc_buf' };
            for (let key in knowledge_keys) {
                if (knowledge_keys.hasOwnProperty(key)) {
                    for (let ks of blks[key]) {
                        for (let k of ks) {
                            let t_key = knowledge_keys[key];
                            if (!_buf[t_key][k.id]) {
                                _buf[t_key][k.id] = k;
                            }
                        }
                    }
                }
            }
            fields['knowledges'] = _.values(_buf.k_buf);
            fields['core_knowledges'] = _.values(_buf.kc_buf);

            var knowledges = fields['knowledges'];
            if (knowledges && !(knowledges instanceof Array)) {
                throw ('knowledges 字段有误');
            }
            let isKnowLegal = true;
            knowledges.forEach(function (knowledge) {
                knowledge.chance = knowledge.chance * 1 || 0;
                knowledge.score = knowledge.score * 1 || 0;
                if (typeof knowledge['chance'] !== 'number') {
                    isKnowLegal = false;
                }
                if (typeof knowledge['score'] !== 'number') {
                    isKnowLegal = false;
                }
                if (Array.isArray(knowledge.know_methods) && knowledge.know_methods.length > 0) {
                    knowledge.know_methods = knowledge.know_methods.map(function (item) {
                        return { id: item.id, name: item.name, coefficient: item.coefficient };
                    });
                }
                if (Array.isArray(knowledge.targets) && knowledge.targets.length > 0) {
                    knowledge.targets = knowledge.targets.map(function (item) {
                        return { id: item.id, name: item.name, coefficient: item.coefficient };
                    });
                }
                for (let k in knowledge) {
                    if (knowledgeKeys.indexOf(k) === -1) {
                        delete knowledge[k];
                    }
                }
            });
            if (!isKnowLegal) {
                throw ('knowledges 字段里key有误');
            }

            if (fields.hasOwnProperty('period') && (typeof fields['period'] !== 'string')) {
                throw ('period 字段有误');
            }
            if (fields.hasOwnProperty('subject') && (typeof fields['subject'] !== 'string')) {
                throw ('subject 字段有误');
            }
            if (fields.hasOwnProperty('type') && (typeof fields['type'] !== 'string')) {
                throw ('type 字段有误');
            }

            if (fields.hasOwnProperty('difficulty') && (typeof fields['difficulty'] !== 'number')) {
                throw ('difficulty 字段有误');
            }

            if (fields.hasOwnProperty('use_type') && (typeof fields['use_type'] !== 'string')) {
                throw ('use_type 字段有误');
            }
            // 验证core_knowledges是否合法
            let kns = fields['core_knowledges'];
            var keys = ['id', 'name'];
            for (let i in kns) {
                if (kns.hasOwnProperty(i)) {
                    let kn = kns[i];
                    kn.chance = kn.chance * 1 || 0;
                    kn.score = kn.score * 1 || 0;
                    //必填字段
                    for (let mk of keys) {
                        if (!kn.hasOwnProperty(mk)) {
                            throw ('core_knowledges.' + mk + '必填字段');
                        }
                    }
                    for (let k in kn) {
                        //去掉多余字段
                        if (knowledgeKeys.indexOf(k) === -1) {
                            delete kn[k];
                        }
                    }
                }
            }

            if (fields.hasOwnProperty('elite') && (typeof fields['elite'] !== 'number')) {
                throw ('elite 字段有误');
            }

            if (fields.hasOwnProperty('tags') && (typeof fields['tags'] !== 'object')) {
                throw ('tags 字段有误');
            }
            if (fields.hasOwnProperty('tags') && (typeof fields['tags'] === 'object')) {
                if (fields['tags']['count_sub_ques'] && (typeof fields['tags']['count_sub_ques'] !== 'number')) {
                    throw ('tags.count_sub_ques 字段有误');
                }
                if (fields['tags']['count_options'] && !Array.isArray(fields['tags']['count_options'])) {
                    throw ('tags.count_options 字段有误');
                }
                if (fields['tags']['grades'] && !Array.isArray(fields['tags']['grades'])) {
                    throw ('tags.grades 字段有误');
                }
            }

            if (fields.hasOwnProperty('type_tags') && !Array.isArray(fields['type_tags'])) {
                throw ('type_tags 字段有误');
            }
            if (fields.hasOwnProperty('type_tags') && Array.isArray(fields['type_tags'])) {
                for (let tag of fields['type_tags']) {
                    if (!tag.id) {
                        throw ('type_tags.id 字段有误');
                    }
                    if (!tag.name) {
                        throw ('type_tags.name 字段有误');
                    }
                    if (!Array.isArray(tag.values)) {
                        throw ('type_tags.values 字段有误');
                    }
                    for (let value of tag.values) {
                        if (!value.name) {
                            throw ('type_tags.values.name 字段有误');
                        }
                        if (value.sub_values && !Array.isArray(value.sub_values)) {
                            throw ('type_tags.values.sub_values 字段有误');
                        }
                    }
                }
            }

            if (fields.hasOwnProperty('elements') && !Array.isArray(fields['elements'])) {
                throw ('elements 字段有误');
            }

            if (fields.hasOwnProperty('dbs') && (!Array.isArray(fields['dbs']) || fields['dbs'].length === 0)) {
                throw ('dbs 字段有误');
            }
            return true;
        },
    },
    'resource_comment': {
        'type': 'object',
        'default': null,
        'ranges': function (p) {
            if (JSON.stringify(p).length < 500) {
                return true;
            } else {
                throw (' 长度超限');
            }
        },
    },
    'set_mode': {
        'type': 'object',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'erratum_status': {
        'type': 'string',
        'default': null,
        'ranges': ['reviewed', 'rejected'],
    },
    'school_books_put': {
        'type': 'object',
        'default': null,
        'ranges': function (fields) {
            return true;
        }
    },
    'region_book_add': {
        'type': 'object',
        'default': null,
        'ranges': function (p) {
            return true;
        },
    },
    'video_category_add': {
        'type': 'object',
        'default': null,
        'ranges': function (fields) {
            //参数字段
            var keys = [
                'period',
                'subject',
                'type',
                'description',
                'teachers',
                'profile_url',
                'from',
                'author_name',
                'videos',
                'acl',
                'tags'];
            // 验证包含的字段名是否合法
            for (var k in fields) {
                if (keys.indexOf(k) < 0 && k != 'id') {
                    throw ('不能包含' + k);
                }
            }
            for (var i in keys) {
                var k = keys[i];
                if (!fields.hasOwnProperty(k)) {
                    throw ('未包含' + k);
                }
            }

            let videos = fields.videos;
            let video_keys = ['name', 'id'];
            for (let video of videos) {
                if (typeof (video) != 'object') {
                    throw new Error('视频专辑的数组元素必须是对象');
                }
                for (let item in video) {
                    if (video_keys.indexOf(item) == -1) {
                        delete video[item];
                    }
                }
            }

            return true;
        }
    }
    ,
    'video_category_update': {
        'type': 'object',
        'default': null,
        'ranges': function (fields) {
            //参数字段
            var keys = [
                'author_name',
                'period',
                'subject',
                'type',
                'description',
                'teachers',
                'profile_url',
                'from',
                'videos',
                'acl',
                'tags'];
            // 验证包含的字段名是否合法
            delete fields.id;
            for (var k in fields) {
                if (keys.indexOf(k) < 0) {
                    throw ('不能包含' + k);
                }
            }
            let video_keys = ['name', 'id'];
            //验证videos结构
            if (fields.videos) {
                if (!Array.isArray(fields.videos)) {
                    throw 'videos必须是数组类型';
                }
                for (let item of fields.videos) {
                    if (!item) {
                        throw 'videos数据是空';
                    }
                    if (typeof (item) != 'object') {
                        throw 'videos数组元素必须是对象';
                    }
                    if (!item.name || !item.id * 1) {
                        throw 'videos数组对象必须包含name，id必须是数字';
                    }
                    for (let key in item) {
                        if (video_keys.indexOf(key) == -1) {
                            delete item[key];
                        }
                    }
                    item.id = item.id * 1;
                }
            }
            return true;
        }
    },
    'add_video_category_videos_teachers': {
        'type': 'array',
        'default': null,
        'ranges': function (fields) {
            //参数字段
            var keys = [
                'name', 'description'];
            // 验证包含的字段名是否合法
            for (var k in fields) {
                for (let j in fields[k]) {
                    if (keys.indexOf(j) < 0) {
                        throw ('不能包含' + j);
                    }
                }
            }
            return true;
        }
    },
    'add_video_category_videos_knowledges': {
        'type': 'array',
        'default': null,
        'ranges': function (fields) {
            //参数字段
            var keys = [
                'id', 'name'];
            // 验证包含的字段名是否合法
            for (var k in fields) {
                for (let j in fields[k]) {
                    if (keys.indexOf(j) < 0) {
                        throw ('不能包含' + j);
                    }
                }
            }
            return true;
        }
    },
    'add_video_category_videos_duration': {
        'type': 'object',
        'default': null,
        'ranges': function (fields) {
            //参数字段
            let int_reg = /^(0|[1-9][0-9]*|-[1-9][0-9]*)$/;
            if (!int_reg.test(fields)) {
                throw (' 不是整数！');
            }
            return true;
        }
    },
    'common_array': {
        'type': 'object',
        'default': null,
        'ranges': function (fields) {
            return true;
        }
    },
    'bool_string': {
        'type': 'string',
        'default': null,
        'ranges': function (fields) {
            return true;
        },
        'post_process': function (p) {
            return 'true' === p ? true : false;
        }
    },
    'tag_ids': {
        'type': 'array',
        'default': null,
        'ranges': function (fields) {
            return true;
        }
    },
    'sort_by': {
        'type': 'string',
        'default': 'year',
        'ranges': ['integrated', 'year_utime', 'year', 'cite_num', 'test_num', 'refer_times', 'use_times', 'view_times', 'ctime', 'dmp_cite_asc', 'dmp_cite_desc']
    },
    'tags': {
        'type': 'array',
        'default': 'null',
        'ranges': function (fields) {
            return true;
        }
    },
    'dbs': {
        'type': 'string',
        'default': '',
        'ranges': function (fields) {
            return true;
        }
    },
};

let kb_api = {
    'v2': {
        // http://kboe.yunxiao.com/kb_api/v2/api_key/
        'api_key': {
            // params desc
            'app_key': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['app_key'],
            },
            'user_tag': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['user_tag'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/books/
        'books': {
            'cache_key': 'kb_api:v2:books_pkg:{period}:{subject}',
            // params desc
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
            'device': {
                'position': 'query',
                'require': false,
                'detail': params['device']
            },
            'dbs': {
                'position': 'query',
                'require': false,
                'detail': params['dbs']
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/books/{book_id}/
        'book': {
            'cache_key': 'kb_api:v2:books:{book_id}:{type}',
            // params desc
            'book_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['book_id'],
            },
            'type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['type'],
            },
            'fields_type': {
                'position': 'query',
                'required': false,
                'detail': params['book_fields_type'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs']
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/books/{book_id}/
        'modify_book': {
            'cache_key': 'kb_api:v2:books:{book_id}',
            // params desc
            'book_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['book_id'],
            },
            'fields': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['book_fields'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/knowledge_trees/
        'knowledge_trees': {
            'cache_key': 'kb_api:v2:knowledge_trees_pkg:{period}:{subject}',
            // params desc
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs']
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/knowledge_trees/{knowledge_tree_id}/
        'knowledge_tree': {
            'cache_key': 'kb_api:v2:knowledge_trees:{knowledge_tree_id}',
            // params desc
            'knowledge_tree_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['knowledge_tree_id'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs']
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/knowledge_nodes/knowledges/
        'knowledge_tree_by_name': {
            'cache_key': 'kb_api:v2:knowledge_trees:{period}:{subject}:{name}',
            // params desc
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['subject'],
            },
            'name': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs']
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/knowledges/{knowledge_id}/
        'knowledge': {
            'cache_key': 'kb_api:v2:knowledges:{knowledge_id}',
            // params desc
            'knowledge_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['knowledge_id'],
            },
            'fields_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': {
                    'type': 'string',
                    'default': 'common',
                    'ranges': ['common', 'statistics', 'full', 'hfsfd_courseware', 'fudao_app'],
                    'post_process': validator.kn_fields_type_post,
                },
            },
            'video_source': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['video_source'],
            },
            'device': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs']
            }
        },
        'batch_get_knowledge': {
            // params desc
            'ids': {
                'position': 'query',			// 知识点id，多个通过逗号分开
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'fields_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': {
                    'type': 'string',
                    'default': 'common',
                    'ranges': ['common', 'statistics', 'full', 'hfsfd_courseware', 'fudao_app'],
                    'post_process': validator.kn_fields_type_post,
                },
            },
            'video_source': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['video_source'],
            },
            'device': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs']
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/knowledges/score_chance/
        'knowledge_score_chance': {
            'cache_key': 'kb_api:v2:knowledges:score_chance',
        },
        // http://kboe.yunxiao.com/kb_api/v2/knowledges/{knowledge_id}/
        'modify_knowledge': {
            // params desc
            'knowledge_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['knowledge_id'],
            },
            'fields': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['kn_fields'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/knowledges/
        'insert_knowledge': {
            // params desc
            'fields': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['insert_kn_fields'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/knowledges/{knowledge_ids}/questions/
        'knowledge_questions': {
            'cache_key': 'kb_api:v2:knowledges:{knowledge_ids}:questions:new:{dbs}',
            // params desc
            'knowledge_ids': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['knowledge_ids'],
            },
            'category': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['ques_cat'],
            },
            'difficulty': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['difficulty'],
            },
            'type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['type'],
            },
            'device': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
            'year': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['year'],
            },
            'exam_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['exam_type'],
            },
            'province': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'city': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'associate_kid': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'sort_by': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['kn_q_sort_by'],
            },
            'limit': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'grade': {
                'position': 'query',
                'required': false,
                'detail': params['common_str'],
            },
            'mult_know_use': {					//针对多知识点情况，判断是合并查询还是交集查询
                'position': 'query',
                'required': false,
                'detail': params['common_str'],
            },
            'elite': {							//试题的elite字段
                'position': 'query',
                'required': false,
                'detail': params['common_str'],
            },
            'serv_range': {							//试题的serv_range字段
                'position': 'query',
                'required': false,
                'detail': params['common_str'],
            },
            'mobile_ready': {							//mobile_ready
                'position': 'query',
                'required': false,
                'detail': params['bool_string'],
            },
            'total': {							// 全量试题
                'position': 'query',
                'required': false,
                'detail': params['bool_string'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs']
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/questions/by_search/
        'question_by_search': {
            // params desc
            'period': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['subject'],
            },
            'knowledges': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'know_methods': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'targets': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'stem_statis': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'type': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['type'],
            },
            'count_options': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'count_sub_ques': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'filter_mkp': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['bool_string'],
            },
            'tag_ids': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['tag_ids'],
            },
            'difficulty': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'category': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'exam_type': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['exam_type'],
            },
            'attach': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'grade': { // 年级，可选,  英语学科专用，分上下册 （对应/se_kb/v2/filter/questions接口grades字段）
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'grades': {  // 目前命题平台用，支持多选用","分隔，不分上下册 （对应/se_kb/v2/filter/questions接口grade字段）
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'province': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'city': {
                'position': 'body',			   // 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'provinces': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'elite': {
                'position': 'body',			    // 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'inspect': {
                'position': 'body',			    // 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'year': {
                'position': 'body',			    // 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'from': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'sort_by': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['sort_by'],
            },
            'limit': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'set_mode': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['set_mode'],
            },
            'landmark_point_ids': { // 地标点
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['landmark_point_ids'],
            },
            'dmp_example': { // dmp例题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'dmp_exercise': {  // dmp习题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'dmp_homework': { // dmp作业
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'research_example': { // 教研课件例题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'research_exercise': { // 教研课件习题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'teach_example': {  // 教学课件例题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'teach_exercise': { // 教学课件习题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'teach_homework': { // 教学作业题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'teach_evaluation': { // 教学测评题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'teach_customize': { // 教学自定义考试题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'attach_is_mobile': { // 筛选移动化试题
                'position': 'body',             // 从url/参数列表/body获取
                'required': false,              // 是否为必输项
                'detail': params['attach_is_mobile'],
            },
            'attach_is_landmark_question': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'dbs': {
                'position': 'body',
                'required': false,
                'detail': params['dbs']
            },
            'multi_or': {
                'position': 'body',
                'required': false,
                'detail': {
                    'type': 'array'
                },
            }
        },
        'question_by_search_v2': {
            // params desc
            'period': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['subject'],
            },
            'knowledges': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'knows': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'know_methods': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'targets': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'stem_statis': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'type': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['type'],
            },
            'count_options': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'count_sub_ques': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'filter_mkp': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['bool_string'],
            },
            'tag_ids': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['tag_ids'],
            },
            'difficulty': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'category': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'exam_type': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['exam_type'],
            },
            'exam_cate': { // 试卷类型
                'position': 'body',
                'required': false,
                'detail': params['common_array'],
            },
            'attach': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'grade': { // 年级，可选,  英语学科专用，分上下册 （对应/se_kb/v2/filter/questions接口grades字段）
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'grades': {  // 目前命题平台用，支持多选用","分隔，不分上下册 （对应/se_kb/v2/filter/questions接口grade字段）
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'province': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'city': {
                'position': 'body',			   // 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'provinces': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'elite': {
                'position': 'body',			    // 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'inspect': {
                'position': 'body',			    // 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'year': {
                'position': 'body',			    // 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'from': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'sort_by': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['sort_by'],
            },
            'limit': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'set_mode': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['set_mode'],
            },
            'landmark_point_ids': { // 地标点
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['landmark_point_ids'],
            },
            'dmp_example': { // dmp例题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'dmp_exercise': {  // dmp习题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'dmp_homework': { // dmp作业
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'research_example': { // 教研课件例题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'research_exercise': { // 教研课件习题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'teach_example': {  // 教学课件例题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'teach_exercise': { // 教学课件习题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'teach_homework': { // 教学作业题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'teach_evaluation': { // 教学测评题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'teach_customize': { // 教学自定义考试题
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'attach_is_mobile': { // 筛选移动化试题
                'position': 'body',             // 从url/参数列表/body获取
                'required': false,              // 是否为必输项
                'detail': params['attach_is_mobile'],
            },
            'attach_is_landmark_question': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'dbs': {
                'position': 'body',
                'required': false,
                'detail': params['dbs']
            },
            'multi_or': {
                'position': 'body',
                'required': false,
                'detail': {
                    'type': 'array'
                },
            },
            'query': { // 搜索关键字
                'position': 'body',
                'required': false,
                'detail': params['common_str'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/knowledges/{knowledge_ids}/questions3/
        'knowledge_questions3': {
            'cache_key': 'kb_api:v2:knowledges:{knowledge_ids}:questions3:new',
            // params desc
            'knowledge_ids': {
                'position': 'params',			// 从url/参数列表获取
                'required': true, 				// 是否为必输项
                'detail': params['knowledge_ids'],
            },
            'category': {
                'position': 'body',			   // 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'difficulty': {
                'position': 'body',			    // 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['difficulty'],
            },
            'type': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['type'],
            },
            'device': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
            'year': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['year'],
            },
            'exam_type': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['exam_type'],
            },
            'province': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'city': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'associate_kid': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'limit': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'grade': {
                'position': 'body',
                'required': false,
                'detail': params['common_str'],
            },
            'mult_know_use': {					//针对多知识点情况，判断是合并查询还是交集查询
                'position': 'body',
                'required': false,
                'detail': params['common_str'],
            },
            'elite': {							//试题的elite字段
                'position': 'body',
                'required': false,
                'detail': params['common_str'],
            },
            'mobile_ready': {							//mobile_ready
                'position': 'body',
                'required': false,
                'detail': params['bool_string'],
            },
            'total': {							// 全量试题
                'position': 'body',
                'required': false,
                'detail': params['bool_string'],
            },
            // kbpv2.10.5新加字段
            'tag_ids': {
                'position': 'body',
                'required': false,
                'detail': params['tag_ids'],
            },
            'count_sub_ques': {
                'position': 'body',
                'required': false,
                'detail': params['common_int'],
            },
            'count_options': {
                'position': 'body',
                'required': false,
                'detail': params['common_int'],
            },
            'fields_type': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['ques_fields_type'],
            },
        },


        // http://kboe.yunxiao.com/kb_api/v2/questions/{question_id}/
        'question': {
            'cache_key': 'kb_api:v2:questions:{question_id}',
            // params desc
            'question_id': {
                'position': 'params',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['question_id'],
            },
            'fields_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['ques_fields_type'],
            },
            'device': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs'],
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/questions/{question_id}/
        'modify_question': {
            // params desc
            'question_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['question_id'],
            },
            'fields': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['ques_fields'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/questions/
        'get_questions': {
            'cache_key': 'kb_api:v2:questions:{question_id}',
            // params desc
            'question_ids': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['question_ids'],
            },
            'fields_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['ques_fields_type'],
            },
            'device': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs'],
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/questions/
        'post_questions': {
            'cache_key': 'kb_api:v2:questions:{question_id}',
            // params desc
            'question_ids': {
                'position': 'body',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['question_ids_arr'],
            },
            'fields_type': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['ques_fields_type'],
            },
            'device': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
            'dbs': {
                'position': 'body',
                'required': false,
                'detail': params['dbs'],
            }
        },
        'post_statistics': {
            'ids': {
                'position': 'body',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['ids_arr'],
            },
            'dbs': {
                'position': 'body',
                'required': false,
                'detail': params['dbs'],
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/exampapers/categorys/
        'exampaper_cats': {
            'cache_key': 'kb_api:v2:exampapers:categorys:{period}:{subject}:{dbs}',
            // params desc
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs'],
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/exampapers/categorys/{category_id}/
        'exampaper_category': {
            'cache_key': 'kb_api:v2:exampapers:categorys:{category_id}',
            // params desc
            'category_id': {
                'position': 'params',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['category_id'],
            },
            'from': {
                'position': 'query',
                'required': false,
                'detail': {
                    'type': 'string',
                    'default': 'kb',
                    'ranges': ['kb', 'hfs'],
                }
            },
            'province': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'city': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'provinces': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'year': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['year'],
            },
            'is_elite': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['is_elite'],
            },
            'offset': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'limit': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'sort_by': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['paper_sort_by'],
            },
            'exam_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['exam_type'],
            },
            'source_platform': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false,
                'detail': {
                    'type': 'string',
                    'default': '',
                }
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs'],
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/exampapers/{exampaper_id}/
        'exampaper': {
            'cache_key': 'kb_api:v2:exampapers:{exampaper_id}',
            // params desc
            'exampaper_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['exampaper_id'],
            },
            'from': {
                'position': 'query',
                'required': false,
                'detail': {
                    'type': 'string',
                    'default': 'kb',
                    'ranges': ['kb', 'hfs'],
                }
            },
            'fields_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': {
                    'type': 'string',
                    'default': 'common',
                    'ranges': ['common', 'full'],
                    'post_process': validator.paper_fields_type_post,
                },
            },
            'ques_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['ques_fields_type'],
            },
            'device': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
            'dbs': {
                'position': 'query',
                'required': false,
                'detail': params['dbs'],
            }
        },
        'exampaperNoView': {
            'cache_key': 'kb_api:v2:exampapers:{exampaper_id}',
            // params desc
            'exampaper_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['exampaper_id'],
            },
            'fields_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': {
                    'type': 'string',
                    'default': 'common',
                    'ranges': ['common', 'full'],
                    'post_process': validator.paper_fields_type_post,
                },
            },
            'ques_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['ques_fields_type'],
            },
            'device': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/videos/categorys/
        'video_cats': {
            'cache_key': 'kb_api:v2:videos:categorys:{period}:{subject}',
            // params desc
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/videos/categorys/{category_id}/
        'video_category': {
            'cache_key': 'kb_api:v2:videos:categorys:{category_id}',
            // params desc
            'category_id': {
                'position': 'params',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['category_id'],
            },
            'offset': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'limit': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'dbs': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['dbs'],
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/videos/{video_id}/
        'video': {
            'cache_key': 'kb_api:v2:videos:{video_id}',
            // params desc
            'video_id': {
                'position': 'params',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['video_id'],
            },
            'dbs': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['dbs'],
            }
        },
        //获取视频专辑列表
        'get_video_categories': {
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
            'offset': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'limit': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
        },
        //添加视频专辑
        'add_video_category': {
            'category': {
                'position': 'body',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['video_category_add'],
            },
        },
        //添加视频专辑
        'put_video_category': {
            'category_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'category': {
                'position': 'body',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['video_category_update'],
            },
        },
        //视频专辑信息
        'get_video_category': {
            'category_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
        },
        //删除视频专辑
        'delete_video_category': {
            'category_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
        },
        //视频专辑视频列表
        'get_video_category_videos': {
            'category_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'offset': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'limit': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
        },
        //视频专辑视频列表
        'get_video_list': {
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
            'teacher': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'name': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'offset': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'limit': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
        },

        //视频专辑添加视频
        'add_video_category_videos': {
            'category_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'name': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'data_url': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'type': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'period': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'subject': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'from': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'description': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'duration': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['add_video_category_videos_duration'],
            },
            'teachers': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['add_video_category_videos_teachers'],
            },
            'recorder': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'label': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'knowledges': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['add_video_category_videos_knowledges'],
            },
            'file_name': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
        },
        //创建基础视频
        'add_video': {
            'name': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'data_url': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'type': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'period': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'subject': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': true, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'from': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'description': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'duration': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['add_video_category_videos_duration'],
            },
            'teachers': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['add_video_category_videos_teachers'],
            },
            'recorder': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'label': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'knowledges': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['add_video_category_videos_knowledges'],
            },
            'file_name': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'tags': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['tags'],
            },
            'qrcode_url': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            }
        },
        'put_video': {
            'video_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'name': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'data_url': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'type': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'period': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'subject': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'from': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'description': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'duration': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['add_video_category_videos_duration'],
            },
            'teachers': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['add_video_category_videos_teachers'],
            },
            'recorder': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'label': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'knowledges': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['add_video_category_videos_knowledges'],
            },
            'file_name': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': {
                    'type': 'string',
                    'default': ''
                }
            },
            'tags': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['tags'],
            },
            'description_files': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'qrcode_url': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'acl': {
                'position': 'body',			// 从url(params)/参数列表(query)/body
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            }
        },
        //视频专辑视频信息
        'get_video_category_video': {
            'category_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'video_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
        },
        //视频专辑 移除视频
        'delete_video_category_video': {
            'category_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'video_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
        },
        //视频专辑 添加已有视频视频
        'put_video_category_existing_video': {
            'category_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'video_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/knowledges/{knowledge_ids}/videos/
        'knowledge_videos': {
            // params desc
            'knowledge_ids': {
                'position': 'params',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['question_ids'],
            },
            'dbs': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['dbs'],
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/ebooks/
        'ebooks': {
            'cache_key': 'kb_api:v2:ebooks:{subject}',
            // params desc
            'subject': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
            'offset': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'limit': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/ebooks/{ebook_id}/
        'ebook': {
            'cache_key': 'kb_api:v2:videos:{video_id}',
            // params desc
            'ebook_id': {
                'position': 'params',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['ebook_id'],
            },
            'fields_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': {
                    'type': 'string',
                    'default': 'common',
                    'ranges': ['common', 'full'],
                    'post_process': validator.ebook_fields_type_post,
                },
            },
            'limit': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/ebooks/{ebook_id}/download/
        'ebook_download': {
            // params desc
            'ebook_id': {
                'position': 'params',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['ebook_id'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/ebooks/popularity/
        'popularity': {
            // params desc
            'subject': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
            'limit': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'sort_by': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['ebook_sort_by'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/regions/
        'regions': {
            'cache_key': 'kb_api:v2:regions:{level}',
            // params desc
            'level': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['level'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/regions/simple/
        'simple_regions': {
            'cache_key': 'kb_api:v2:regions:simple',
        },
        // http://kboe.yunxiao.com/kb_api/v2/resources/
        'resources': {
            // params desc
            'collection': {
                'position': 'body',				// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': {
                    'type': 'object',
                    'default': null,
                },
            },
            'query': {
                'position': 'body',				// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': {
                    'type': 'object',
                    'default': null,
                    'ranges': function (p) {
                        return true;
                    },
                },
            },
            'project': {
                'position': 'body',				// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': {
                    'type': 'object',
                    'default': {},
                    'ranges': function (p) {
                        return true;
                    },
                },
            },
            'limit': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/resources/ (put)
        'put_resources': {
            // params desc
            'collection': {
                'position': 'body',				// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': {
                    'type': 'object',
                    'default': null,
                },
            },
            'query': {
                'position': 'body',				// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': {
                    'type': 'object',
                    'default': null,
                    'ranges': function (p) {
                        return true;
                    },
                },
            },
            'project': {
                'position': 'body',				// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': {
                    'type': 'object',
                    'default': {},
                    'ranges': function (p) {
                        return true;
                    },
                },
            },
            'update': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': {
                    'type': 'object',
                    'default': {},
                    'ranges': function (p) {
                        return true;
                    },
                },
            },
            'options': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': {
                    'type': 'object',
                    'default': {},
                    'ranges': function (p) {
                        return true;
                    },
                },
            },
        },
        'get_resources_indexes': {
            'collection': {
                'position': 'query',				// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['common_str']
            }
        },
        // http://kboe.yunxiao.com/kb_api/v2/search/knowledges/{key_word}/
        'search_knowledges': {
            // params desc
            'key_word': {
                'position': 'params',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['type'],
            },
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
        },

        'changeAnotherQuestion': {
            'question_id': {
                'position': 'params',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['question_id'],
            },
            'fields_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['ques_fields_type'],
            },
            'device': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
            'dbs': {
                'position': 'query',			// 从url/参数列表/query获取
                'required': false, 				// 是否为必输项
                'detail': params['dbs'],
            }
        },

        'changeQuestions': {
            'question_id': {
                'position': 'params',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['question_id'],
            },
            'fields_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['ques_fields_type'],
            },
            'device': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },
            'dbs': {
                'position': 'query',			// 从url/参数列表/query获取
                'required': false, 				// 是否为必输项
                'detail': params['dbs'],
            }
        },

        'update': {
            'fields_type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': {
                    'type': 'string',
                    'default': 'all',
                    'ranges': ['all', 'question', 'exampaper'],
                },
            },
            'limit': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
        },
        'getNewHotExampaper': {
            'limit': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
            'filter_mkp': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['bool_string'],
            },
            'grade': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': '',
            }
        },

        'exampaperUpdateSubject': {
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['period'],
            },
        },
        'guideBooks': {
            // params desc
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
            'press_version': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['press_version'],
            },
            'grade': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['grade'],
            },
            'name': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['name'],
            },
            'year': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['guide_year'],
            },
            'app_name': {
                'position': 'query',
                'required': true,
                'detail': params['guide_app_name'],
            },
        },
        'guideBookStrcut': {
            'guide_book_id': {
                'position': 'params',
                'required': true,
                'detail': params['guide_book_id'],
            },
        },
        'guideChapter': {
            'chapter_id': {
                'position': 'params',
                'required': true,
                'detail': params['chapter_id'],
            },

            'device': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['device'],
            },

            'fields_type': {
                'position': 'query',
                'required': false,
                'detail': params['guide_fields_type']
            }
        },
        //区域教材资源：区域教材统计
        'get_region_book_statistics': {
            'cache_key': 'kb_api:v2:region_books:statistics:{province}:{city}:{country}',
            'province': {
                'position': 'query',
                'required': true,
                'detail': params['common_str'],
            },
            'city': {
                'position': 'query',
                'required': true,
                'detail': params['common_str'],
            },
            'country': {
                'position': 'query',
                'required': true,
                'detail': params['common_str'],
            }

        },
        //区域教材资源：区域教材数据
        'get_region_book_regions': {
            'province': {
                'position': 'query',
                'required': true,
                'detail': params['common_str'],
            },
            'city': {
                'position': 'query',
                'required': true,
                'detail': params['common_str'],
            },
            'country': {
                'position': 'query',
                'required': true,
                'detail': params['common_str'],
            }

        },
        //设置学校使用教材
        'put_region_book_regions': {
            'rb_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'periods': {
                'position': 'body',
                'required': true,
                'detail': params['region_book_add'],
            },
        },

        //学校教材资源
        //学校列表
        'get_school_books_schools': {
            'query': {
                'position': 'query',
                'required': true,
                'detail': params['common_str'],
            },
            'limit': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
        },
        //学校教材信息
        'get_school_books_school_books': {
            'sch_id': {
                'position': 'params',			// 从url/参数列表
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'cache': {
                'position': 'query',			// 从url/参数列表
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            }
        },
        //设置学校区域
        'put_school_books_region': {
            'sch_id': {                             // 学校id
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'name': {
                'position': 'body',
                'required': true,
                'detail': params['common_str'],
            },
            'province': {
                'position': 'body',
                'required': true,
                'detail': params['common_str'],
            },
            'city': {
                'position': 'body',
                'required': true,
                'detail': params['common_str'],
            },
            'country': {
                'position': 'body',
                'required': true,
                'detail': params['common_str'],
            }
        },
        //设置学校使用教材
        'put_school_books_book': {
            'sch_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'periods': {
                'position': 'body',
                'required': true,
                'detail': params['school_books_put'],
            },
        },
        // http://kboe.yunxiao.com/kb_api/v2/exampapers/by_search/
        'exampaper_by_search': {
            'name': {
                'position': 'body',
                'required': false,
                'detail': params['common_str']
            },
            'period': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['subject'],
            },
            'press_version': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'grade': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'province': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'city': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'provinces': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_array'],
            },
            'region': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'type': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['type'],
            },
            'category_id': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'filter_mkp': {
                'position': 'body',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['bool_string'],
            },
            'from': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'to_year': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'sch_name': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'album_type': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'is_show': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false,				// 是否为必输项
                'detail': params['is_show'],
            },
            'cyear': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'cyear_range': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_int'],
            },
            'sort_by': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['sort_by'],
            },
            'need_ques_num': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'limit': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'chapter_id': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            },
            'dbs': {
                'position': 'body',				// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['dbs'],
            }
        },
        'getKnowledgeByBook': {
            'cache_key': 'kb_api:v2:books:{book_id}:{type}',
            // params desc
            'book_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['book_id'],
            },
            'type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['type'],
            },
        },
        'getKnowledgesByBook': {
            'cache_key': 'kb_api:v2:books:{book_id}:{type}',
            // params desc
            'book_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['book_id'],
            },
            'type': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['type'],
            },
            'dbs': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['dbs'],
            }
        },
        'getKnowledgesByKnowledgeTree': {
            'cache_key': 'kb_api:v2:knowledge_trees:{knowledge_tree_id}',
            // params desc
            'knowledge_tree_id': {
                'position': 'params',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['knowledge_tree_id'],
            },
            'dbs': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['dbs'],
            }
        },
        'getRegionExampaper': {
            'limit': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['limit'],
            },
            'offset': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['offset'],
            },
            'period': {
                'position': 'query',			// 从url(params)/参数列表(query)/body(body)获取
                'required': true, 				// 是否为必输项
                'detail': params['period'],
            },
            'subject': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': true, 				// 是否为必输项
                'detail': params['subject'],
            },
            'filter_mkp': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['bool_string'],
            },
            'region': {
                'position': 'query',			// 从url/参数列表/body获取
                'required': false, 				// 是否为必输项
                'detail': params['common_str'],
            }
        },
    },
};

module.exports = {
    kb_api: kb_api,
};
