var ObjectID = require("mongodb").ObjectID;
const Config = require('config');
const url = require('url');
const qs = require('qs');
const axios = require('axios');
var Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
var Thenjs = require('thenjs');
var _ = require('underscore');
const Joi = require('@hapi/joi');
const mapExampaperName = require('./exampaper/mapExampaperName');
const filterExampaperName = require('./exampaper/filterExampaperName');

/*
 * Table is alias two-way-specicication
 */

/**
 * 检测双向细目表的合法性
 * @param {object} table 双向细目表的object
 * @return {boolean} true: 合法
 * @exception: false
 * @private
 */
function isValidTable(table) {

	let retval = true;

	try {
		if (typeof table.name !== 'string' || !table.name)
			throw new TypeError('缺少name字段');

		if (typeof table.period !== 'string' || !table.period)
			throw new TypeError('缺少学段信息');

		if (typeof table.subject !== 'string' || !table.subject)
			throw new TypeError('缺少学科信息');

		if (typeof table.permission !== 'string' || !table.permission)
			throw new TypeError('缺少权限信息');

		if (!Array.isArray(table.blocks))
			throw new TypeError('缺少blocks信息');

		for (var ix in table.blocks) {
			var questions = table.blocks[ix].questions;
			if (typeof table.blocks[ix].name !== 'string')
				throw new TypeError('blocks' + ix + '缺少name');

			for (var jx in questions) {
				var question = questions[jx];

				if (typeof question.type !== 'string')
					throw new TypeError('blocks' + ix + ':questions' + jx + '缺少type信息');

				if ([
					'不限',
					'容易',
					'较易',
					'中等',
					'较难',
					'困难'
				].indexOf(question.difficulty) < 0)
					throw new TypeError('blocks' + ix + ':questions' + jx + '难度信息有问题');

				if (typeof question.score !== 'number')
					throw new TypeError('blocks' + ix + ':questions' + jx + 'score有问题');

				if (typeof question.period !== 'string')
					throw new TypeError('blocks' + ix + ':questions' + jx + 'period有问题');

				if (typeof question.subject !== 'string')
					throw new TypeError('blocks' + ix + ':questions' + jx + 'subject有问题');

				for (var kx in question.knowledges) {
					var knowledge = question.knowledges[kx];
					if (typeof knowledge.id !== 'number')
						throw new TypeError('blocks' + ix + ':questions' + jx + ':knowledges:' + kx + ':id有问题');

					if (typeof knowledge.name !== 'string')
						throw new TypeError('blocks' + ix + ':questions' + jx + ':knowledges:' + kx + ':name有问题');
				}
			}
		}
	} catch (err) {
		Logger.error(err.message);
		return false;
	}
	return true;
}

/**
 * 检测双向细目表的合法性
 * @param {object} table 双向细目表的object
 * @return {object} table
 * @exception: false
 * @private
 */
function filterTable(table) {
	var retobj = {};
	if (table.name)
		retobj.name = table.name;

	if (table.period)
		retobj.period = table.period;

	if (table.subject)
		retobj.subject = table.subject;

	if (table.relevance_type)
		retobj.relevance_type = table.relevance_type;

	if (table.relevance_id)
		retobj.relevance_id = table.relevance_id;

	if (table.grade)
		retobj.grade = table.grade;

	if (table.permission)
		retobj.permission = table.permission;

	if (table.view_times)
		retobj.view_times = table.view_times;

	if (table.download_times)
		retobj.download_times = table.download_times;

	if (table.type)
		retobj.type = table.type;

	if (table.province)
		retobj.province = table.province;

	if (table.year)
		retobj.year = table.year;

	if (!!table.blocks == false)
		return retobj;

	retobj.blocks = [];
	for (var bix in retobj.blocks) {
		let bBody = {};
		let block = retobj.blocks[bix];
		bBody.name = block.name;
		bBody.questions = [];
		for (var qix in block.questions) {
			var qBody = {};
			var question = block.questions[qix];
			qBody.type = question.type;
			qBody.difficulty = question.difficulty;
			qBody.score = question.score;
			qBody.knowledges = [];
			for (var kix in question.knowledges) {
				qBody.knowledges.push({
					id: question.knowledges[kix].id,
					name: question.knowledges[kix].name
				});
			}
			bBody.questions.push(qBody);
		}
		retobj.blocks.push(bBody);
	}

	return table;
}

/**
 * Description: 创建一张双向细目标
 * Method: POST
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=15532806#id-知识库APIV2-创建一张双向细目标
 * URL: /kb_api/v2/tw_specifications/
 * Author: cuiyunfeng
 * Date: 2017-8-31
 * @public
 */
function createTable(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let body = req.body;

	if (!isValidTable(body)) {
		Logger.error(`传入的双向细目表结构不合法${JSON.stringify(body)}`);
		return responseWrapper.error('PARAMETERS_ERROR', 'PARAMETERS_ERROR');
	}

	/*
	 * Load Data
	 */

	body = filterTable(body);

	body.ctime = new Date();
	body.utime = new Date();
	body.view_times = 0;
	body.download_times = 0;

	db.collection('tw_specification').insert(body, function (err, writeResult) {
		if (err) {
			Logger.error(err.message);
			return responseWrapper.error('HANDLE_ERROR', err.message);
		}

		if (writeResult.result.ok > 0) {
			return responseWrapper.succ({
				id: writeResult.ops[0]._id
			});
		} else {
			Logger.error('添加双向细目表失败');
			return responseWrapper.error('PARAMETERS_ERROR', '传入的双向细目表结构不合法');
		}
	});
}

/**
 * Description: 查看我的双向细目表
 * http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-查看我的双向细目表
 * Method: GET
 * URL: /kb_api/v2/tw_specifications/{table_ids}/list
 * Author: cuiyunfeng
 * Date: 2017-8-31
 * @public
 */
function getTableList(req, res) {
	let responseWrapper = new ResponseWrapper(res);

	let tableIds = decodeURIComponent(req.params.table_ids).split(',');
	tableIds = _.map(tableIds, function (id) {
		return ObjectID(id);
	});

	db.collection('tw_specification').find({
		_id: { $in: tableIds }
	}, {
		name: 1,
		vague_name: 1,
		period: 1,
		subject: 1,
		ctime: 1,
		utime: 1,
		view_times: 1,
		type: 1,
		province: 1,
		year: 1,
		grade: 1,
		download_times: 1,
		relevance_type: 1,
		relevance_id: 1
	}).toArray(function (err, docs) {
		if (err) {
			Logger.error(err.message);
			return responseWrapper.error('HANDLE_ERROR', err.message);
		}

		if (docs.length === 0) {
			Logger.info('没有对应的细目表' + req.params.table_ids);
			return responseWrapper.error('NULL_ERROR', '没有对应的细目表');
		}

		_.each(docs, function (doc) {
			doc.id = doc._id;
			if (doc.ctime)
				doc.ctime = new Date(doc.ctime).toLocaleDateString();
			if (doc.utime)
				doc.utime = new Date(doc.utime).toLocaleDateString();
			delete doc._id;
			doc.vague_name = filterExampaperName(doc);
			doc.name = mapExampaperName(doc, req.query);
		});

		return responseWrapper.succ(docs);
	});
}


/**
 * Description: 修改某双向细目表
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-修改某双向细目表
 * Method: PUT
 * URL: /assemble_api/v1/tw_specification/{tw_specification_id}/
 * Author: cuiyunfeng
 * Date: 2017-8-29
 * @public
 */
function updateTable(req, res) {
	let responseWrapper = new ResponseWrapper(res);

	let body = req.body;
	let tableId = req.params.table_id;

	/*
	 * 此处可能会出现不安全因素
	 */

	/*
	 * Load Data
	 */

	body = filterTable(body);
	body.utime = new Date();

	db.collection('tw_specification').updateOne({
		_id: ObjectID(tableId),
		permission: 'private'
	}, {
		$set: body
	}, function (err, writeResult) {

		if (err) {
			Logger.error(err.message);
			return responseWrapper.error('HANDLE_ERROR', err.message);
		}

		if (writeResult.result.ok > 0) {
			return responseWrapper.succ({
				status: 'ok'
			});
		} else {
			Logger.error('双向细目表修改失败');
			return responseWrapper.error('PARAMETERS_ERROR', '只允许修改自己的双向细目表');
		}
	});
}

const getTableById = async (req, res) => {
	let responseWrapper = new ResponseWrapper(res);
	let tableId = req.params.table_id;
	try {
		let doc = await db.collection('tw_specification').findOne({
			_id: ObjectID(tableId)
		});
		if (req.query.view_only !== 'false') {
			doc.view_times += 1;
			db.collection('tw_specification').updateOne({
				_id: ObjectID(tableId)
			}, {
				$inc: { view_times: 1 }
			});
		}
		if (!doc) {
			return responseWrapper.error('NULL_ERROR');
		}
		doc.id = doc._id;
		delete doc._id;
		doc.vague_name = filterExampaperName(doc);
		doc.name = mapExampaperName(doc, req.query);
		return responseWrapper.succ(doc);
	} catch (err) {
		Logger.error(err.stack);
		responseWrapper.error('HANDLE_ERROR', err.message);
	}
};

function getTableByRefId(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let relevanceId = Number(req.query.relevance_id);
	let relevance_id = isNaN(relevanceId) ? req.query.relevance_id : relevanceId;
	db.collection('tw_specification').findOne({
		relevance_id: relevance_id
	}, function (err, doc) {
		if (err) {
			Logger.error(err.message);
			return responseWrapper.error('HANDLE_ERROR', err.message);
		}

		if (!doc) {
			return responseWrapper.error('NULL_ERROR');
		}

		var tableId = doc._id;

		if (req.query.view_only !== 'false') {
			db.collection('tw_specification').updateOne({
				_id: tableId
			}, {
				$inc: { view_times: 1 }
			});
			doc.view_times += 1;
		}
		doc.id = doc._id;
		delete doc._id;
		doc.vague_name = filterExampaperName(doc);
		doc.name = mapExampaperName(doc, req.query);
		return responseWrapper.succ(doc);
	});
}

function downloadTimesInc(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	var tableId = req.params.table_id;
	db.collection('tw_specification').updateOne({
		_id: ObjectID(tableId)
	}, { $inc: { download_times: 1 } }, function (err, writeResult) {
		if (err) {
			return responseWrapper.error('HANDLE_ERROR', err.message);
		}

		if (writeResult.result.ok > 0) {
			return responseWrapper.succ({
				status: 'ok'
			});
		} else {
			return responseWrapper.succ({
				status: 'no'
			});
		}
	});
}

/**
 * Description: 选择双向细目表组卷
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-选择双向细目表组卷
 * Method: GET
 * URL: /assemble_api/v1/tw_specification/{tw_specification_id}/exampaper
 * Author: cuiyunfeng
 * Date: 2017-8-29
 * @public
 */
function getExampaperByTable(req, res) {
	var responseWrapper = new ResponseWrapper(res);

}


/**
 * Description: 从试卷导出双向细目表
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-从试卷导出双向细目表
 * Method: GET
 * URL: /assemble_api/v1/exampapers/{exampaper_id}/tw_specification/
 * Author: cuiyunfeng
 * Date: 2017-8-29
 * @public
 */
function getExampaperTable(req, res) {
	var responseWrapper = new ResponseWrapper(res);

}

/**
 * Description: 从试卷平行组卷
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-从试卷平行组卷
 * Method: POST
 * URL: /assemble_api/v1/exampapers/{exampaper_id}/exampaper/
 * Author: cuiyunfeng
 * Date: 2017-8-29
 * @public
 */
function createExampaperFromExampaper(req, res) {
	var responseWrapper = new ResponseWrapper(res);

}

/**
 * Description: 获取热门双向细目表
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-获取热门双向细目表
 * Method: GET
 * URL: /kb_api/v1/tw_specification/hot
 * Author: cuiyunfeng
 * Date: 2017-8-31
 * @public
 */
const getHotTable = async (req, res) => {
	let responseWrapper = new ResponseWrapper(res);

	let offset = Number(req.query.offset);
	let limit = Number(req.query.limit);

	offset = isNaN(offset) ? 0 : offset;
	limit = isNaN(limit) ? 6 : limit;

	let cursor = null;
	try {
		//let cond = { hot: true };
		let cond = {
			name: {$ne: '双向细目表名称'}
		};

		if (req.query.period) {
			cond.period = req.query.period;
		}
		if (req.query.subject) {
			cond.subject = req.query.subject;
		}
		if (req.query.type) {
			cond.type = req.query.type;
		}
		if (req.query.grade) {
			cond.grade = req.query.grade;
		}
		if (req.query.province) {
			cond.province = req.query.province;
		}
		if (req.query.year) {
			if (req.query.year === 'other') {
				const now = new Date();
				const giveYearAgo = now.getFullYear() - 5;
				cond.year = {$lte: giveYearAgo};
			} else {
				cond.year =  Number(req.query.year);
			}
		}
		let proj = {
			name: 1,
			vague_name: 1,
			period: 1,
			subject: 1,
			ctime: 1,
			view_times: 1,
			type: 1,
			province: 1,
			year: 1,
			grade: 1,
			download_times: 1,
			relevance_type: 1,
			relevance_id: 1
		};
		let sort = {};
		if (req.query.sort_by) {
			// sort[req.query.sort_by] = -1;
			sort.ctime = -1;
		} else {
			sort.view_times = -1;
		}
		cursor = db.collection('tw_specification').find(cond).project(proj);
		let count = await cursor.count();
		if (count === 0) {
			Logger.error('没有任何对应记录');
			return responseWrapper.error('NULL_ERROR', '暂时没有热门细目表');
		}
		let docs = await cursor.sort(sort).skip(offset).limit(limit).toArray();
		_.each(docs, function (doc) {
			doc.id = doc._id;
			delete doc._id;
			doc.vague_name = filterExampaperName(doc);
			doc.name = mapExampaperName(doc, req.query);
		});
		return responseWrapper.succ({
			total_num: count,
			specifications: docs
		});
	} catch (err) {
		return responseWrapper.error('HANDLE_ERROR', err.message);
	}
};

function deleteTable(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let tableId = req.params.table_id;
	db.collection('tw_specification').remove({
		_id: ObjectID(tableId),
		permission: 'private'
	}, function (err, writeResult) {
		if (err) {
			Logger.error(err.message);
			return responseWrapper.error('HANDLE_ERROR', err.message);
		}
		return responseWrapper.succ({
			status: 'ok'
		});
	});
}

function _sampleQuestion(cond, inQuestions) {

	let questions = inQuestions;

	let difficulty = {
		'容易': [0, 1],
		'较易': [2, 3],
		'中等': [4, 5],
		'较难': [6, 7],
		'困难': [8, 9]
	};

	questions = _.filter(questions, function (question) {
		return cond.type === question.type;
	});

	let questionsTmp = _.filter(questions, function (question) {
		return _.isEqual(_.sortBy(_.pluck(cond.knowledges, 'id')), _.sortBy(question.kns));
	});

	inQuestions = questions;

	if (questionsTmp.length !== 0)
		questions = questionsTmp;

	// 难度筛选
	questionsTmp = _.filter(questions, function (question) {

		let array = difficulty[cond.difficulty];
		if (!array)
			array = _.range(0, 11);

		return array.indexOf(Number(question.diff)) >= 0;
	});

	if (questionsTmp.length === 0) {

		// 难度筛选
		questionsTmp = _.filter(questions, function (question) {
			let array = difficulty[cond.difficulty];
			let max = _.max(array);
			let min = _.min(array);
			array.push(max + 1);
			array.push(max + 2);
			array.push(min - 1);
			array.push(min - 2);
			return array.indexOf(Number(question.diff)) >= 0;
		});
	}

	if (questionsTmp.length !== 0)
		questions = questionsTmp;

	if (questions.length === 0)
		return null;

	let questionObj = {};
	let simpleQuestions = [];

	_.each(questions, function (question) {

		if (question._id)
			question.id = question._id;

		if (question.kns) {

			if (question.kns.length == 1)
				simpleQuestions.push(question.id);

			_.each(question.kns.slice(0, 2), function (ques) {
				if (!questionObj[ques])
					questionObj[ques] = [];

				if (questionObj[ques].indexOf(question.id) < 0) {
					questionObj[ques].push(question.id);
				}
			});
		} else {
			simpleQuestions.push(question.id);
		}
	});

	let _questions = _.pluck(questions, 'id');
	let filter = [];
	let knowledgeId = null;

	for (let qo in questionObj) {
		if (_.pluck(cond.knowledges, 'id').indexOf(Number(qo)) >= 0)
			filter.push(questionObj[qo]);
	}

	if (cond.knowledges.length > 1) {

		if (_questions.length === 0)
			_questions = _.intersection.apply(this, filter);

		if (_questions.length === 0 && filter.length >= 2) {
			try {
				_questions = _.intersection.apply(this, _.sample(filter, 2));
			} catch (err) {
				_questions = [];
			}
		}

		if (_questions.length === 0 && filter.length >= 1) {
			_questions = simpleQuestions;
		}
	} else {
		_questions = simpleQuestions;
	}


	if (_questions && _questions.length === 0) {
		_questions = _.pluck(questions, 'id');
	}

	let qId = _.sample(_questions);
	let question = _.find(inQuestions, function (question) {
		return question.id === qId;
	});

	if (!question)
		return null;

	if (question.kns)
		delete question.kns;

	if (question.diff)
		delete question.diff;

	question.period = cond.period;
	question.subject = cond.subject;
	question.score = cond.score;

	return question;
}

function _getQuestionsByKnowledgeSet(knowledgeIds, callback) {
	knowledgeIds = _.uniq(knowledgeIds);

	db.collection('knowledge').find({
		_id: { $in: knowledgeIds }
	}, {
		'questions.kns': 1,
		'questions.id': 1,
		'questions.diff': 1,
		'questions.type': 1,
		'questions': { $slice: 500 }
	}).toArray(function (err, docs) {
		return callback(err, docs);
	});
}

function _getQuestionsByGroup(knowledgeIds, type, callback) {
	knowledgeIds = _.uniq(knowledgeIds);
	db.collection('knowledge').find({
		_id: { $in: knowledgeIds },
		'questions': { $elemMatch: { type: type } }
	}, {
		'questions.kns': 1,
		'questions.id': 1,
		'questions.diff': 1,
		'questions.type': 1,
		'questions': { $slice: 500 }
	}).toArray(function (err, docs) {
		return callback(err, docs);
	});
}

function _createExampaperByTable(table, callback) {
	let knowledgeObj = {};
	_.each(_.flatten(_.pluck(table.blocks, 'questions')), function (question) {
		try {
			let type = question.type;
			if (!knowledgeObj[type]) {
				knowledgeObj[type] = [];
			}
			knowledgeObj[type].push(question.knowledges[0].id);
		} catch (err) {
			//none
		}
	});

	Thenjs.each(_.keys(knowledgeObj), function (cont, key) {
		_getQuestionsByGroup(knowledgeObj[key], key, cont);
	}).then(function (cont, result) {
		let docs = _.flatten(result);
		let retval = [];

		/*
		 * 过滤_id
		 */
		_.each(docs, function (doc) {
			if (doc._id) {
				doc.id = doc._id;
				delete doc._id;
			}
		});

		let _table = table;
		let _questions = _.flatten(_.pluck(table.blocks, 'questions'));

		let preCond = null,
			preQuestion = null;

		for (let ix in _questions) {
			let cond = _questions[ix];
			if (!cond.knowledges || !cond.knowledges.length) {
				return callback(new Error('试题知识点不能为空'));
			}
			let _doc = _.find(docs, function (doc) {
				return doc.id === cond.knowledges[0].id;
			});

			if (!_doc)
				continue;

			var questions = _doc.questions;
			var question = _sampleQuestion(cond, questions);
			if (!question && !preQuestion) {
				question = _sampleQuestion(preCond, preQuestion);
			} else {
				preCond = cond;
				preQuestion = questions;
			}
			retval.push(question);
		}

		return callback(null, retval);
	});
}

function createExampaperByTable(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let table = req.body;
	return _createExampaperByTable(table, function (err, docs) {
		if (err) {
			return responseWrapper.error('HANDLE_ERROR', err.message);
		}

		return responseWrapper.succ(docs);
	});
}

const getUnableKnowledge = async (req, res) => {
	let resWrap = new ResponseWrapper(res);
	try {
		if (!req.body) {
			Logger.error('parameters error' + req.body);
			return resWrap.error('PARAMETERS_ERROR');
		}

		const DifficultyRange = {
			'不限': [1, 2, 3, 4, 5],
			'容易': [0, 1],
			'较易': [2, 3],
			'中等': [4, 5],
			'较难': [6, 7],
			'困难': [8, 9]
		};
		let data = req.body;
		let knowledgeArr = [];
		let knowledges_cond  = [];
		for (let a = 0; a < data.length; a++) {
			if (data[a].kids && data[a].kids.length > 0) {
				for (let b = 0; b < data[a].kids.length; b++) {
					knowledgeArr.push(Number(data[a].kids[b]));
					const obj = {
						knowledges: data[a].kids[b].toString(),
						limit: 1,
						period: data[a].period,
						subject: data[a].subject,
						// difficulty: data[a].diff,
						type: data[a].type,
						// set_mode: { knowledges: 'union', targets: 'union', methods: 'union' }
					};
					if (data[a].diff !== '不限') obj.difficulty = data[a].diff;
					knowledges_cond.push(obj);
				}

			}
		}

		// let knowledges = await db.collection('knowledge').find({ _id: { $in: knowledgeArr } }).project({ _id: 1, period: 1, subject: 1, 'questions.type': 1, 'questions.diff': 1 }).toArray();
		// let mapKnow = new Map();
		// for (let c = 0; c < knowledges.length; c++) {
		// 	const knowledge = knowledges[c];
		// 	knowledge.questions = [];
		// 	mapKnow.set(knowledges[c]._id, knowledge);
		// }

		// let existArr = [];
		// for (let i = 0; i < data.length; i++) {
		// 	let queryObj = data[i];
		// 	let flag = false;
		// 	for (let j = 0; j < queryObj.kids.length; j++) {
		// 		if (mapKnow.has(queryObj.kids[j]) && !flag) {
		// 			let tmpKnow = mapKnow.get(queryObj.kids[j]);
		// 			if (tmpKnow.period === queryObj.period && tmpKnow.subject === queryObj.subject && !flag) {
		// 				let diff = DifficultyRange[queryObj.diff];
		// 				for (let d = 0; d < tmpKnow.questions.length; d++) {
		// 					if (tmpKnow.questions[d].type === queryObj.type && diff.indexOf(tmpKnow.questions[d].diff) !== -1 && !flag) {
		// 						existArr.push(queryObj);
		// 						flag = true;
		// 					}
		// 				}
		// 			}
		// 		}
		// 	}
		// }
		// es 查找试题


		let uri = url.format({
			protocol: Config.SE_API_SERVER.protocol,
			hostname: Config.SE_API_SERVER.hostname,
			port: Config.SE_API_SERVER.port,
			pathname: '/se_kb/v2/filter/questions',
			search: qs.stringify({
				api_key: Config.SE_API_SERVER.appKey
			})
		});
		const exe = async (cond) => {
			try {
				let result = await axios.post(uri, cond, {timeout: 1000});
				if (result && result.data.code === 0 && result.data.data.total > 0) {
					return true;
				}
			} catch (e) {
				return false;
			}
		}
		const searchResult = await Promise.all(knowledges_cond.map(cond => exe(cond)));
		let mapKnow = new Map();
		for (const i in knowledges_cond) {
			const know = knowledges_cond[i];
			const hasQues = searchResult[i];
			if (!mapKnow.has(+know.knowledges)) {
				const k = {
					id: +know.knowledges,
					period: know.period,
					subject: know.subject,
					questions: []
				}
				mapKnow.set(+know.knowledges, k);
			}
			if (!hasQues) continue;
			mapKnow.get(+know.knowledges).questions.push({
				period: know.period,
				subject: know.subject,
				type: know.type,
				diff: know.difficulty,
			});
		}

		let existArr = [];
		for (let i = 0; i < data.length; i++) {
			let queryObj = data[i];
			let flag = false;
			for (let j = 0; j < queryObj.kids.length; j++) {
				if (mapKnow.has(queryObj.kids[j]) && !flag) {
					let tmpKnow = mapKnow.get(queryObj.kids[j]);
					if (tmpKnow.period === queryObj.period && tmpKnow.subject === queryObj.subject && !flag) {
						// let diff = DifficultyRange[queryObj.diff];
						for (let d = 0; d < tmpKnow.questions.length; d++) {
							if (tmpKnow.questions[d].type === queryObj.type && queryObj.diff ===tmpKnow.questions[d].diff && !flag) {
								existArr.push(queryObj);
								flag = true;
							}
						}
					}
				}
			}
		}

		let resArr = [];
		while (data.length > 0) {
			let tmpData = data.pop();
			let bExist = false;
			for (let e = 0; e < existArr.length; e++) {
				if (existArr[e].num === tmpData.num) {
					bExist = true;
					break;
				}
			}
			if (!bExist)
				resArr.push(tmpData);
		}

		return resWrap.succ(resArr);
	} catch (err) {
		Logger.error(err.stack);
		return resWrap.error('HANDLE_ERROR', err.message);
	}
};

function getQuestionIntegerDiff(diff) {
	let _diff = Number(diff);
	if (_diff < 0 || _diff >= 0 && _diff < 0.35) {
		_diff = 5;
	} else if (_diff >= 0.35 && _diff < 0.55) {
		_diff = 4;
	} else if (_diff >= 0.55 && _diff < 0.7) {
		_diff = 3;
	} else if (_diff >= 0.7 && _diff < 0.85) {
		_diff = 2;
	} else if (_diff >= 0.85 && _diff <= 1 || _diff > 1) {
		_diff = 1;
	}
	return _diff;
}

const JOI_SEARCH = Joi.object({
	name: Joi.string().optional().allow(''),
	period: Joi.string().optional().allow(''),
	subject: Joi.string().optional().allow(''),
	grade: Joi.array().items(Joi.string()).optional(),
	province: Joi.array().items(Joi.string()).optional(),
	year: Joi.array().items(Joi.number()).optional(),
	type: Joi.array().items(Joi.string()).optional(),
	offset: Joi.number().optional().default(0),
	limit: Joi.number().optional().default(10),
	sort_by: Joi.array().items(Joi.object()).optional(),
}).unknown(true);

async function searchTable(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	try {
		const params = await JOI_SEARCH.validate(req.body);
		let uri = url.format({
			protocol: Config.SE_API_SERVER.protocol,
			hostname: Config.SE_API_SERVER.hostname,
			port: Config.SE_API_SERVER.port,
			pathname: '/se_kb/v2/filter/tw_specification',
			search: qs.stringify({
				api_key: Config.SE_API_SERVER.appKey
			})
		});
		const cond = {};
		if (params.name) cond.name = params.name;
		if (params.period) cond.period = params.period;
		if (params.subject) cond.subject = params.subject;
		if (params.grade) cond.grade = params.grade;
		if (params.province) cond.province = params.province;
		if (params.year) cond.year = params.year;
		if (params.type) cond.type = params.type;
		if (params.offset) cond.offset = params.offset;
		if (params.limit) cond.limit = params.limit;
		if (params.sort_by) cond.sort_by = params.sort_by;
		else cond.sort_by = [{view_times: -1}];
		const result = await axios.post(uri, cond, { timeout: 10000 });
		let resultData = result.data;
		if (resultData.code === 0) {
			const sort = {}
			for (const s of params.sort_by) {
				const key = Object.keys(s)[0];
				sort[key] = s[key];
			}
			const tableIds = _.map(resultData.data.datas, id => ObjectID(id));
			const docs = await db.collection('tw_specification').find({_id: { $in: tableIds }}, {
				name: 1,
				vague_name: 1,
				period: 1,
				subject: 1,
				ctime: 1,
				utime: 1,
				view_times: 1,
				type: 1,
				province: 1,
				year: 1,
				grade: 1,
				download_times: 1,
				relevance_type: 1,
				relevance_id: 1
			}).sort(sort).toArray();
			_.each(docs, function (doc) {
				doc.id = doc._id;
				delete doc._id;
				doc.vague_name = filterExampaperName(doc);
				doc.name = mapExampaperName(doc, req.query);
			});
			responseWrapper.succ({ total_num: resultData.data.total, specifications: docs });
		} else {
			responseWrapper.succ({ total_num: 0, specifications: [] });
		}
	} catch (e) {
		Logger.error(e);
		responseWrapper.error('HANDLE_ERROR', e.message || e);
	}
}

module.exports = {
	createTable: createTable,
	getTableList: getTableList,
	updateTable: updateTable,
	getHotTable: getHotTable,
	getTableById: getTableById,
	deleteTable: deleteTable,
	getTableByRefId: getTableByRefId,
	downloadTimesInc: downloadTimesInc,
	createExampaperByTable: createExampaperByTable,
	getUnableKnowledge: getUnableKnowledge,
	searchTable,
};
