const grade_order = require('../../../modules/kb_api/v2/region_press/grade_order');

module.exports = function (router) {
     // 教学顺序管理
     router.get('/grade_orders/list', grade_order.getGradeOrderList);
     router.get('/grade_orders/region_press_version/:id', grade_order.getRegionPressVersionById);
     router.get('/grade_orders/:grade_order_id', grade_order.getGradeOrderById);
     router.put('/grade_orders/:grade_order_id', grade_order.updateGradeOrder);
};