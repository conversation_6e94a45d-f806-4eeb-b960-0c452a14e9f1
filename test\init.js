/* eslint-disable */
process.env.NODE_ENV = 'development.local';
const mongodber = require('../modules/common/utils/mongodber');
const config = require('config');

const initDB = function (dbName) {
    return new Promise((resolve) => {
        // 初始化基础资源数据库
        let databases = config.get('DATABASES');
        mongodber.init(databases, function (err) {
            if (err) {
                process.exit(-1);
            }
            resolve(mongodber.use(dbName || 'KB'));
        });
    });
};

module.exports = {
    initDB,
};
