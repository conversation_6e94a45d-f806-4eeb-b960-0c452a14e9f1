# 📚 K12校本教辅封面SVG提取工具

本工具可以从HTML页面中提取各个科目的教辅封面设计，并转换为高质量的SVG矢量格式。

## 📁 文件说明

### 核心文件
- `教辅封面2.html` - 原始HTML页面，包含所有科目的封面设计
- `extract_covers_to_svg.py` - 主要提取脚本，将HTML中的封面转换为SVG
- `svg_preview.html` - SVG预览页面，可在浏览器中查看所有生成的封面
- `convert_svg_to_png.py` - SVG转PNG工具（可选）

### 输出目录
- `svg_covers/` - 包含所有生成的SVG文件
- `png_covers/` - PNG格式文件（运行转换脚本后生成）

## 🎨 生成的封面

### 科目列表
| 科目 | 英文名称 | 变体数量 | 配色方案 |
|------|----------|----------|----------|
| 语文 | Chinese | 3 | 红色/棕色调 |
| 数学 | Mathematics | 3 | 蓝色调 |
| 英语 | English | 3 | 紫色/深蓝调 |
| 物理 | Physics | 2 | 深灰/青色调 |
| 化学 | Chemistry | 2 | 绿色/青绿调 |
| 生物 | Biology | 2 | 绿色/棕色调 |
| 历史 | History | 2 | 棕褐色调 |
| 地理 | Geography | 2 | 蓝绿/大地色调 |
| 政治 | Politics | 2 | 红色/金色调 |

### 设计特点
- **尺寸**: 120×150像素
- **格式**: SVG矢量格式，支持无损缩放
- **设计**: 每个科目都有独特的渐变背景和装饰图案
- **文字**: 包含中英文科目名称
- **结构**: 顶部标题区、中间内容区、底部区域

## 🚀 使用方法

### 1. 生成SVG文件
```bash
python extract_covers_to_svg.py
```
运行后会在 `svg_covers/` 目录中生成所有科目的SVG封面文件。

### 2. 预览封面
在浏览器中打开 `svg_preview.html` 文件，可以预览部分生成的封面效果。

### 3. 转换为PNG（可选）
如果需要PNG格式，首先安装依赖：
```bash
pip install cairosvg pillow
```

然后运行转换脚本：
```bash
python convert_svg_to_png.py
```

可以选择不同的输出分辨率：
- 1x: 120×150像素（原始尺寸）
- 2x: 240×300像素
- 4x: 480×600像素

## 📋 文件命名规则

SVG文件按以下格式命名：
```
科目名_variant-数字.svg
```

例如：
- `语文_variant-1.svg`
- `数学_variant-2.svg`
- `英语_variant-3.svg`

## 🎯 应用场景

这些封面设计适用于：
- 📖 教辅材料封面设计
- 🎨 教育类APP图标
- 📱 在线课程缩略图
- 🖼️ 教学资源展示
- 📚 数字图书馆分类图标

## 🔧 技术特点

### SVG优势
- **矢量格式**: 无损缩放，适合各种尺寸需求
- **文件小**: 相比位图格式，文件体积更小
- **可编辑**: 可以用代码或设计软件进一步修改
- **兼容性**: 现代浏览器和设计软件都支持

### 设计元素
- **渐变背景**: 使用CSS渐变转换为SVG linearGradient
- **装饰图案**: 包含几何图形、网格、文字等装饰元素
- **响应式**: 支持不同尺寸的显示需求
- **一致性**: 所有封面保持统一的设计风格

## 📝 自定义修改

如果需要修改封面设计，可以：

1. **修改颜色**: 编辑SVG文件中的 `linearGradient` 颜色值
2. **调整装饰**: 修改装饰图案的位置、大小、透明度
3. **更改文字**: 修改 `text` 元素的内容和样式
4. **添加元素**: 在SVG中添加新的图形元素

## 🤝 贡献

如果您有改进建议或发现问题，欢迎：
- 提交Issue报告问题
- 提交Pull Request改进代码
- 分享使用经验和建议

## 📄 许可证

本项目采用MIT许可证，您可以自由使用、修改和分发。

---

**生成时间**: 2025年7月17日  
**工具版本**: v1.0  
**支持的科目**: 9个主要K12科目，共21个设计变体
