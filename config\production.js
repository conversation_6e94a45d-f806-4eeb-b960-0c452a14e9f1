/* eslint-disable quotes */
module.exports = {
    "app": {
        "name": "KB_API 服务",
        "port": process.env.NODE_PORT || 8500
    },

    "DATABASES": {
        "KB": "***********************************************************************************************************************************",
    },

    "redis": {
        "host": "**********",
        "port": 6000,
        "db": 0,
        "password": "<EMAIL>"
    },

    "host": "dnr-kb-api-lan.yunxiao.com",
    "host_internal": "dnr-kb-api-lan.yunxiao.com",

    "log4js": {
        "logfaces": {
            "application": "KB_API",
            "url": "http://kblog-serv-lan.yunxiao.com/log_api/v1/logs"
        },
        "appenders": [{
            "type": "console"
        }, {
            "type": "dateFile",
            "filename": "./log/log",
            "pattern": "-yyyy-MM-dd.log",
            "alwaysIncludePattern": true,
            "category": "info"
        }, {
            "type": "dateFile",
            "filename": "./log/error",
            "pattern": "-yyyy-MM-dd.log",
            "alwaysIncludePattern": true,
            "category": "error"
        }],
        "levels": {"info": "INFO", "error": "ERROR"},
        "replaceConsole": true
    },

    "app_keys": [
        "iyunxiao_kboe",
        "iyunxiao_utilbox",
        "iyunxiao_monitor851427",
        "iyunxiao_kb98913578",
        "iyunxiao_analysis95684",
        "iyunxiao_haofenshu8361",
        "iyunxiao_cuotiben001",
        "iyunxiao_tr19911225",
        "iyunxiao_yiquanjie171207",
        "iyunxiao_kbplat180305",
        "iyunxiao_kb_plat",
        "iyunxiao_fudao944871489398",
        "iyunxiao_kb_plat_private",
        "iyunxiao_english100_a9061fd98992eb4c",
        "iyunxiao_fdp_serv2123421",
        "iyunxiao_boss1118",
        "iyunxiao_yuejuan1211",
        "iyunxiao_mkp20200421",
        "iyunxiao_mt20200421",
        "iyunxiao_drm20200421",
        "iyunxiao_tiku20200421",
        "iyunxiao_yuanpei20201203",
        "iyunxiao_kbp20201203",
        "iyunxiao_mkp20200421_private",
        "iyunxiao_mt20200421_private",
        "iyunxiao_drm20200421_private",
        "iyunxiao_tiku20200421_private",
        "iyunxiao_kbp20201203_private",
        "iyunxiao_ai20201214",
        "iyunxiao_zyk8990",
        "iyunxiao_aievaluation200409", // ai测评卷门店服务
        "iyunxiao_yxzx20241211_private",
        "iyunxiao_yxzx20241211"
    ],

    "kb_plat": {
        "appkey": [
            "iyunxiao_kb_plat",
            "iyunxiao_kb_plat_private"
        ],
        "unique_subjects": ["社会"]
    },

    "app_keys_private": [
        "iyunxiao_kb_plat_private",
        "iyunxiao_tr19911225",
        "iyunxiao_mkp20200421_private",
        "iyunxiao_mt20200421_private",
        "iyunxiao_drm20200421_private",
        "iyunxiao_tiku20200421_private",
        "iyunxiao_kbp20201203_private",
        "iyunxiao_yxzx20241211_private"
    ],

    "kb_api_server": {
        "protocol": "http",
        "hostname": "dnr-kb-api-lan.yunxiao.com",
        "port": 80,
        "api_key": "iyunxiao_kb98913578"
    },

    "kb_util_server": {
        "baseURI": "http://dnr-kb-util-lan.yunxiao.com",
        "api_key": "iyunxiao_kb98913578"
    },

    "visit_freq": {
        "KEY_EXP": 3600,
        "FREQ_LIMIT_PER_MIN": 6000,
        "FREQ_LIMIT_PER_HOUR": 40000,
        "FREQ_LIMIT_PER_DAY": 200000,
        "TOKEN_SMOOTH_WINDOW": 18000,
        "TIMES": 2,
        "APIS": {
            "get /kb_api/v2/exampapers/:exampaper_id/": {
                "day": 100,
                "hour": 25,
                "min": 10
            },
            "get /kb_api/v2/questions/:question_id/": {
                "day": 400,
                "hour": 100,
                "min": 30
            }
        }
    },

    "image_serv": {
        "protocol": "http",
        "hostname": "dnr-kb-util-lan.yunxiao.com",
        "port": 80
    },

    "SE_API_SERVER": {
        "protocol": "http",
        "hostname": "kb-se-lan.yunxiao.com",
        "port": 80,
        "appKey": "iyunxiao_kb8988" 
    },
    "BOSS_SERV": {
        "protocol": "http",
        "hostname": "boss-api.iyunxiao.com",
        "port": 80,
        "apikey": "9c0ef6da3b7a07bc311a0ebb092ca23a"
    },

    "ks_conf": {
        "yunxiao-kbvideo": {
            "ak": "jgLJSxZi1hJztGehRtCl",
            "sk": "eMW302IBAD2U6j6mNCvKXsZk7wu/o8aiidLhCDCH"
        },
        "yunxiao-kbimage": {
            "ak": "jgLJSxZi1hJztGehRtCl",
            "sk": "eMW302IBAD2U6j6mNCvKXsZk7wu/o8aiidLhCDCH"
        },
        "yunxiao-kbs": {
            "ak": "jgLJSxZi1hJztGehRtCl",
            "sk": "eMW302IBAD2U6j6mNCvKXsZk7wu/o8aiidLhCDCH"
        },
        "yunxiao-kb": {
            "ak": "LUKEBCQ32C6AVUK2TCHA",
            "sk": "5Qixn339yiXUBtiP67XPCB14TRHnoqq8cVPWVdM9"
        },
        "yx-kbs": {
            "ak": "AKLTKDDdfYFUSXOo2xOmSfsHhA",
            "sk": "OPPDC6oaEph3LyPTJjsLEZ9fjKEle3lbXMVLDEpTL7ig97tmpxf3YdM2CfOi7XwxHQ=="
        }
    },

	"BOS": {
		"endpoint": "https://bj.bcebos.com",
		"protocol": "http",
        "hostname": "jzl-oss.yunxiao.com",
		"ak": "0c7d26b7627d4e97a1a3e679575042bf",
		"sk": "25e81d25cdb442c29fe4c74861340a03",
		"bucketName": "ayx-kbs"
	},
    "knowledge_info": {
        "valid_cache_time": 600,
        "unvalid_cache_time": 3600 * 24
    },
    "knowledge_exam_times": {
        "valid_cache_time": 600,
        "unvalid_cache_time": 3600 * 24
    }
};
