/**
 * 知识点映射 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 9500,
    app_key: 'iyunxiao_kbplat180305',
};

describe('知识点映射', function () {
    describe('待映射知识点列表', function () {
        it('/kb_api/v2/knowledge_map/unmap/list', async function () {
            let pathname = `/kb_api/v2/knowledge_map/unmap/list`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                limit: 10,
                offset: 0,
                period: '初中',
                subject: '化学'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            assert.ok(Array.isArray(result.body.list));
            assert.ok(result.body.total_num > -1);
        });
    });

    describe('已映射知识点列表', function () {
        it('/kb_api/v2/knowledge_map/mapped/list', async function () {
            let pathname = `/kb_api/v2/knowledge_map/mapped/list`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                limit: 10,
                offset: 0,
                period: '初中',
                subject: '化学'
            };
            let result = await request.get(url).query(data);
            console.log(result.body);
            assert.ok(Array.isArray(result.body.list));
            assert.ok(result.body.total_num > -1);
        });
    });

    describe('更新映射', function () {
        it('/kb_api/v2/knowledge_map/:out_id', async function () {
            let out_id = '5cce5cfaaea41ba4859d7a7d';
            let pathname = `/kb_api/v2/knowledge_map/${out_id}`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                map_knows: [{ id: 321, name: 'fsfdf' }]
            };
            let result = await request.put(url).send(data);
            assert.deepStrictEqual(result.body, {});
        });
    });

});
