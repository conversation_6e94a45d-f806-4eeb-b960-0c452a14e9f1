const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
let Logger = require('../../../common/utils/logger');
const ObjectId = require('mongodb').ObjectID;

const school_line = 'score_line';
const school_press_version = 'school_press_version';
const exam_description = 'exam_description';
const exampaper = 'exampaper';

const getLines = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let type = req.query.type;
        let province = req.query.province;
        let city = req.query.city;
        let county = req.query.county;
        let offset = parseInt(req.query.offset) || 0;
        let limit = parseInt(req.query.limit) || 10;

        if (!type || !province) {
            return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
        }
        if (type === '高考') {
            if (!province) {
                return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
            }
            let result = await db.collection(school_line).findOne({ province: province });
            if (result) {
                result.id = result._id;
                delete result._id;
                result.stime = new Date();
            } else {
                result = {
                    id: null,
                    years: [],
                    stime: new Date()
                };
            }
            return responseWrapper.succ(result);
        }
        let cond = {
            province: province,
            'periods.name': (type === '中考' ? '高中' : '初中')
        };
        if (city) { cond.city = city; }
        if (county) { cond.country = county; }
        let total_num = await db.collection(school_press_version).find(cond).count();
        let data = await db.collection(school_press_version).find(cond).skip(offset).limit(limit).toArray();
        let result = {};
        let schools = [];
        result.stime = new Date();
        for (let school of data) {
            let sch = {};
            sch.id = school._id;
            sch.name = school.name;
            if (type === '中考') {
                if (school.score_lines && school.score_lines.junior_middle_exam) {
                    sch.score_lines = school.score_lines.junior_middle_exam;
                }
            }
            if (type === '小升初') {
                if (school.score_lines && school.score_lines.primary_exam) {
                    sch.score_lines = school.score_lines.primary_exam;
                }
            }
            if (!sch.score_lines) {
                sch.score_lines = [];
            }
            schools.push(sch);
        }
        result.schools = schools;
        result.total_num = total_num;
        return responseWrapper.succ(result);
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }

};
const updateLines = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let type = req.body.type;
        let data = req.body.data;
        let province = req.body.province;
        let insertId;
        if (!type) {
            return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
        }
        if (type === '高考') {
            let id = data.id;
            let years = data.years;
            if (!id) {
                let insertData = {
                    province: province,
                    years: years,
                    utime: new Date(),
                    ctime: new Date()
                };
                let result = await db.collection(school_line).insertOne(insertData);
                insertId = result.insertedId;
            } else {
                insertId = id;
                await db.collection(school_line).updateOne({ _id: ObjectId(id) }, { $set: { years: years, utime: new Date() } });
            }
        }
        if (type === '中考' || type === '小升初') {
            for (let sch of data) {
                let id = sch.id;
                let lines = sch.score_lines;
                if (type === '中考') {
                    await db.collection(school_press_version).updateOne({ _id: parseInt(id) }, { $set: { 'score_lines.junior_middle_exam': lines, utime: new Date() } });
                }
                if (type === '小升初') {
                    await db.collection(school_press_version).updateOne({ _id: parseInt(id) }, { $set: { 'score_lines.primary_exam': lines, utime: new Date() } });
                }
            }
        }
        return responseWrapper.succ({ id: insertId });
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }
};
const getDesc = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let type = req.query.type;
        let province = req.query.province;
        let city = req.query.city;
        let county = req.query.county;
        if (!type || !province) {
            return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
        }
        let result = {};
        if (type === '高考') {
            result = await db.collection(exam_description).findOne({ type: type, province: province });
        }
        if (type === '小升初' || type === '中考') {
            let cond = {
                type: type,
                province: province,
                city: city,
                county: county
            };
            result = await db.collection(exam_description).findOne(cond);
        }
        if (result) {
            result.id = result._id;
            delete result._id;
        } else {
            result = {
                id: null,
                description: ''
            };
        }
        return responseWrapper.succ(result);
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }
};
const updateDesc = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let id = req.body.id;
        let type = req.body.type;
        let desc = req.body.description;
        let province = req.body.province;
        let city = req.body.city;
        let county = req.body.county;
        let insertId;
        if (!id) {
            let data = {
                type: type,
                description: desc,
                province: province,
                utime: new Date(),
                ctime: new Date()
            };
            if (city && county) { data.city = city; data.county = county; }
            let result = await db.collection(exam_description).insertOne(data);
            insertId = result.insertedId;
        } else {
            insertId = id;
            await db.collection(exam_description).updateOne({ _id: ObjectId(id) }, { $set: { description: desc } });
        }
        return responseWrapper.succ({ id: insertId });
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }
};

const getkbexampaper = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let g_paperid = req.params.id;
        let data = await db.collection(exampaper).findOne({ g_paper_id: g_paperid });
        let result = {};
        if (data) {
            result.id = data._id;
            if (data.statistics) {
                let kbstatistics = [];
                for (let statistic of data.statistics) {
                    let stat = statistic.statistics;
                    if (stat) {
                            kbstatistics.push(stat);
                    }
                }
                result.statistics = kbstatistics;
            }
        }
        return responseWrapper.succ(result);
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }
};
const getmtexampaper = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let g_paperid = req.params.g_paper_id;
        if (!g_paperid) {
            return responseWrapper.error('PARAMETERS_ERROR', '缺少g_paper_id');
        }
        let result = {};
        result = await db.collection(exampaper).findOne({ g_paper_id: g_paperid });
        if (result) {
            return responseWrapper.succ({id:result._id});
        }
        return responseWrapper.succ({});
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }
};
module.exports = {
    getLines,
    updateLines,
    getDesc,
    updateDesc,
    getkbexampaper,
    getmtexampaper
};