const url = require('url');
const config = require('config');
const request = require('request');
const Logger = require('../../common/utils/logger');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const IMAGESERV = config.get('image_serv');

/**
 * This function gets the service from image_serv and submits the resource to 
 * the service to get the address of the picture in KS3. The address returned 
 * by the function is then written to the fieldName field in the target library.
 */
function getAndModifyImage(urlString, colName, fieldName){
	return function(id, callback){
		const proj = {};
		let cond = {_id: id};
		proj[fieldName] = 1;
		let IMAGESERV = config.get('image_serv');

		db.collection(colName).findOne(cond, proj, function(err, doc){
			if (err || !doc){
				return callback(err, null);
			}

			/**
			 * First in the database to find whether the fieldName field is 
			 * existed. And the data in the database contains this 
			 * field, it will return the result directly.
			 */
			if (!doc[fieldName]){
				const imageUrl = url.format({
					protocol: IMAGESERV.protocol,
					hostname: IMAGESERV.hostname,
					port: IMAGESERV.port,
					pathname: urlString,
					search: '?appid=kb&question_part='+fieldName
				});

				request.get(imageUrl, function(err, response, body){

					if (err){
						return callback(err, null);
					}

					if (response.statusCode !== 200){
						return callback(new Error(), null);
					}

					try {
						body = JSON.parse(body);	
						proj[fieldName] = body.url;
						db.collection(colName).updateOne(cond, {
							$set: proj
						}, function(err){

							/*
							 * Theory, err === null
							 */
							return callback(err, body.url);
						});
					} catch (err){
						return callback(err, null);
					}
				});
			} else {
				return callback(err, doc[fieldName]);
			}
		});
	}
}

/**
 * 或者试题的题干对应的图片
 * @param {integer} id 试题id
 * @param {string} fieldName 更新字段
 * @returns {Promise}
 */
function getImagePart(id, fieldName) {
	return new Promise((sovle,reject) => {
		let urlString = `/utilbox_api/v2/questions/${id}/image`;
		const imageUrl = url.format({
			protocol: IMAGESERV.protocol,
			hostname: IMAGESERV.hostname,
			port: IMAGESERV.port,
			pathname: urlString,
			search: '?appid=kb&question_part='+fieldName
		});

		request.get(imageUrl, function(err, response, body){

			if (err){
				return reject(err, null);
			}

			if (response.statusCode !== 200){
				return reject(new Error(), null);
			}

			try {
				body = JSON.parse(body);
				sovle(body.url);
			} catch (err){
				return reject(err, null);
			}
		});
	});
}

function checkQuestionFieldEmpty(quetion, project) {
	let flag = true;
	for (let field in project) {
		if (!quetion[field]) {
			continue;
		} else {
			flag = false;
			break;
		}
	}
	return flag;
}
async function batchUpdateQuestionImges(ids, option = {}) {
	const force = option.force || '';
	const coll = db.collection('question');
	const project = {
		stem_img: 1,
		answer_img: 1,
		solution_img: 1,
		explanation_img: 1
	};
	const fields = Object.keys(project);
	let set = {};
	try {
		for (let id of ids) {
			
			let quetion = await coll.findOne({_id: id});
			let empty = checkQuestionFieldEmpty(quetion, project);
			if (!empty && !force) {
				continue;
			}
			for (let field of fields) {
				set[field] = await getImagePart(id, field);
			}
			await coll.updateOne({_id: id}, {$set: set});
			
		}
	} catch (err) {
		return;
	}
}
module.exports = {
	getAndModifyImage: getAndModifyImage,
	batchUpdateQuestionImges: batchUpdateQuestionImges
};

