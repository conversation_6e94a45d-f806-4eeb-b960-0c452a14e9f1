var Thenjs  = require('thenjs');
var config = require('config');
var request = require('request');
var qs = require('querystring');
var Logger = require('../../common/utils/logger');
var ResponseWrapper = require('../../common/utils/response_wrapper');
var params_utils = require('../../common/utils/params.js');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../common/utils/rediser');
var kb_api = require('../config.js').kb_api['v2'];
var cols_to_tree = require('./utils.js').cols_to_tree;
var loosen_tree = require('./utils.js').loosen_tree;
var URL = require('url');
const KBServerCfg = config.get('kb_api_server');
const _ = require('underscore');

const KaoshenFilterKeys = ['period', 'subject', 'press_version', 'grade', 'name', 'year'];
const ListenBookFilterKeys = ['period', 'subject',  'press_version', 'grade'];
const FilterKeysMap = {
                        '天天练习': KaoshenFilterKeys, 
                        '超级听写': ListenBookFilterKeys,
                    };
const GuideChapterFields = ['ques_id', 'chinese_words', 'eng_words'];
function _requestQuestions(qids, device, callback) {
	if (qids.length  === 0 ){
		return callback(null, []); 
	}
    var postBody = JSON.stringify({
        question_ids: qids,
        fields_type: 'full',
        device: device,
    });
    
    var questionsUrl = URL.format({
        protocol: KBServerCfg.protocol,
        hostname: KBServerCfg.hostname,
        port: KBServerCfg.port,
        pathname: 'kb_api/v2/questions/',
        search: qs.stringify({
            api_key:KBServerCfg.api_key 
        })
    });
    try {
        request.post({
            url: questionsUrl,
            headers: {
                'content-type': 'application/json'
            },
            body: postBody
        }, function(error, response, _body){
            if(response.statusCode !== 200 || error){
                return callback('HANDLE_ERROR', null);
            }
            var retObj = JSON.parse(_body);
            return  callback(null, retObj);
        });
    } catch (e) {
        Logger.error(e);
        return callback(e);
    }   
}


function _getGuideBooksKey(params) {
    var findTag = false;
    var guideBooksKey  = 'kb_api:v2:guide_book:'+ process.env.NODE_PORT + ':' + params.app_name;
    var guideBooksPrefix = guideBooksKey;
    var guideBookFilterKeys = FilterKeysMap[params.app_name]; 
    for (var keyIndex in guideBookFilterKeys) {
        var key = guideBookFilterKeys[keyIndex];
        if (params[key]) {
            guideBooksKey += ':' + params[key];
            findTag = true;
        } else {
            if (!findTag) {
                guideBooksKey += ':' + key +  '_all';
            }
        }
    }
    if ( findTag ){
        return guideBooksKey; 
    } else {
        return guideBooksPrefix; 
    }
}

function guideBooks(req, res) {
    var responseWrapper = new ResponseWrapper(res);
    var params = null;
    try {
        var fun_name = arguments.callee.name; 
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    Thenjs(function(cont) {
        var guideBooksKey = _getGuideBooksKey(params);
        rediser.get(guideBooksKey, function (err, item) {
            if (err) {
                return cont(err);
            }
            if (!item) {
                 //没有找到缓存中的key
                return cont(null, guideBooksKey);
            } else {
                return responseWrapper.succ(item);
            }
        });
    }).then(function(cont, keyName) {
        var cond = { 
			'app_name' : params.app_name,
		};
        var guideBookFilterKeys = FilterKeysMap[params.app_name]; 

        for (var index in guideBookFilterKeys) {
            var key = guideBookFilterKeys[index];
            if (params[key]) {
                cond[key] = params[key];
            }
        }
        var proj = {};
        for (var index in guideBookFilterKeys) {
            var key = guideBookFilterKeys[index];
            proj[key] = 1;
        }
		proj['is_valid'] = 1;

		var sort_by = {'order_no' : 1};
        db.collection('guide_book').find(cond).project(proj).sort(sort_by).toArray(function (err, Books) {
            if (err) {
                return cont(err);
            }
			try {
            	if (Books.length == 0) {
                	return responseWrapper.succ({'guide_books':{'children':[]}});
            	}
				Books = _.filter(Books, function(book){
					if (book.is_valid ===  false){
						return false;
					}
					return true;
				});
            	var keys = [];
            	var index = 0;
            	_.each(guideBookFilterKeys, function(key) {
                	keys[index] = [key, key];
                	index++;
            	});
            	var retObj = cols_to_tree('guide_books', Books, keys);
            	rediser.set(keyName, retObj, 60*30);
            	return responseWrapper.succ(retObj);
			} catch(err) {
				return responseWrapper.error('HANDLE_ERROR');
			}
        });
    }).fail(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}


function guideBookStrcut(req, res) {
    var responseWrapper = new ResponseWrapper(res);
    var params = null;
    try {
        var fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    
    db.collection('guide_book').findOne({_id: params.guide_book_id}, function (err, book) {
        if (err) {
            return responseWrapper.error('HANDLE_ERROR','取guideBook表出错');
        }
        try {
            if (!book) {
                return responseWrapper.error('NULL_ERROR', '此id没有找到相关的guidebook');
            } else {
                book['id'] = book['_id'];
                delete book['_id'];
                
                book['guide_book'] = { 'chapters': book.chapters};
                delete book.chapters;
                var keys = {'chapters': 'chapters'};
                loosen_tree(book['guide_book'], keys);
                return responseWrapper.succ(book);
            }
        } catch (err) {
            Logger.error(err.message);
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
    });
}

function guideChapter(req, res) {

    var responseWrapper = new ResponseWrapper(res);
    var params = null;
    try {
        var fun_name = arguments.callee.name; 
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    
    Thenjs(function(cont) {

        db.collection('guide_chapter').findOne({_id: params.chapter_id}, function(err, chapter) {
            if (err || !chapter) {
                return responseWrapper.error('HANDLE_ERROR', '获取辅导章节id出错');
            }        
			chapter['id'] = chapter['_id'];
			delete chapter['_id'];
            return cont (null, chapter);
        }); 
    
    }).then(function(cont, chapter) {
        if (params.fields_type === 'all' || params.fields_type ==='questions') {
            if (chapter.hasOwnProperty('ques_id') && Array.isArray(chapter.ques_id)) {
                _requestQuestions(chapter.ques_id, params.device, function(err, questions){
                    if (err) {
                        return responseWrapper.error('HANDLE_ERROR', 'request kb err');
                    }
					try {
                    	var sortArray = [];
                    	_.each(chapter.ques_id, function(qid) {
                        	var findQuestion = _.find(questions, function (question) {
                            	return question.id === qid;    
                        	});
                        	if (findQuestion) {
                            	sortArray.push(findQuestion);
                        	}
                    	});
                    	chapter['id'] = chapter['_id'];
                    	delete chapter['_id'];
                    	delete chapter.ques_id;
                    	chapter.questions = sortArray;
                    	return cont(null, chapter);
					} catch (err) {
						return responseWrapper.error('HANDLE_ERROR');
					}
                });    
            } else {
                return cont(null, chapter);
            }
            
        } else {
            return cont(null, chapter)
        }
    }).then(function(cont, chapter) {
       if (params.fields_type !== 'all') {
	   		for (var index = 0; index < GuideChapterFields.length ; ++index){
				var curField = GuideChapterFields[index];
				if (params.fields_type != curField && chapter.hasOwnProperty(curField)) {
					delete chapter.curField;
				}
			}
	   } 
	   return responseWrapper.succ(chapter);
    }).fail(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function(cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

module.exports = {
    guideBooks: guideBooks,
    guideBookStrcut: guideBookStrcut,
    guideChapter: guideChapter,
}
