//地标树
const landmarkTree = require('../../../modules/kb_api/v2/landmark/landmark_tree');
const landmarkPoint = require('../../../modules/kb_api/v2/landmark/landmark_point');


module.exports = function (router) {

    // 请求地标树树信息
    router.get('/landmark_tree/list/', landmarkTree.getLandmarkTreeList);
    router.get('/landmark_tree/:id', landmarkTree.getLandmarkTreeById);
    router.put('/landmark_tree/:id', landmarkTree.putLandmarkTreeInfo);
    router.post('/landmark_tree', landmarkTree.landmarkTreeInfo);

    router.get('/landmark_points/knowledges/:knowledge_id', landmarkPoint.getLandPointList);
    
    // 地标点
    router.get('/landmark_points/:point_ids', landmarkPoint.getLandmarkPointByIds);
    router.put('/landmark_points', landmarkPoint.putLandmarkPoints);
    router.post('/landmark_points', landmarkPoint.postLandmarkPoints);
    router.post('/landmark_points/exists', landmarkPoint.checkExists);
    router.put('/landmark_points/questions/has_errors', landmarkPoint.checkQuestionHasError);
};
