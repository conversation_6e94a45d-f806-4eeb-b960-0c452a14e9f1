/**
 * 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 8500,
    app_key: 'iyunxiao_kbplat180305',
};

describe('对象类别列表', function () {
    it('/kb_api/v2/targets/category/list', async function () {
        let pathname = `/kb_api/v2/targets/category/list`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            subject: '语文',
            period: '高中'
        }
        let result = await request.get(url).query(data);
        console.log(result.body);
        assert.ok(result.body.total_num >= 0);
    });
});

describe('对象列表', function () {
    it.only('/kb_api/v2/targets/detail/{id}', async function () {
        let pathname = `/kb_api/v2/targets/detail/list/234334`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let result = await request.get(url);
        console.log(result.body);
        assert.ok(result.body.total_num>=0);
    });
});

describe('类别是否存在', function () {
    it('/kb_api/v2/targets/category/{name}', async function () {
        let id = 2147418111;
        let pathname = `/kb_api/v2/targets/category/sdfsfsf`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            subject: '语文',
            period: '高中'
        }
        let result = await request.get(url).query(data);
        console.log(JSON.stringify(result.body));
        assert.ok(result.body);
    });
});

describe('对象是否存在', function () {
    it('/kb_api/v2/targets/detail/{name}', async function () {
        let pathname = `/kb_api/v2/targets/detail/sdfsfsf`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            subject: '语文',
            period: '高中'
        }
        
        let result = await request.post(url).query(data);
        console.log(JSON.stringify(result.body));
        assert.ok(result.body);
    });
});
