var crypto = require("crypto");
var encode = require("../../common/utils/utils.js").encode;
var is_empty_obj = require("../../common/utils/utils.js").is_empty_obj;

var SALT = "Z85a2:1b9e}f5c`57fm9a^ede.19*f$31";

var DIRTY_CH =
	"水题是中为解数物化分和一用有生在电体可质不选方时学大度应成能量故小答于子图点出以个与水要据线式气合动地根空力上得性所反理定本平关形等变对正面填高法原考了氧程由相择知同查到表过下液作求确两人实行酸运向则发位因加此溶意种后即角最示速国间直结进三长比细多如素二离库其基积算系主计验初内元值胞入及这流重属现区错列条明从利析色之组误温热光压当率产单球来经次而该碳通函路我部会识键案断公使象制自或特类含判置第米少构件项边都取说氢增接减机做强将别四活甲阻器常开功就果北转义律乙标圆代然再南全较放设外受问年海带把无心态测除西若射吸处并铁读们工先极交源道移各它传目只车需写注导状称周界管金东半天号白也前场期环符洲均家燃盐已且料价容硫握距总植还完文钠立低装达共简越调具去滑被整指集每回口衡影看型保记山氯统试染段起提情收掌布石核烧业浓亚存观字新差连没轴样河联民块磁密究况某响坐排灯证乘节消他降日者镜论难固决干势世规着品食血月改非匀红端几洋展又径概精右依泡材里境好混础候事感拉围母例名升效恒千养左太显层负粒持板片但命倍资级足像推易剂止病步摩铜省域信糖碱换互些异约纬述复木季书想序油叶适风么近切淀政花请引擦建熟探克菌底阳准育曲察垂稀瓶美际弹黄造斜火照治蛋清见市语包息范否沿声呼征括土随始膜沉至微绿族杂粉欧输综零真静落守配必科画满裂很钙济任江身给何万侧安滴格认陆首折你护神社汽抛思那蒸视维厘支波室荷游失研短遗刻透控农充氨培手防余头致青习钾玻岛参丙网顺脉叫双皮操释够柱丝检念音闭句激快纯纸轨五酒供圈按浮广递织才晶教似城找尔偏台营星另占牛采般轮污触更斯氮顶殖械冰黑缩打铝璃毒灰雨华杆百十词史频技耗倒池硝草针历酶锰播竖弱滤商战林深假逐未群树弦张串获绝己往京药卵富术抗杯壁限簧净练尺稳权终便梯飞施费旋阶突呈仪划门须份循走什途末横住马虫远胚焦古害绳毛骤冷老哪振顿官阴眼凸员沙续藏六超促骨德磷散渐印模免链缺锥略鱼居夏邻灵逆慢沸女镁优银催饱姆腰既蓝亲熔备湿额兴罗码醇归叙棒争助景服革丰绕奇隔钟腺言苯巴剩丁忆烷冬遇肺令扩洗泌盘夜脂描偶宽校聚让待亮破卫棱煤停夹碘船恰独课团湾灭补尿股许矿杠刚严乳班鉴阿房举抽疫附冲湖务肥站岸靠脱椭润临筒英颜寒州味肌师盖胶盆孔留盛菜春航斗男儿矩臂箱拔买惯粗螺载繁轻漏创勾阅领录脑硬峡良胎仅父涉秒昼虑吨幂赤香措缓估葡却肠烯萄珠健普康燥尾修钢爱蜡锌紫觉朝塞话烛肾醋驶死溴仍桌众凝伸著送弧军铵继废苏肉今笔腾宜延王钡酚志介腔映替截迹虚穿鸟望纳暗档纵钱亿售客刺芽汉秦隐坏益腐幅烟港队雪鼠俄危议七殊框患";

var DIRTY_EN =
	"qazxswedcvfrtgbnhyujmkiolpPOIUYTREWQLKJHGFDSAMNBVCXZAinMeKDnaIZgl";
var DIRTY_NUM = "0987654321123456";
var SKIP_CHS = new Set([
	"，",
	"。",
	" ",
	"（",
	"）",
	"　",
	"：",
	"“",
	"”",
	"．",
]);
var REPL_CHS_IN_MATH = new Set(["+", "-", "*", "=", "|", "^", "_"]);

var NORM_DIRTY_CH_LEN = closest_2exp(DIRTY_CH.length) - 1;
var NORM_DIRTY_EN_LEN = closest_2exp(DIRTY_EN.length) - 1;

/*
 * Desc:
 *      将传入的数组转为树状结构
 * Params:
 *      tree_name - 返回的树的名称
 *      input_data - 数组数据
 *      node_name_keys - 每个level下节点的name
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-22
 */
function cols_to_tree(tree_name, input_data, node_name_keys, id_key) {
	if (!input_data || input_data.length == 0) {
		return null;
	}

	var col_num = node_name_keys.length;
	if (col_num < 2) {
		return null;
	}

	if (!id_key) {
		id_key = "_id";
	}
	var output_d = {};
	for (i in input_data) {
		var t = input_data[i];
		var parent_node = output_d;
		for (var i in node_name_keys) {
			var col_name = node_name_keys[i][0];
			var node_name = node_name_keys[i][1];
			var c = t[col_name];
			if ("null" == c.toLowerCase()) {
				continue;
			}
			if (null == key_in_node(c, "name", parent_node, "children")) {
				if (!parent_node.hasOwnProperty("children")) {
					parent_node["children"] = [];
				}
				var nodes = parent_node["children"];
				if (i < col_num - 1) {
					nodes.push({ key: node_name, name: c });
				} else {
					nodes.push({ key: node_name, name: c, id: t[id_key] });
				}
			}
			parent_node = key_in_node(c, "name", parent_node, "children");
		}
	}
	var r = {};
	r[tree_name] = output_d;
	return r;
}

/*
 * Desc:
 *      判断id是否在node[node_name]的list中
 * Params:
 *      key - 目标节点是key_name的属性值为key
 *      key_name - 在node[node_name]的list中查找key_name的节点
 *      node - 当前节点
 *      node_name - 在当前节点的node_name属性下查找
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-22
 */
function key_in_node(key, key_name, node, node_name) {
	if (!node || !node.hasOwnProperty(node_name)) {
		return null;
	} else {
		var l = node[node_name];
		for (var i in l) {
			var e = l[i];
			if (key == e[key_name]) {
				return e;
			}
		}
		return null;
	}
}

/*
 * Desc:
 *      将紧凑树转为宽松树
 * Params:
 *      input - 紧凑的数据
 *      keys - 键映射表
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-11-14
 */
function loosen_tree(input, keys, excludes) {
	if (!input) {
		return;
	}
	// 清除不需要的项
	if (excludes && excludes instanceof Array) {
		for (var key of excludes) {
			delete input[key];
		}
	}
	// 转换
	for (var key in keys) {
		if (!input.hasOwnProperty(key)) {
			continue;
		}
		var value = input[key];
		if (null == value || is_empty_obj(value)) {
			delete input[key];
			continue;
		}
		if (input.hasOwnProperty("children")) {
			for (var i in value) {
				input["children"].push(value[i]);
			}
		} else {
			input["children"] = value;
		}
		delete input[key];
		var key_ = keys[key];
		for (var i in value) {
			let v = value[i];
			v["key"] = key_;
			loosen_tree(v, keys, excludes);
		}
	}
}

/*
 * Desc:
 *      将宽松树转为紧凑树
 * Params:
 *      input - 宽松的数据
 *      keys - 键映射表
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-11-14
 */
function tighten_tree(input, keys) {
	if (input == null || !input.hasOwnProperty("children")) {
		return;
	}

	var kids = input["children"];
	var orig_key = null;
	for (var i in kids) {
		var kid = kids[i];
		var key = kid["key"];
		if (keys.hasOwnProperty(key)) {
			orig_key = key;
			break;
		}
	}
	if (orig_key == null) {
		return;
	}

	var target_key = keys[orig_key];
	input[target_key] = kids;
	delete input["children"];

	for (var i in kids) {
		var kid = kids[i];
		delete kid["key"];
		tighten_tree(kid, keys);
	}
}

/*
 * Desc:
 *      dirty a string
 * Params:
 *      s - input string
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2017-04-06
 */
function dirty_string(s, ignore_len) {
	if (!s || (ignore_len > 0 && s.length <= ignore_len)) {
		return s;
	}
	// gen token by s
	var m = crypto.createHash("md5");
	m.update(s + SALT);
	var token = m.digest("hex");

	var len = s.length;
	var norm_s_len = closest_2exp(len) - 1;
	var repl_ch_num = 2;
	if (len > 50) {
		repl_ch_num = len >> 3;
	} else if (len > 15) {
		repl_ch_num = len >> 2;
	} else if (len > 10) {
		repl_ch_num = 4;
	}
	var avg = parseInt(len / repl_ch_num);

	// dirty data
	var dirty = "";
	var cur = "0x";
	var math_expr_flag = 0;
	var html_src_flag = 0;
	var i = -1;
	var repl_cnt = 0;
	var next = -1;
	while (++i < len) {
		var ch = s[i];
		if ("$" == ch && "{" == s[i + 1]) {
			// latex
			math_expr_flag++;
			i++;
			dirty += "${";
			continue;
		} else if ("}" == ch && "$" == s[i + 1]) {
			math_expr_flag--;
			i++;
			dirty += "}$";
			continue;
		} else if (
			"<" == ch &&
			((s[i + 1] >= "a" && s[i + 1] <= "z") || "/" == s[i + 1])
		) {
			// html
			html_src_flag++;
			dirty += ch;
			continue;
		} else if (
			html_src_flag > 0 &&
			">" == ch &&
			((s[i - 1] >= "a" && s[i - 1] <= "z") || "/" == s[i - 1])
		) {
			html_src_flag--;
			dirty += ch;
			continue;
		} else if (SKIP_CHS.has(ch)) {
			dirty += ch;
			continue;
		} else {
			var changed = false;
			if (next < len && (i - next) % avg == 0) {
				// generate next pos
				if (token[repl_cnt]) {
					if (cur.length > 13) {
						cur = "0x" + token[repl_cnt++];
					} else {
						cur += token[repl_cnt++];
					}

					if (repl_cnt > repl_ch_num) {
						next = len;
					} else {
						changed = true;
						next = Number(cur) & norm_s_len;
					}
				} else {
					next = len;
				}
			}
			if (math_expr_flag > 0 || html_src_flag > 0) {
				if (changed) {
					var need_repl =
						(ch >= "A" && ch <= "Z") ||
						(ch >= 0 && ch <= 9) ||
						REPL_CHS_IN_MATH.has(ch);
					if (need_repl) {
						var ch_pos = (Number(cur) * 17) & NORM_DIRTY_EN_LEN;
						dirty += DIRTY_EN[ch_pos];
						continue;
					}
				}
				dirty += ch;
				continue;
			} else {
				if (changed) {
					var ch_pos = (Number(cur) * 17) & NORM_DIRTY_CH_LEN;
					dirty += DIRTY_CH[ch_pos];
				} else {
					dirty += ch;
				}
			}
		}
	}
	return dirty;
}

/*
 * Desc:
 *      Find a number that statisfies
 *      	output <= input
 *      	output*2 > input
 *      	output == 2^exp
 * Params:
 *      num - input number
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2017-04-06
 */
function closest_2exp(num) {
	var o = 1;
	while (o <= num) {
		o = o << 1;
	}
	return o >> 1;
}

/*
 * Desc:
 *      generate a dirty id based on original id
 *      dirty id = [encode(1), encode(max_val)]
 * Params:
 *      id - original id
 *      max_val - max value
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2017-04-10
 */
function dirty_id(id, max_val) {
	var t = ((id * 271) & max_val) + 1;
	return encode(t);
}

// 将数组data参照数组sort的顺序进行排序，依据str字段
const to_sort = (sort, data, str) => {
	let _data = {};
	let arr = [];
	for (let i = 0, l = data.length; i < l; i++) {
		_data[data[i][str]] = data[i];
	}
	for (let i = 0, l = sort.length; i < l; i++) {
		if (_data[sort[i]]) {
			arr.push(_data[sort[i]]);
		}
	}
	return arr;
};

// 查出的结果中提取出省市.1. 通过多省市查询。 2通过单个省市查询
const getProvinceCity = (paper, params) => {
	let { provinces, province, city } = params;
	// 多省市字段查询
	if (Array.isArray(provinces) && provinces.length > 0) {
		let proArr = [];
		let cities = [];
		for (let p of provinces) {
			if (p.name) {
				proArr.push(p.name);
			}
			if (Array.isArray(p.cities)) {
				cities = cities.concat(p.cities);
			}
		}
		if (Array.isArray(paper.provinces)) {
			for (let p of paper.provinces) {
				if (proArr.includes(p.name)) {
					paper.province = p.name; // 设置省
					if (Array.isArray(p.cities)) {
						for (let c of p.cities) {
							if (cities.includes(c)) {
								paper.city = c; // 设置市
								return;
							}
						}
					}
					return;
				}
			}
		}
	}
	// 单省市字段查询
	if (province) {
		paper.province = province;
		if (city) {
			paper.city = city;
		}
	}
};

// 省市做为查询条件
const setCondProvinces = (cond, params) => {
	let { province, city, provinces } = params;
	if (province) {
		if (city) {
			cond.provinces = {
				$elemMatch: { name: province, cities: { $in: [city] } },
			};
		} else {
			cond.provinces = { $elemMatch: { name: province } };
		}
	}
	if (provinces && typeof provinces === "string") {
		try {
			provinces = JSON.parse(provinces);
		} catch (err) {
			console.error(err);
		}
	}

	if (Array.isArray(provinces) && provinces.length > 0) {
		let arr = [];
		for (let p of provinces) {
			if (p.name) {
				let ele = null;
				if (Array.isArray(p.cities) && p.cities.length > 0) {
					ele = {
						provinces: {
							$elemMatch: { name: p.name, cities: { $in: p.cities } },
						},
					};
				} else {
					ele = { provinces: { $elemMatch: { name: p.name } } };
				}
				arr.push(ele);
			}
		}
		cond["$or"] = arr;
	}
};

// 设置搜索引擎省市查询
const setSeProvinces = (cond, params) => {
	let { province, city, provinces } = params;
	if (province) {
		cond.province = province;
	}
	if (city) {
		cond.city = city;
	}
	if (Array.isArray(provinces) && provinces.length > 0) {
		let arr = [];
		for (let p of provinces) {
			if (p.name) {
				if (Array.isArray(p.cities) && p.cities.length > 0) {
					let isExists = false;
					for (let c of p.cities) {
						if (c) {
							isExists = true;
							arr.push(`${p.name}-${c}`);
						}
					}
					if (!isExists) {
						arr.push(p.name);
					}
				} else {
					arr.push(p.name);
				}
			}
		}
		if (arr.length > 0) {
			cond.provinces = arr.join(",");
		}
	}
};

module.exports = {
	cols_to_tree: cols_to_tree,
	loosen_tree: loosen_tree,
	tighten_tree: tighten_tree,
	dirty_string: dirty_string,
	dirty_id: dirty_id,
	to_sort: to_sort,
	getProvinceCity,
	setCondProvinces,
	setSeProvinces,
};
