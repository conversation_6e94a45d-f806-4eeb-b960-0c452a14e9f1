const post = {
    type: 'object',
    additionalProperties: false,
    required: ['subject', 'period', 'type', 'parent', 'children'],
    properties: {
        subject: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        type: {
            type: 'string',
        },
        parent: {
            type: 'integer',
        },
        children: {
            type: 'array',
            minItems: 1,
            items: {
                type: 'integer'
            }
        }
    }
};

const updateRepeat = {
    type: 'object',
    additionalProperties: false,
    required: ['parent', 'children'],
    properties: {
        parent: {
            type: 'integer',
        },
        children: {
            type: 'array',
            minItems: 1,
            items: {
                type: 'integer'
            }
        }
    }
};

module.exports = {
    post,
    updateRepeat,
};
