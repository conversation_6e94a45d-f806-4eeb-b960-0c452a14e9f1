const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../common/utils/rediser');
const cache = rediser.getCache();
var ResponseWrapper = require('../../common/utils/response_wrapper');
var utils = require('../../common/utils/utils.js');


function find_src(src_name, cond, proj, callback){
    var coll = db.collection(src_name);
    coll.find(cond, proj).toArray(function(err, items){
        if (err){
            return callback(err);
        }
        if (!items){
            return callback(null, null);
        }
        for (var i in items){
            let item = items[i];
            item['id'] = item['_id'];
            delete item['_id'];
        }
        return callback(null, items);
    });
}

function find_src_by_ids(src_name, ids, proj, callback){
    var cond = {_id: {$in: ids}};
    var order_d = {};
    for (var i=0; i<ids.length; ++i ){
        order_d[ids[i]] = i;
    }
    find_src(src_name, cond, proj, function(err, items){
        if (err){
            return callback(err);
        }
        if (!items){
            return callback(null, null);
        }
        items.sort((x, y) => order_d[x.id] - order_d[y.id]);
        return callback(null, items);
    });
}

function find_src_top(src_name, id, callback){
    var filter = {_id: id};
    var proj = {speaking_times: 1, speaking_scores: 1, speaking_top: 1};
    find_src(src_name, filter, proj, function(err, items){
        if (err){
            return callback(err);
        }
        if (!items){
            return callback(null, null);
        }
        var item = items[0];
        try{
            var speaking_top = item['speaking_top'] || [];
            if (speaking_top.length == 0){
                return callback(null, {
                    speaking_times: 0,
                    speaking_top: []
                });
            }
            var min_score = speaking_top[speaking_top.length-1]['score'];
            var speaking_times = item['speaking_times'];
            var speaking_scores = item['speaking_scores'];
            var score_win_rate = {};
            var lnum = 1;
            for (var i in speaking_scores){
                if (i >= min_score){
                    score_win_rate[i] = lnum / speaking_times;
                }
                lnum += speaking_scores[i];
            }
            for (var i in speaking_top){
                var score = speaking_top[i]['score'];
                speaking_top[i]['ranking'] = parseInt(i)+1;
                speaking_top[i]['win_rate'] = score_win_rate[score]
            }
            delete item['_id'];
            delete item['speaking_scores'];
            return  callback(null, item);
        } catch(err){
            return callback(err);
        }
    });
}

function eng_words(req, res){
	var responseWrapper = new ResponseWrapper(res);
    try{
        var ids_ = req.params.ids.split(',');
        var ids = []
        ids_.forEach(function(id){
            ids.push(parseInt(id.trim()))
        });
    } catch(err){
        return responseWrapper.error('PARAMETERS_ERROR' ,err.message);
    }
    var proj = {speaking_scores: 0, speaking_top: 0, ctime:0, utime:0};
    find_src_by_ids('eng_word', ids, proj, function(err, items){
        if (err){
            return responseWrapper.error('HANDLE_ERROR');
        }
        if (!items){
            return responseWrapper.error('NULL_ERROR');
        }
        return  responseWrapper.succ(items);
    });
}

function eng_sentences(req, res){
	var responseWrapper = new ResponseWrapper(res);
    try{
        var ids_ = req.params.ids.split(',');
        var ids = []
        ids_.forEach(function(id){
            ids.push(parseInt(id.trim()))
        });
    } catch(err){
        return responseWrapper.error('PARAMETERS_ERROR' ,err.message);
    }
    var proj = {speaking_scores: 0, speaking_top: 0, ctime:0, utime:0};
    find_src_by_ids('eng_sentence', ids, proj, function(err, items){
        if (err){
            return responseWrapper.error('HANDLE_ERROR');
        }
        if (!items){
            return responseWrapper.error('NULL_ERROR');
        }
        return  responseWrapper.succ(items);
    });
}

function eng_dialogs(req, res){
	var responseWrapper = new ResponseWrapper(res);
    try{
        var ids_ = req.params.ids.split(',');
        var ids = []
        for (var i in ids_){
            ids.push(parseInt(ids_[i].trim()))
        }
    } catch(err){
        return responseWrapper.error('PARAMETERS_ERROR' ,err.message);
    }

    var proj = {dialog: 1};
    find_src_by_ids('eng_dialog', ids, proj, function(err, dialogs){
        if (err){
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
        if (!dialogs){
            return responseWrapper.error('NULL_ERROR');
        }
        var sids = [];
        for (var i in dialogs){
            var dialog = dialogs[i]['dialog'];
            for (var j in dialog){
                var sentences = dialog[j]['sentences'];
                for (var k in sentences){
                    sids.push(sentences[k]['id']);
                }
            }
        }
        var sproj = {speaking_scores: 0, ctime: 0, utime: 0};
        find_src_by_ids('eng_sentence', sids, sproj, function(err, sentences){
            if (err){
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }
            var smap = {};
            for (var i in sentences){
                var item = sentences[i];
                var id = item['id'];
                smap[id] = item;
            }
            for (var i in dialogs){
                var dialog = dialogs[i]['dialog'];
                for (var j in dialog){
                    var sentences = dialog[j]['sentences'];
                    for (var k in sentences){
                        sentences[k] = smap[sentences[k]['id']]
                    }
                }
            }
            return  responseWrapper.succ(dialogs);
        });
    });
}

function get_eng_word_top(req, res){
	var responseWrapper = new ResponseWrapper(res);
    try{
        var id = parseInt(req.params.id);
    } catch(err){
        return responseWrapper.error('PARAMETERS_ERROR' ,err.message);
    }
    find_src_top('eng_word', id, function(err, item){
        if (err){
            return responseWrapper.error('HANLE_ERROR');
        }
        if (!item){
            return responseWrapper.error('HANDLE_ERROR');
        }
        return  responseWrapper.succ(item);
    });
}

function get_eng_sentence_top(req, res){
	var responseWrapper = new ResponseWrapper(res);
    try{
        var id = parseInt(req.params.id);
    } catch(err){
        return responseWrapper.error('PARAMETERS_ERROR' ,err.message);
    }
    find_src_top('eng_sentence', id, function(err, item){
        if (err){
            return responseWrapper.error('HANDLE_ERROR');
        }
        if (!item){
            return responseWrapper.error('NULL_ERROR');
        }
        return  responseWrapper.succ(item);
    });
}

function update_speaking_top(speaking_top, cand){
    var max = 10;
    var len = speaking_top.length - 1;
    if (len >= max-1 && speaking_top[len].score > cand.score){
        return null;
    }
    var tag = false;
    var i;
    for (i=len; i>=0; --i){
        var st = speaking_top[i];
        if (st.score > cand.score){
            var j;
            for (j=i; j>=0; --j){
                if (st.id == cand.id){
                    break;
                }
            }
            if (j < 0){ // 没有重复时才插入
                speaking_top.splice(i+1, 0, cand);
                tag = true;
            }
            break;
        } else {
            // st.score <= score
            if (st.id == cand.id){
                speaking_top.splice(i, 1);
            }
        }
    }
    if (i < 0){
        speaking_top.splice(0, 0, cand);
        tag = true;
    }
    if (tag){
        return speaking_top.slice(0, max);
    }
    return null;
}

function put_score(req, res, src_name){
	var responseWrapper = new ResponseWrapper(res);
    try{
        var id = parseInt(req.params.id);
        var user_id = req.body.id;
        var name = req.body.name;
        if (!user_id || !name){
            throw Error('user id or name can not empty');
        }
        var score = parseInt(req.query.score) || parseInt(req.body.score);
        if (score < 0 || score > 100){
            throw Error('score value unlegal');
        }
    }catch(err){
        return responseWrapper.error('PARAMETERS_ERROR' ,err.message);
    }

    var filter = {_id: id};
    var tmp = {};
    tmp['speaking_scores.'+score] = 1;
    tmp['speaking_times'] = 1;
    var update = {
        '$inc': tmp, 
    };
    var options = {
        'projection': {
            'speaking_scores': 1,
            'speaking_top': 1,
            'speaking_times': 1
        },
        'returnOriginal': false,
    }
    var coll = db.collection(src_name);
    coll.findOneAndUpdate(filter, update, options, function(err, ret){
        if (err){
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
        if (!ret){
            return responseWrapper.error('NULL_ERROR');
        }
        ret = ret['value'];
        if (!ret){
            return responseWrapper.error('NULL_ERROR');
        }
        try{
            // 更新speaking_top
            var speaking_top = ret['speaking_top'] || [];
            var cand = {
                id: user_id,
                name: name,
                score: score
            };
            speaking_top = update_speaking_top(speaking_top, cand);
            if (speaking_top){
                coll.update(filter, {$set: {speaking_top: speaking_top}});
            }

            // 计算击败率
            var speaking_scores = ret['speaking_scores'];
            var lnum = 1;
            var speaking_times = ret['speaking_times'];
            for (var i in speaking_scores){
                if (i >= score){
                    break;
                }
                lnum += speaking_scores[i];
            }
            let result = {
                speaking_times: speaking_times,
                win_rate: lnum / speaking_times,
            }
            return  responseWrapper.succ(result);
        } catch(err){
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
    });
}

function put_eng_word_score(req, res){
	var responseWrapper = new ResponseWrapper(res);
    try{
        return put_score(req, res, 'eng_word');
    }catch(err){
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

function put_eng_sentence_score(req, res){
	var responseWrapper = new ResponseWrapper(res);
    try{
        return put_score(req, res, 'eng_sentence');
    }catch(err){
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

module.exports = {
    eng_words: eng_words,
    put_eng_word_score: put_eng_word_score,
    get_eng_word_top: get_eng_word_top,
    eng_sentences: eng_sentences,
    put_eng_sentence_score: put_eng_sentence_score,
    get_eng_sentence_top: get_eng_sentence_top,
    eng_dialogs: eng_dialogs,
}
