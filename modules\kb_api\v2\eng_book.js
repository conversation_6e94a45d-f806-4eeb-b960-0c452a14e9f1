const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');

function getEnglishBooksToCatalog (callback) {
	let coll = db.collection('eng_book');
	// 提取出的字段
	let proj = {
		id: 1,
		grade: 1,
		press_version: 1,
		subject: 1, 
		period: 1
	};
	// 查询
	coll.find({}, proj).toArray(function (err, englishBookList) {
		if (err) {
			return callback(err);
		}
		// let keys = [
		// 	['period', 'period'],
		// 	['subject', 'subject'],
		// 	['press_version', 'press_version'],
		// 	['grade', 'grade']
		// ];
		// let book = cols_to_tree('book', englishBookList, keys);
		// book['_id'] = 'book';
		// callback(null, book);
		callback(null, englishBookList);
	});
}
module.exports = {
	getEnglishBooksToCatalog: getEnglishBooksToCatalog
};