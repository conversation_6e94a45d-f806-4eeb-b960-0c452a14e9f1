const ObjectID = require('mongodb').ObjectID;
const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const schemaGroup = require('./schemas/target_group');

// 数据库集合名
const groupColl = 'target_group';

// 入库数据schema模版
const getDataTemplate = (body) => ({
    name: body.name,
    period: body.period,
    subject: body.subject,
    children: body.children,
});

// 是否组名重复
const groupNameRepeat = async (data, groupId) => {
    if (!data.subject || !data.period || !data.name) {
        throw new Error('学段，学科，组名称需要全部填写');
    }
    let existCond = { subject: data.subject, period: data.period, name: data.name, invalid: 0 };
    let options = { fields: { _id: 1 } };
    let existRes = await db.collection(groupColl).findOne(existCond, options);
    if (groupId) { // 更新的判断
        if (existRes) {
            return existRes._id.toString() !== groupId;
        }
        return false;
    }
    // 新增的判断
    return existRes !== null;
};

/**
 * kbp审核通过，推送过来，添加
 * @param {*} req 
 * @param {*} res 
 */
const postGroup = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    let now = new Date();
    try {
        let data = getDataTemplate(body);
        // 格式校验
        if (!ajv.validate(schemaGroup.putData, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '格式错误');
        }
        data.ctime = now;
        data.utime = now;
        data.invalid = 0;
        // 检查是否组名重复
        let existRes = await groupNameRepeat(data);
        if (existRes) {
            return responseWrapper.error('HANDLE_ERROR', '组名已经存在');
        }
        // 添加数据
        let result = await db.collection(groupColl).insertOne(data);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '新增出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

/**
  * kbp审核通过，推送过来，更新
  * @param {*} req 
  * @param {*} res 
  */
const updateGroup = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    let body = req.body;
    try {
        let data = getDataTemplate(body);
        // 格式校验
        if (!ajv.validate(schemaGroup.putData, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '格式错误');
        }
        data.utime = new Date();
        // 检查是否组名重复
        let existRes = await groupNameRepeat(data, id);
        if (existRes) {
            return responseWrapper.error('HANDLE_ERROR', '组名已经存在');
        }
        // 更新数据
        let cond = { _id: ObjectID(id), invalid: 0 };
        let setData = { $set: data };
        let result = await db.collection(groupColl).updateOne(cond, setData);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '修改出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

/**
 * 获取详情，根据id
 * @param {*} req 
 * @param {*} res 
 */
const getGroupById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let cond = { _id: ObjectID(req.params.id), invalid: 0 };
        let result = await db.collection(groupColl).findOne(cond);
        if (result) {
            result.id = result._id;
            delete result._id;
        }
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

/**
 * 获取详情
 * @param {*} req 
 * @param {*} res 
 */
const getGroupsList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let { period, subject, limit, offset } = req.query;
    if (!period || !subject) {
        return responseWrapper.error('HANDLE_ERROR', '请传学段学科');
    }
    let cond = { period: period, subject: subject, invalid: 0 };
    limit = limit || 10;
    offset = offset || 0;
    try {
        let cursor = await db.collection(groupColl).find(cond);
        let totalNum = await cursor.count();
        let resultList = await cursor.skip(parseInt(offset))
            .limit(parseInt(limit))
            .sort([['_id', -1]])
            .toArray();
        if (resultList && resultList.length > 0) {
            for (let i = 0; i < resultList.length; i++) {
                resultList[i].id = resultList[i]._id;
                delete resultList[i]._id;
            }
        }
        responseWrapper.succ({ total_num: totalNum, list: resultList });
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

/**
 * kbp审核通过，推送过来，删除
 * @param {*} req 
 * @param {*} res 
 */
const deleteGroup = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let cond = { _id: ObjectID(req.params.id) };
        let setData = { $set: { invalid: 1, utime: new Date() } };
        let result = await db.collection(groupColl).updateOne(cond, setData);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};
/**
 * 检查组名称是否重复
 * @param {object} req req.query: {peirod, subject, name, id}
 *                      (peirod, subject, name, 必填)
 * @param {object} res 
 */
const isGroupRepeat = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let result = await groupNameRepeat(req.query, req.query.id);
        responseWrapper.succ({ exists: result });
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

module.exports = {
    postGroup, // 添加
    updateGroup, // 更新
    getGroupsList, // 列表
    getGroupById, // 获取详情，根据id
    deleteGroup, // 删除
    isGroupRepeat, // 检查组名称是否重复
};
