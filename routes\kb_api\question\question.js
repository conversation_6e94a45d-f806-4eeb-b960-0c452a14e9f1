// 试题相关接口
const question = require('../../../modules/kb_api/v2/question');
const questionDistinct = require('../../../modules/kb_api/v2/question_distinct');
const exampaper = require('../../../modules/kb_api/v2/exampaper');

module.exports = function (router) {
    // 知识点/细分/对象获取试题
    router.post('/questions/by_search', question.questionsBySearch);
    // 试题搜索
    router.post('/questions/by_search2', question.questionsBySearchV2);
    // 返回试题资源的统计信息
    router.get('/questions/filters/', question.filters);
    // 获取试题筛选信息(kbp2.10.5新增：试题标签，英语年级，小题数量，选项数量)
    router.get('/questions/filters2', question.filters2);
    router.get('/questions/elite_count', question.getEliteCount);
    // 创建试题
    router.post('/questions/instances', question.addQuestion);
    router.put('/questions/instances/:question_id', question.updateQuestion);
    // 返回试题资源的统计信息
    router.get('/questions/profile/', question.profile);
    // 简单展示试题，供试题管理页面首页初次展示
    router.get('/questions/news', question.simpleSelectQuestions);
    //机器打完标签之后待审查试题列表
    router.get('/questions/tags_review', question.getPendingReviewQuestions);
    // 批量获取试题练习量
    router.post('/questions/exercise_statistics', question.postPracticeStatisticsByIds);
    // 基于试题id，请求试题信息
    router.get('/questions/:question_id/', question.question);
    // 基于多个试题id, 返回全部试题信息
    router.get('/questions/', question.get_questions);
    router.get('/questions/qids/kbp', question.getQuestionsByQId);

    // 基于多个试题id, 返回全部试题信息
    router.post('/questions/', question.post_questions);
    // 修改试题内容 kbp在使用
    router.put('/questions/:question_id', question.modify_question);
    // 各种试题信息获取
    //下面两个接口是生成试题题干图片接口，暂时dmp使用
    router.put('/lessons/questions/:question_id/stemImage', question.setStemImageUrlToQuestion);
    router.post('/questions/stemImage', question.updateQuestionPartImages);
    router.get('/questions/:question_ids/stem_img/', question.getQuestionStemImage('stem_img'));
    router.get('/questions/:question_ids/answer_img/', question.getQuestionStemImage('answer_img'));
    router.get('/questions/:question_ids/solution_img/', question.getQuestionStemImage('solution_img'));
    router.get('/questions/:question_ids/explanation_img/', question.getQuestionStemImage('explanation_img'));
    router.get('/questions/:question_id/change', question.changeAnotherQuestion);
    router.get('/questions/:question_id/change_some/', question.changeQuestions);
    // 根据一或者多个试题id，下载相应试题的word文件
    router.get('/questions/:question_ids/download/', exampaper.downloadQuestions);
    // 相似题
    router.post('/questions/:id/reco_questions', question.addSimilarQuestions);
    router.put('/questions/:id/reco_questions', question.updateSimilarQuestions);
    // drm的试题数量
    router.get('/questions/drm/count', question.getDrmCount);

    // 试题去重，kbp审核通过，推送过来, 添加
    router.put('/question_distinct', questionDistinct.updateRepeat);

    // 批量试题 use_times 加1
    router.post('/assemble/questions/update_use_times/', exampaper.updateQuestionsUseTimes);

    // 给试题打质检标签
    router.put('/questions/inspect/:question_id', question.inspect);
};
