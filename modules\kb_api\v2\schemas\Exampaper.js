const createKbExampaperSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        id: {
            type: 'number',
        },
        type: {
            type: 'string',
        },
        subject: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        status: {
            type: 'string',
        },
        score: {
            type: 'number',
        },
        name: {
            type: 'string',
        },
        sch_name: {
            type: 'string',
        },
        sch_short_name: {
            type: 'string',
        },
        region: {
            type: 'string',
        },
        press_version: {
            type: 'string',
        },
        grade: {
            type: 'string',
        },
        province: {
            type: 'string',
        },
        city: {
            type: 'string',
        },
        to_year: {
            type: 'integer',
        },
        from_year: {
            type: 'integer',
        },
        user_name: {
            type: 'string',
        },
        blocks: {
            type: 'array',
        },
        type_score: {
            type: 'array',
        },
    }
};

const createHfsExampaperSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {

        g_paper_id: {
            type: 'string',
        },
        id: {
            type: 'integer',
        },
        type: {
            type: 'string',
        },
        subject: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        status: {
            type: 'string',
        },
        score: {
            type: 'number',
        },
        name: {
            type: 'string',
        },
        sch_name: {
            type: 'string',
        },
        sch_short_name: {
            type: 'string',
        },
        region: {
            type: 'string',
        },
        press_version: {
            type: 'string',
        },
        grade: {
            type: 'string',
        },
        province: {
            type: 'string',
        },
        city: {
            type: 'string',
        },
        to_year: {
            type: 'integer',
        },
        from_year: {
            type: 'integer',
        },
        user_name: {
            type: 'string',
        },
        blocks: {
            type: 'array',
        },
        type_score: {
            type: 'array',
        },
    }
};

module.exports = {
    createKbExampaperSchema,
    createHfsExampaperSchema,
};