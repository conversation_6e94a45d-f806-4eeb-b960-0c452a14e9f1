const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');

// 确保输出目录存在
const outputDir = path.join(__dirname, 'textbook-covers');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 定义所有科目及其封面数量
const subjects = [
  { name: '语文', className: 'chinese', count: 3 },
  { name: '数学', className: 'math', count: 3 },
  { name: '英语', className: 'english', count: 3 },
  { name: '物理', className: 'physics', count: 3 },
  { name: '化学', className: 'chemistry', count: 3 },
  { name: '生物', className: 'biology', count: 3 },
  { name: '历史', className: 'history', count: 3 },
  { name: '地理', className: 'geography', count: 3 },
  { name: '政治', className: 'politics', count: 3 },
  { name: '音乐', className: 'music', count: 3 },
  { name: '美术', className: 'art', count: 3 },
  { name: '信息技术', className: 'it', count: 3 }
];

async function captureCoverImages() {
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    // 设置视口大小
    await page.setViewport({
      width: 1200,
      height: 800
    });

    // 加载HTML文件
    const htmlPath = `file://${path.join(__dirname, '教辅封面2.html')}`;
    await page.goto(htmlPath, { waitUntil: 'networkidle0' });

    // 等待页面完全加载
    await page.waitForTimeout(2000);

    // 为每个科目创建子目录
    for (const subject of subjects) {
      const subjectDir = path.join(outputDir, subject.name);
      if (!fs.existsSync(subjectDir)) {
        fs.mkdirSync(subjectDir, { recursive: true });
      }

      console.log(`正在截取${subject.name}科目的封面...`);

      // 获取该科目的所有封面卡片
      const coverCards = await page.evaluate((subjectName) => {
        const sections = Array.from(document.querySelectorAll('.subject-section'));
        const targetSection = sections.find(section => {
          const title = section.querySelector('.subject-title');
          return title && title.textContent.includes(subjectName);
        });
        
        if (!targetSection) return [];
        
        return Array.from(targetSection.querySelectorAll('.cover-card')).map((card, index) => {
          const titleEl = card.querySelector('.title');
          const subtitleEl = card.querySelector('.subtitle');
          return {
            index,
            title: titleEl ? titleEl.textContent : '',
            subtitle: subtitleEl ? subtitleEl.textContent : ''
          };
        });
      }, subject.name);
      
      for (let i = 0; i < coverCards.length && i < subject.count; i++) {
        const cardInfo = coverCards[i];
        
        // 找到对应的元素进行截图
        const card = await page.evaluateHandle((subjectName, cardIndex) => {
          const sections = Array.from(document.querySelectorAll('.subject-section'));
          const targetSection = sections.find(section => {
            const title = section.querySelector('.subject-title');
            return title && title.textContent.includes(subjectName);
          });
          
          if (!targetSection) return null;
          const cards = targetSection.querySelectorAll('.cover-card');
          return cards[cardIndex];
        }, subject.name, i);
        
        // 生成文件名
        const fileName = `${subject.name}_${i + 1}_${cardInfo.subtitle}.png`;
        const filePath = path.join(subjectDir, fileName);
        
        // 截取单个封面
        if (card && card.asElement()) {
          await card.asElement().screenshot({
            path: filePath,
            omitBackground: false
          });
          
          console.log(`  ✓ 已保存: ${fileName}`);
        } else {
          console.log(`  ✗ 无法截取: ${fileName}`);
        }
      }
    }

    console.log('\n所有封面图片已成功截取！');
    console.log(`输出目录: ${outputDir}`);

  } catch (error) {
    console.error('截取过程中出现错误:', error);
  } finally {
    await browser.close();
  }
}

// 执行截图
captureCoverImages().catch(console.error);