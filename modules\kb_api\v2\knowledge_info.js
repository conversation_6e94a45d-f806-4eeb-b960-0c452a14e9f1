const config = require('config');
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../common/utils/rediser');
const utils = require('../../common/utils/utils');
const validCacheTime = config.get('knowledge_info').valid_cache_time;
const unvalidCacheTime = config.get('knowledge_info').unvalid_cache_time;

const getFields = (fields_type) => {
    let hfsfdCourseware = { 'real_exam_statis.years': 1, 'real_exam_statis.exam_times': 1 };
    let fields = {
        'hfsfd_courseware': hfsfdCourseware
    };
    let defaultFields = { ctime: 0, utime: 0 };
    return fields[fields_type] || defaultFields;
};

const getKnowledgeInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let kid = parseInt(req.params.knowledge_id);
    let cond = { _id: kid };
    let cacheKey = `kb_api:v2:knowledge_info:${kid}`;
    try {
        let knowInfo = await rediser.get(cacheKey);
        if (knowInfo) {
            return responseWrapper.succ(knowInfo);
        }
        knowInfo = await db.collection('knowledge_info').findOne(cond);
        if (knowInfo) {
            rediser.set(cacheKey, knowInfo, validCacheTime);
        } else {
            rediser.set(cacheKey, {}, unvalidCacheTime);
        }
        responseWrapper.succ(knowInfo);
    } catch (error) {
        Logger.error(error);
        responseWrapper.error('HANDLE_ERROR');
    }
};

const getKnowledgeBatchInfos = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let kids = req.params.knowledge_ids;
    let fields_type = req.query.fields_type || '';
    let filter_fields = req.query.filter_fields || '';
    let cacheKey = `kb_api:v2:knowledge_info:batch:${utils.getMd5(fields_type + kids + filter_fields)}`;
    let pro = {};
    if (filter_fields) {
        filter_fields = filter_fields.split(',');
        for (const field of filter_fields) {
            pro[field] = 1;
        }
    } else {
        pro = getFields(fields_type);
    }

    try {
        let kidArr = kids.split(',').map((id) => Number(id));
        let cond = { _id: { $in: kidArr } };
        let knowInfo = await rediser.get(cacheKey);
        if (knowInfo) {
            return responseWrapper.succ(knowInfo);
        }
        knowInfo = await db.collection('knowledge_info').find(cond).project(pro).toArray();
        if (knowInfo.length > 0) {
            rediser.set(cacheKey, knowInfo, validCacheTime);
        } else {
            rediser.set(cacheKey, [], unvalidCacheTime);
        }
        responseWrapper.succ(knowInfo);
    } catch (error) {
        Logger.error(error);
        responseWrapper.error('HANDLE_ERROR');
    }
};

module.exports = {
    getKnowledgeInfo,
    getKnowledgeBatchInfos
};
