const config = require('config');
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../common/utils/rediser');
const validCacheTime = config.get('knowledge_exam_times').valid_cache_time;
const unvalidCacheTime = config.get('knowledge_exam_times').unvalid_cache_time;
const crypto = require('crypto');

const getFields = (fields_type) => {
    let hfsfd = {
        know_id: 1,
        type: 1,
        name: 1,
        statis: 1,
    };
    let fields = {
        hfsfd
    };
    let defaultFields = {ctime: 0, utime: 0};
    return fields[fields_type] || defaultFields;
};

const sortKnowledgeExamTimes = (sort_data, data) => {
    // 将data转换为object格式
    let obj_data = {};
    data.forEach(item => {
        item.id = item.know_id;
        delete item._id;
        delete item.know_id;
        obj_data[item.id] = item;
    });
    // 根据sort_data排序，返回数据
    let _data = [];
    sort_data.forEach(item => {
        obj_data[item] && _data.push(obj_data[item]);
    });
    return _data;
};

const getKnowledgeExamTimes = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {knowledge_ids, type, name} = req.body;
    let required_fileds = [];
    if (!Array.isArray(knowledge_ids) || knowledge_ids.length === 0) {
        required_fileds.push('knowledge_ids');
    }
    if (!type) {
        required_fileds.push('type');
    }
    if (!name) {
        required_fileds.push('name');
    }
    if (required_fileds.length) {
        return responseWrapper.error('PARAMETERS_ERROR', `${required_fileds.join('、')}必传！`);
    }
    if (!['pro', 'city'].includes(type)) {
        return responseWrapper.error('PARAMETERS_ERROR', '请传入正确的type值！');
    }
    if (knowledge_ids.length > 200) {
        knowledge_ids = knowledge_ids.slice(0, 200);
    }
    let kids = knowledge_ids.join(',');
    const md5 = crypto.createHash('md5');
    let md5_result = md5.update(`${kids}:${type}:${name}`).digest('hex');
    let cacheKey = `kb_api:v2:knowledge_exam_times:batch:${md5_result}`;
    let proj = getFields('hfsfd');
    res.resMode = 'normal';
    knowledge_ids = knowledge_ids.map((id) => Number(id));
    let cond = {
        know_id: { $in: knowledge_ids },
        type,
        name
    };
    let knowledgeExamTimes = [];
    // redis崩溃后可以继续查询库
    try {
        knowledgeExamTimes = await rediser.get(cacheKey);
        if (knowledgeExamTimes) {
            return responseWrapper.succ(knowledgeExamTimes);
        }
    } catch (error) {
        Logger.error(error);
    }
    try {
        knowledgeExamTimes = await db.collection('knowledge_exam_times').find(cond).project(proj).toArray();
        if (knowledgeExamTimes.length > 0) {
            knowledgeExamTimes = sortKnowledgeExamTimes(knowledge_ids, knowledgeExamTimes);
            rediser.set(cacheKey, knowledgeExamTimes, validCacheTime);
        } else {
            rediser.set(cacheKey, [], unvalidCacheTime);
        }
        return responseWrapper.succ(knowledgeExamTimes);
    } catch (error) {
        Logger.error(error);
        responseWrapper.error('HANDLE_ERROR');
    }
};

module.exports = {
    getKnowledgeExamTimes
};
