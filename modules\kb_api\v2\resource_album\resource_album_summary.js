const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const { ObjectId } = require('mongodb');

const { createResourceAlbumSummaryValidator, updateResourceAlbumSummaryValidator } = require('./schema/resource_album_summary');

/**
 * 创建资源专辑合集
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const createResourceAlbumSummary = async (req, res) => {
    res.resMode = 'normal';
    let resourceWrapper = new ResponseWrapper(res);
    let body = req.body;
    if (!createResourceAlbumSummaryValidator(body)) {
        return resourceWrapper.error('PARAMETERS_ERROR');
    }
    try {
        const dbs = (req.query.dbs || 'basic').split(',');
        body.dbs = (body.dbs || dbs).filter(db => dbs.includes(db) || dbs.includes('kbp'));

        body.ctime = new Date();
        body.utime = new Date();
        body.valid = 1;
        let result = await db.collection('resource_album_summary').insertOne(body);
        return resourceWrapper.succ({ id: result.insertedId.toString() });

    } catch (error) {
        return resourceWrapper.error('HANDLE_ERROR', error);
    }
};

/**
 * 更新资源专辑合集
 * @param {*} req
 * @param {*} res
 * @returns {ResponseWrapper}
 */
const updateResourceAlbumSummary = async (req, res) => {
    res.resMode = 'normal';
    let resourceWrapper = new ResponseWrapper(res);
    let body = req.body;
    if (!updateResourceAlbumSummaryValidator(body)) {
        return resourceWrapper.error('PARAMETERS_ERROR');
    }

    let { id, dbs = 'basic' } = req.query;
    if (!id) {
        return resourceWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');

    const query = {
        _id: new ObjectId(id),
        dbs: { $in: dbs }
    };

    // 非kbp平台，不能更新 dbs, valid
    if (!dbs.includes('kbp')) {
        delete body.dbs;
        delete body.valid;
        query.valid = 1;
    }

    body.utime = new Date();
    try {
        let resourceAlbumSummary = await db.collection('resource_album_summary').findOne(query, { fields: { _id: 1 } });
        if (!resourceAlbumSummary) {
            return resourceWrapper.error('NULL_ERROR', '资源未找到');
        }
        await db.collection('resource_album_summary').updateOne({ _id: new ObjectId(id) }, { $set: body });
        return resourceWrapper.succ({ id });
    } catch (error) {
        return resourceWrapper.error('HANDLE_ERROR', error);
    }

};

/**
 * 删除资源专辑合集
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const deleteResourceAlbumSummary = async (req, res) => {
    res.resMode = 'normal';
    let resourceWrapper = new ResponseWrapper(res);
    let { id, dbs = 'basic' } = req.query;
    if (!id) {
        return resourceWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');
    const query = {
        _id: new ObjectId(id),
        dbs: { $in: dbs },
        valid: 1,
    };
    try {
        let resourceAlbumSummary = await db.collection('resource_album_summary').findOne(query, { fields: { _id: 1, dbs: 1 } });
        if (!resourceAlbumSummary) {
            return resourceWrapper.error('NULL_ERROR', '资源未找到');
        }
        resourceAlbumSummary.dbs = resourceAlbumSummary.dbs.filter(db => !dbs.includes(db));
        resourceAlbumSummary.utime = new Date();
        await db.collection('resource_album_summary').updateOne({ _id: new ObjectId(id) }, { $set: resourceAlbumSummary });
        return resourceWrapper.succ({ id });
    } catch (error) {
        return resourceWrapper.error('HANDLE_ERROR', error);
    }
};


/**
 * 获取资源专辑合集详情
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const getResourceAlbumSummary = async (req, res) => {
    res.resMode = 'normal';
    let responseWrapper = new ResponseWrapper(res);
    let { id, dbs = 'basic' } = req.query;
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');
    const query = {
        _id: new ObjectId(id),
        dbs: { $in: dbs },
        valid: 1,
    };

    try {
        let resourceAlbumSummary = await db.collection('resource_album_summary').findOne(query);
        if (!resourceAlbumSummary) {
            return responseWrapper.error('NULL_ERROR', '资源未找到');
        }
        return responseWrapper.succ(resourceAlbumSummary);
    } catch (error) {
        return responseWrapper.error('HANDLE_ERROR', error);
    }
};


/**
 * 获取资源专辑合集列表
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const getResourceAlbumSummaryList = async (req, res) => {
    res.resMode = 'normal';
    let responseWrapper = new ResponseWrapper(res);
    const sort = { ctime: -1 };
    let { limit = 10, offset = 0, valid = 1, dbs = 'basic' } = req.query;
    limit = +limit;
    offset = +offset;
    if (limit !== limit || offset !== offset) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');
    const query = {
        dbs: { $in: dbs },
        valid: 1,
    };

    if (dbs.includes('kbp')) {
        query.valid = valid;
    }

    try {
        let resourceAlbumSummaryList = await db.collection('resource_album_summary').find(query).sort(sort).skip(offset).limit(limit).toArray();
        if (!resourceAlbumSummaryList || resourceAlbumSummaryList.length === 0) {
            return responseWrapper.error('NULL_ERROR', '无资源');
        }
        return responseWrapper.succ(resourceAlbumSummaryList);
    } catch (error) {
        return responseWrapper.error('HANDLE_ERROR', error);
    }
};


module.exports = {
    createResourceAlbumSummary,
    updateResourceAlbumSummary,
    deleteResourceAlbumSummary,
    getResourceAlbumSummary,
    getResourceAlbumSummaryList
};