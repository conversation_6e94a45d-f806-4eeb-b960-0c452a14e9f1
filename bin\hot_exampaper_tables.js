const MongoClient = require('mongodb').MongoClient;
const _ = require('underscore');
const ObjectID = require('mongodb').ObjectID;

const dbUrl = '****************************************************************';

const mapDiff = (difficult) => {
    let diff = Math.floor(difficult / 2);
    switch (diff){
        case 5:
            return '困难';
        case 4:
            return '困难';
        case 3:
            return '较难';
        case 2:
            return '中等';
        case 1:
            return '较易';
        default:
            return '容易';
    }
};

const _exampaper2table = async (exampaper, db) => {
	let retobj = {};
	retobj.name = exampaper.name;
	retobj.period = exampaper.period;
	retobj.subject = exampaper.subject;
	retobj.province = exampaper.province;
	retobj.type = exampaper.type;
    retobj.grade = exampaper.grade;
    retobj.year = exampaper.year || exampaper.to_year;
    retobj.relevance_id = exampaper._id || exampaper.id;
    retobj.relevance_type = 'exampaper';
    retobj.blocks = [];
    let questionIds = _.flatten(_.pluck(exampaper.blocks, 'questions'));
    let questions = await db.collection('question').find({_id: {$in: questionIds}}).project({
        type: 1,
        period: 1,
        subject:1,
        difficulty: 1,
        knowledges: 1,
        score: 1
    }).toArray();
    questions = _.object(_.pluck(questions, '_id'), questions);
    
	_.each(exampaper.blocks, function(block){
		try {
			let b = {
				name: block.title
			};		

			b.questions = _.map(block.questions, function(id){
                let question = questions[id];
                if (!!question === false) {
                    return {};
                }
				return {
					type: question.type,
					period: question.period,
					subject: question.subject,
					difficulty: mapDiff(parseInt((1- question.difficulty) * 5) + 1),
					knowledges: question.knowledges,
					score: Math.floor(block.score / block.questions.length)
				};
            });
            b.type = b.questions[0].type;
			retobj.blocks.push(b);
		} catch (err){
			// retobj.blocks.push({});
		}
    });
    // console.log(JSON.stringify(retobj));
	return retobj;
};

const run = async (dbUrl) => {
  try {
    const client = await MongoClient.connect(dbUrl);
    const db = client.db('kb');
    await db.collection('tw_specification').updateMany({
        hot: true
    }, {
        $unset: {
            hot: 1
        }
    });
    let exampapers = await db.collection('exampaper').find({
        to_year: {$in: [2015, 2016, 2017, 2018]}
    }).project({
      period: 1,
      subject: 1,
      type: 1,
      grade: 1,
      to_year: 1,
    }).sort({
        view_times: -1
    }).limit(50000 * 2).toArray();
    exampapers = _.filter(exampapers, x => {
        return !!x && x.period && x.type && x.subject && x.grade;
    });
    exampapers = _.groupBy(exampapers, (x) => `${x.period}-${x.subject}-${x.type}-${x.grade}`);
    for (let i in exampapers) {
      if (exampapers.hasOwnProperty(i)) {
        //根据to_year排序
        exampapers[i].sort(function (a, b) {
            return b.to_year - a.to_year;
        });
        if (i.indexOf('上') >= 0 || i.indexOf('下') >= 0) {
            exampapers[i] = exampapers[i].slice(0, 1);
        } else {
            exampapers[i] = exampapers[i].slice(0, 2);
        }
      }
    }
    let exampaperIds = _.pluck(_.flatten(_.values(exampapers)), '_id');
    let tables = await db.collection('tw_specification').find({
      relevance_id: {
        $in: exampaperIds
      },
      relevance_type: 'exampaper'
    }).toArray();
    // 现有细目表
    let tableRefIds = _.pluck(tables, 'relevance_id');
    let nulltableIds = _.filter(exampaperIds, x => tableRefIds.indexOf(x) < 0);
    let newTables = [];
    let tExampapers = await db.collection('exampaper').find({_id: {$in: nulltableIds}}).toArray();
    for (let ix in tExampapers) {
        if (tExampapers.hasOwnProperty(ix)) {
            let exampaper = tExampapers[ix];
            let tb = await _exampaper2table(exampaper, db);
            tb.view_times = 0;
            tb._id = new ObjectID();
            tb.hot = true;
            newTables.push(tb);
        }
    }
    if (newTables.length > 0) {
        await db.collection('tw_specification').insertMany(newTables);
    }
    
    tables = tables.concat(newTables);
    if (tables.length > 0) {
        await db.collection('tw_specification').updateMany({
            _id: {$in: _.pluck(tables, '_id')}}, {$set: {
                hot: true,
            }
        });
    }
    
    client.close();
  } catch (err) {
    console.log(err.stack);
  }
};

run(dbUrl);
