/**
 * 知识点映射 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 9500,
    app_key: 'iyunxiao_kbplat180305',
};

describe('知识点映射', function () {
    describe('待映射知识点列表', function () {
        it('/kb_api/v2/contents', async function () {
            let pathname = `/kb_api/v2/contents`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
           
            let result = await request.get(url);
            console.log(result.body);
            assert.ok(Array.isArray(result.body));
            assert.ok(result.body.length === 5);
        });
    });


});
