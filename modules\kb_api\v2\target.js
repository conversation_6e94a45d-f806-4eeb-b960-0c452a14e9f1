let Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const counter = require('../../common/utils/counter');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const schemaElementCategory = require('./schemas/target');
const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const _ = require('lodash');

// 数据库集合名
const colElementCategory = 'target_category';
const colElement = 'target';

const getDataTemplate = (body) => ({
    period: body.period,
    subject: body.subject,
    name: body.name,
    children: body.children,
});

// 判断是否已存在该叠加要素类别或者叠加要素
const getExist = async (data, colName) => {
    let cond = {
        name: data.name,
        period: data.period,
        subject: data.subject,
        invalid: 0
    };
    let isExist = await db.collection(colName).findOne(cond);
    if (isExist && isExist._id !== data.id) {
        return {
            status: true,
            name: isExist.name,
            id: isExist._id
        };
    }
    return {status: false};
};

// 判断审核通过的叠加要素类别或者叠加要素是否存在
const validateData = async (data) => {
    let exist = {
        element_category: [],
        element: []
    };
    // 类别是否存在重名
    let elementCategoryExist = await getExist(data, colElementCategory);
    if (elementCategoryExist.status) {
        exist.element_category = [elementCategoryExist.name];
    }
    // 类别下的children是否存在重名
    let children = data.children;
    if (Array.isArray(children) && children.length) {
        let se_children = [];
        let childrenToObj = {};
        for (let item of children) {
            se_children.push(item.name);
            // 如果已存在delete的，则不能被覆盖
            childrenToObj[item.name] =  childrenToObj[item.name] === 'delete' ? 'delete' : item.operation_type;
        }
        let cond = {
            name: {$in: se_children},
            period: data.period,
            subject: data.subject,
            invalid: 0
        };
        let proj = {
            _id: 1,
            name: 1
        };
        // 获取库中已存在的name集合
        let result = await db.collection(colElement).find(cond, proj).toArray();
        if (result.length) {
            // 求差集，得到库中已存在的name
            let diff = _.differenceBy(result.map(item => ({id: item._id, name: item.name})), children, 'id');
            if (diff.length) {
                diff = diff.filter(item => childrenToObj[item.name] !== 'delete');
                exist.element = diff.map(item => item.name);
            }
        }
    }
    if (exist.element_category.length || exist.element.length) {
        let msg;
        if (exist.element_category.length) {
            msg = `数据库中已存在类型：${exist.element_category.join('、')}`;
        }
        if (exist.element.length) {
            msg = `${msg ? msg + '；' : ''}数据库中已存在对象：${exist.element.join('、')}`;
        }
        return {
            status: true,
            exist,
            msg
        };
    }
    return {
        status: false
    };
};

const validateTargetCategoryExist = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {
        name,
        period,
        subject
    } = req.query;
    if (!name || !period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    try {
        let result = await getExist(req.query, colElementCategory);
        return responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

const validateTargetExist = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {
        name,
        period,
        subject
    } = req.query;
    if (!name || !period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    try {
        let result = await getExist(req.query, colElement);
        return responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

const isRepeatName = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let result = await validateData(req.body);
    return responseWrapper.succ(result);
};

const getTargetCategoryList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {
        period,
        subject,
        offset = 0,
        limit = 10,
        detail = false
    } = req.query;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    let cond = {
        period,
        subject,
        invalid: 0
    };
    offset = parseInt(offset);
    limit = parseInt(limit);
    let proj = {
        invalid: 0
    };
    if (detail !== true && detail !== 'true') {
        proj.children = 0;
    }
    try {
        let result = await db.collection(colElementCategory).find(cond, proj).skip(offset).limit(limit).sort({utime: -1}).toArray();
        let totalNum = await db.collection(colElementCategory).count(cond);
        for (let item of result) {
            item.id = item._id;
            delete item._id;
        }
        return responseWrapper.succ({total_num: totalNum, list: result});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e);
    }       
};

const getTargetCategoryDetail = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let {
        id
    } = req.params;
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    let cond = {
        _id: Number(id),
        invalid: 0
    };
    try {
        let result = await db.collection(colElementCategory).findOne(cond, {invalid: 0});
        if (result) {
            result.id = result._id;
            delete result._id;
            return responseWrapper.succ(result);
        }
        return responseWrapper.error('NULL_ERROR', `id：${id}不存在！`);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 处理叠加要素
const handleElement = async ({data, period, subject, now}) => {
    let newData = [];
    if (Array.isArray(data) && data.length) {
        for (let i = 0, l = data.length; i < l; i++) {
            switch (data[i].operation_type) {
                case 'add': {
                    const seq = await counter.nextSeq('target', 1);
                    data[i].id = seq[0];
                    let val = {
                        _id: seq[0],
                        name: data[i].name,
                        coefficient: data[i].coefficient,
                        period,
                        subject,
                        ctime: now,
                        utime: now,
                        invalid: 0
                    };
                    await db.collection(colElement).insertOne(val);
                    break;
                }
                case 'update': {
                    let setdata = {
                        name: data[i].name,
                        coefficient: data[i].coefficient,
                        utime: now,
                        invalid: 0
                    };
                    await db.collection(colElement).updateOne({_id : data[i].id},{$set : setdata});
                    break;
                }
                case 'delete': {
                    let setdata = {
                        invalid: 1,
                        utime: now
                    };
                    await db.collection(colElement).updateOne({_id : data[i].id},{$set : setdata});
                    break;
                }
                default:
                    break;
            }
            if (data[i].operation_type !== 'delete') {
                newData.push({
                    id: data[i].id,
                    name: data[i].name,
                    coefficient: data[i].coefficient
                });
            }
        }
    }
    return newData;
};

const postTargetCategory = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    let {period, subject} = body;
    let now = new Date();
    try {
        let data = getDataTemplate(body);
        // 格式校验
        if (!ajv.validate(schemaElementCategory.postElementCategory, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '格式错误');
        }
        // 验证是否重名
        let exist = await validateData(data);
        if (exist.status) {
            return responseWrapper.error('HANDLE_ERROR', exist.msg);
        }
        // 处理叠加要素
        data.children = await handleElement({
            data: data.children,
            period,
            subject,
            now
        });
        const seq = await counter.nextSeq('target_category', 1);
        data._id = seq[0];
        data.ctime = now;
        data.utime = now;
        data.invalid = 0;
        let result = await db.collection(colElementCategory).insertOne(data);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '新增出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

const updateTargetCategory = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    let {period, subject} = body;
    let id = parseInt(req.params.id);
    let now = new Date();
    try {
        let data = getDataTemplate(body);
        // 格式校验
        if (!ajv.validate(schemaElementCategory.postElementCategory, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '格式错误');
        }
        // 验证是否重名
        let exist = await validateData({id, ...data});
        if (exist.status) {
            return responseWrapper.error('HANDLE_ERROR', exist.msg);
        }
        // 处理叠加要素
        data.children = await handleElement({
            data: data.children,
            period,
            subject,
            now
        });
        let result = await db.collection(colElementCategory).updateOne({_id : id},{$set : {
            utime: now,
            name: data.name,
            children: data.children,
            invalid: 0
        }});
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '修改出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

const deleteTargetCategory = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = parseInt(req.params.id);
    let now = new Date();
    try {
        let data = await db.collection(colElementCategory).findOne({_id: id});
        if (data && Array.isArray(data.children)) {
            for (let item of data.children) {
                await db.collection(colElement).updateOne({_id: item.id}, {$set: {
                    utime: now,
                    invalid: 1
                }});
            }
        }
        let result = await db.collection(colElementCategory).updateOne({_id: id}, {$set: {
            utime: now,
            invalid: 1
        }});
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '删除出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

module.exports = {
    validateTargetCategoryExist,
    validateTargetExist,
    isRepeatName, // kbp_serv提交过来的数据是否存在重名
    getTargetCategoryList,
    getTargetCategoryDetail,
    postTargetCategory,
    updateTargetCategory,
    deleteTargetCategory
};
