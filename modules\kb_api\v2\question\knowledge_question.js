let Logger = require('../../../common/utils/logger');
let ResponseWrapper = require('../../../common/utils/response_wrapper');
let mongodber = require('../../../common/utils/mongodber');
let db = mongodber.use('KB');
let params_utils = require('../../../common/utils/params.js');
let kb_api = require('../../config.js').kb_api['v2'];
let resource = require('../resource');
const utilsBiz = require('../utils.js');
const axios = require('axios');
const url = require('url');
const qs = require('qs');
const Config = require('config');
const { filter_fields } = require('./utils');
const mapExampaperName = require('../exampaper/mapExampaperName');
const filterExampaperName = require('../exampaper/filterExampaperName');

/*
 * 函数描述：获取试题筛选项数据
 * URI: /kb_api/v2/questions/filters2/
 * Method: GET
 */
async function filters2(req, res) {
    req.query.resource = 'question';
    return await resource.filters2(req, res);
}

// 获取所有标签名
const getAllTagNamesArr = (resultArr) => {
    let allTagNameArr = [];
    if (resultArr && Array.isArray(resultArr)) {
        for (let result of resultArr) {
            if (result && result.tag_type && Array.isArray(result.values)) {
                let tagType = result.tag_type;
                for (let value of result.values) {
                    if (value.name && (tagType !== 'cascade') && !allTagNameArr.includes(value.name)) {
                        allTagNameArr.push(value.name);
                    }
                    if (value.sub_values && Array.isArray(value.sub_values)) {
                        for (let sub_value of value.sub_values) {
                            if (sub_value && !allTagNameArr.includes(sub_value)) {
                                allTagNameArr.push(sub_value);
                            }
                        }
                    }
                }
            }
        }
    }
    return allTagNameArr;
};

// 知识点试题的查询mongodb条件参数
const knowledgeQuestionsParams = (params) => {
    let cond = {};
    // 知识点id
    if (Array.isArray(params.knowledge_ids) && params.knowledge_ids.length > 0) {
        if (params.knowledge_ids.length === 1) {
            cond['knowledges.id'] = params.knowledge_ids[0];
        } else {
            cond['knowledges.id'] = { $all: params.knowledge_ids };
        }
    }
    // 类型
    if (params.category === '常考题') {
        cond['is_final'] = { $in: [0, null] };
        cond['elite'] = { $in: [-1, 0, null] };
    } else if (params.category === '压轴题') {
        cond['is_final'] = 1;
    } else if (params.category === '名校精品题') {
        cond['elite'] = 1;
    }

    if (+params.elite === -1) { // 未遍题
        cond['elite'] = { $in: [-1, null] };
    } else if (+params.elite === 0) { // 非精编
        cond['elite'] = 0;
    } else if (+params.elite === 1) { // 精编
        cond['elite'] = 1;
    }

    // 试题标签
    if (Array.isArray(params.tag_ids) && params.tag_ids) {
        let tagIdArr = params.tag_ids.map(function (obj) {
            return obj.id;
        });
        let allTagNamesArr = getAllTagNamesArr(params.tag_ids);
        let eleMatch = {
            id: { $in: tagIdArr },
            $or: [{ 'values.name': { $in: allTagNamesArr } }, { 'values.sub_values': { $in: allTagNamesArr } }]
        };
        cond['type_tags'] = { $elemMatch: eleMatch };
    }
    if (params.count_sub_ques) {
        cond['tags.count_sub_ques'] = params.count_sub_ques;
    }
    if (params.count_options) {
        cond['tags.count_options'] = params.count_options;
    }
    if (params.grade) {
        cond['tags.grades'] = params.grade;
    }
    if (params.year) {
        cond['year'] = params.year;
    }

    // 题型
    if (params.type) {
        cond['type'] = params.type;
    }
    // 难度 单选
    if (params.difficulty) {
        let difficulty = Array.from(params.difficulty);
        if (difficulty.length === 1) {
            let value = null;
            if (difficulty[0] === 1) {
                value = { $gte: 0.85 };
            } else if (difficulty[0] === 2) {
                value = { $gte: 0.7, $lt: 0.85 };
            } else if (difficulty[0] === 3) {
                value = { $gte: 0.55, $lt: 0.7 };
            } else if (difficulty[0] === 4) {
                value = { $gte: 0.35, $lt: 0.55 };
            } else if (difficulty[0] === 5) {
                value = { $lt: 0.35 };
            }
            if (value) {
                cond['difficulty'] = value;
            }
        }
    }
    cond['refer_exampapers'] = {
        $elemMatch: {}
    };
    // 来源
    if (params.exam_type) {
        //cond['refer_exampapers.0.category'] = params.exam_type;
        cond['refer_exampapers'].$elemMatch.category = params.exam_type;
    }
    // 省
    if (params.province) {
        //cond['refer_exampapers.0.province'] = params.province;
        cond['refer_exampapers'].$elemMatch.province = params.province;
    }
    // 市
    if (params.city) {
        //cond['refer_exampapers.0.city'] = params.city;
        cond['refer_exampapers'].$elemMatch.city = params.city;
    }
    if (!params.exam_type && !params.province && !params.city) {
        delete cond['refer_exampapers'];
    }

    return cond;
};

// 知识点/细分/对象查询试题，通过搜索引擎的查询条件
const questionsBySearchParams = (params, api_key) => {
    let cond = {};
    if (params.period) {
        cond['period'] = params.period;
    }
    if (params.subject) {
        cond['subject'] = params.subject;
    }
    if (params.knowledges) {
        cond['knowledges'] = params.knowledges;
    }
    if (params.know_methods) {
        cond['know_methods'] = params.know_methods;
    }
    if (params.targets) {
        cond['targets'] = params.targets;
    }
    if (params.stem_statis) {
        cond['stem_statis'] = params.stem_statis;
    }
    if (params.type) {
        cond['type'] = params.type;
    }
    if (params.count_sub_ques) {
        cond['count_sub_ques'] = params.count_sub_ques;
    }
    if (params.filter_mkp) {
        cond['filter_mkp'] = params.filter_mkp;
    }
    if (params.count_options) {
        cond['count_options'] = params.count_options;
    }
    if (Array.isArray(params.tag_ids) && params.tag_ids) {
        cond['tag_ids'] = params.tag_ids;
    }
    if (params.difficulty) {
        cond['difficulty'] = params.difficulty;
    }
    if (params.category) {
        let category = {
            '压轴题': {
                is_final: 1
            },
            '名校精品题': {
                elite: 1
            },
            '精品题': {
                elite: 1
            }
        };
        let _category = category[params.category];
        if (_category) {
            cond['ques_cate'] = _category;
        }
        if ('地标题' === params.category) {
            cond['attach_is_landmark_question'] = [1, 2]; // 1：原题，2:相似题
        }
        if ('常考题' === params.category) {
            cond['is_changkao'] = 1;
        }
        if ('经典题' === params.category) {
            cond['attach_is_classic'] = 1;
        }
        if ('名优校题' === params.category) {
            cond['attach_is_famous'] = 1;
        }
    }
    if (Array.isArray(params.attach_is_landmark_question) && params.attach_is_landmark_question.length > 0) {
        cond['attach_is_landmark_question'] = params.attach_is_landmark_question;
    }
    if (params.exam_type) {
        cond['exam_cate'] = params.exam_type;
    }
    if (params.grade) {
        // 年级，可选,  英语学科专用，分上下册 （对应/se_kb/v2/filter/questions接口grades字段）
        cond['grades'] = params.grade;
    }
    if (params.grades) {
        // 目前命题平台用，支持多选用","分隔，不分上下册 （对应/se_kb/v2/filter/questions接口grade字段）
        cond['grade'] = params.grades;
    }
    if (Array.isArray(params.landmark_point_ids) && params.landmark_point_ids.length > 0) {
        cond['landmark_point_ids'] = params.landmark_point_ids;
    }
    if (params.attach_is_mobile) {
        cond['attach_is_mobile'] = params.attach_is_mobile;
    }
    if (params.dmp_example) {
        cond['dmp_example'] = params.dmp_example;
    }
    if (params.dmp_exercise) {
        cond['dmp_exercise'] = params.dmp_exercise;
    }
    if (params.dmp_homework) {
        cond['dmp_homework'] = params.dmp_homework;
    }
    if (params.research_example) {
        cond['research_example'] = params.research_example;
    }
    if (params.research_exercise) {
        cond['research_exercise'] = params.research_exercise;
    }
    if (params.teach_example) {
        cond['teach_example'] = params.teach_example;
    }
    if (params.teach_exercise) {
        cond['teach_exercise'] = params.teach_exercise;
    }
    if (params.teach_homework) {
        cond['teach_homework'] = params.teach_homework;
    }
    if (params.teach_evaluation) {
        cond['teach_evaluation'] = params.teach_evaluation;
    }
    if (params.teach_customize) {
        cond['teach_customize'] = params.teach_customize;
    }

    utilsBiz.setSeProvinces(cond, params);

    if (params.elite || parseInt(params.elite) === 0) {
        cond['elite'] = parseInt(params.elite);
    }

    if (params.inspect || parseInt(params.inspect) === 0) { // 质检题
        cond['inspect'] = parseInt(params.inspect);
    }

    if (params.year) {
        cond['year'] = params.year;
    }

    if (Array.isArray(params.attach) && params.attach.length > 0) {
        let map = {
            'is_mom': 'attach_is_mom',
            'is_new_gaokao': 'attach_is_new_gaokao',
            'is_classic': 'attach_is_classic',
            'is_famous': 'attach_is_famous'
        };
        for (let item of params.attach) {
            if (map[item]) {
                cond[map[item]] = 1;
            }
        }
    }

    cond['dbs'] = params.dbs || ['basic'];

    if (params.from) {
        let from = {
            kbp: 'kbp',
            mkp: 'mkp',
            drm: 'drm',
            'drm,drm_yuanpei': 'drm,drm_yuanpei',
            mt: 'mt',
            other: { '$ne': 'kbp,mkp,drm,drm_yuanpei,mt' }
        };
        let _from = from[params.from];
        if (_from) {
            cond['from'] = _from;
        }
    }
    let sort_by = {};
    if (['小升初真卷', '中考真卷', '高考真卷'].includes(params.exam_type)) {
        sort_by = {
            year: [
                { year: -1 },
                { ctime: -1 }
                // { attach_is_real: -1 },
                // { attach_is_famous: -1 },
                // { elite: -1 },
                // { knows_len: -1 },
                // { difficulty: -1 }
            ],
            cite_num: [
                { ncite: -1 },
                { year: -1 },
                { attach_is_real: -1 },
                { attach_is_famous: -1 },
                { elite: -1 },
                { knows_len: -1 },
                { difficulty: -1 }
            ],
            test_num: [
                { ntest: -1 },
                { year: -1 },
                { attach_is_real: -1 },
                { attach_is_famous: -1 },
                { elite: -1 },
                { knows_len: -1 },
                { difficulty: -1 }
            ],
            integrated: [
                { year: -1 },
                { ctime: -1 },
                { score: -1 }
            ],
            use_times: [
                { use_times: -1 },
                { year: -1 },
                { attach_is_real: -1 },
                { attach_is_famous: -1 },
                { elite: -1 },
                { knows_len: -1 },
                { difficulty: -1 }
            ]
        };
    } else {
        sort_by = {
            year: [
                { year: -1 },
                { ctime: -1 }
                // { rtime: -1 },
                // { attach_is_famous: -1 },
                // { elite: -1 },
                // { knows_len: -1 },
                // { difficulty: -1 }
            ],
            cite_num: [
                { ncite: -1 },
                { rtime: -1 },
                { year: -1 },
                { attach_is_famous: -1 },
                { elite: -1 },
                { knows_len: -1 },
                { difficulty: -1 }
            ],
            test_num: [
                { ntest: -1 },
                { rtime: -1 },
                { year: -1 },
                { attach_is_famous: -1 },
                { elite: -1 },
                { knows_len: -1 },
                { difficulty: -1 }
            ],
            integrated: [
                { year: -1 },
                { ctime: -1 },
                { score: -1 }
            ],
            use_times: [
                { use_times: -1 },
                { rtime: -1 },
                { year: -1 },
                { attach_is_famous: -1 },
                { elite: -1 },
                { knows_len: -1 },
                { difficulty: -1 }
            ],
            dmp_cite_desc: [
                { dmp_cite: -1 }
            ],
            dmp_cite_asc: [
                { dmp_cite: 1 }
            ]
        };
    }
    let _sort_by = sort_by[params.sort_by || 'year'];
    if (_sort_by) {
        cond['sort_by'] = _sort_by;
    }
    // 获取所属平台
    let platformArr = ['iyunxiao_mt', 'iyunxiao_tiku', 'iyunxiao_mkp', 'iyunxiao_kbp', 'iyunxiao_drm'];
    let platform = platformArr.find(item => api_key.indexOf(item) > -1) || 'iyunxiao_kbp';
    if (platform) {
        let platformFilter = {
            'iyunxiao_mt': 'mt',
            'iyunxiao_tiku': 'tiku',
            'iyunxiao_mkp': 'mkp',
            'iyunxiao_kbp': 'kbp',
            'iyunxiao_drm': 'drm'
        };
        platform = platformFilter[platform];
    }
    // 如果传了grades，并且全选的时候不加grades
    if (params.grades) {
        let result = gradeCompare(params.grades, params.period);
        if (result) {
            cond['sort_by'].push({ grades: 1 });
        }
    }
    //
    if (params.multi_or) cond['multi_or'] = params.multi_or;
    cond['platform'] = platform;
    cond[platform] = true;
    cond['offset'] = parseInt(params.offset) || 0;
    cond['limit'] = parseInt(params.limit) || 10;
    cond['set_mode'] = Object.assign({ knowledges: 'union', targets: 'union', methods: 'union' }, params['set_mode']);
    return cond;
};

// 知识点/细分/对象查询试题，通过搜索引擎的查询条件
const questionsBySearchParamsV2 = (params, api_key) => {
    let cond = {};
    if (params.query) { // 搜索关键字
        cond['query'] = params.query;
    }
    if (params.period) {
        cond['period'] = params.period;
    }
    if (params.subject) {
        cond['subject'] = params.subject;
    }
    if (params.knowledges) {
        cond['knowledges'] = params.knowledges;
    }
    if (params.knows) { // 知识点
        cond['knows'] = params.knows;
    }
    if (params.know_methods) {
        cond['know_methods'] = params.know_methods;
    }
    if (params.targets) {
        cond['targets'] = params.targets;
    }
    if (params.stem_statis) {
        cond['stem_statis'] = params.stem_statis;
    }
    if (params.type) {
        cond['type'] = params.type;
    }
    if (params.count_sub_ques) {
        cond['count_sub_ques'] = params.count_sub_ques;
    }
    if (params.filter_mkp) {
        cond['filter_mkp'] = params.filter_mkp;
    }
    if (params.count_options) {
        cond['count_options'] = params.count_options;
    }
    if (Array.isArray(params.tag_ids) && params.tag_ids) {
        cond['tag_ids'] = params.tag_ids;
    }
    if (params.difficulty) {
        cond['difficulty'] = params.difficulty;
    }
    if (params.category) {
        let category = {
            '压轴题': {
                is_final: 1
            },
            '名校精品题': {
                elite: 1
            },
            '精品题': {
                elite: 1
            }
        };
        let _category = category[params.category];
        if (_category) {
            cond['ques_cate'] = _category;
        }
        if ('地标题' === params.category) {
            cond['attach_is_landmark_question'] = [1, 2]; // 1：原题，2:相似题
        }
        if ('常考题' === params.category) {
            cond['is_changkao'] = 1;
        }
        if ('经典题' === params.category) {
            cond['attach_is_classic'] = 1;
        }
        if ('名优校题' === params.category) {
            cond['attach_is_famous'] = 1;
        }
    }
    if (Array.isArray(params.attach_is_landmark_question) && params.attach_is_landmark_question.length > 0) {
        cond['attach_is_landmark_question'] = params.attach_is_landmark_question;
    }
    // if (params.exam_type) {
    //     cond['exam_cate'] = params.exam_type;
    // }
    if (params.exam_cate) {
        cond['exam_cate'] = params.exam_cate;
    }
    if (params.grade) {
        // 年级，可选,  英语学科专用，分上下册 （对应/se_kb/v2/filter/questions接口grades字段）
        cond['grades'] = params.grade;
    }
    if (params.grades) {
        // 目前命题平台用，支持多选用","分隔，不分上下册 （对应/se_kb/v2/filter/questions接口grade字段）
        cond['grade'] = params.grades;
    }
    if (Array.isArray(params.landmark_point_ids) && params.landmark_point_ids.length > 0) {
        cond['landmark_point_ids'] = params.landmark_point_ids;
    }
    if (params.attach_is_mobile) {
        cond['attach_is_mobile'] = params.attach_is_mobile;
    }
    if (params.dmp_example) {
        cond['dmp_example'] = params.dmp_example;
    }
    if (params.dmp_exercise) {
        cond['dmp_exercise'] = params.dmp_exercise;
    }
    if (params.dmp_homework) {
        cond['dmp_homework'] = params.dmp_homework;
    }
    if (params.research_example) {
        cond['research_example'] = params.research_example;
    }
    if (params.research_exercise) {
        cond['research_exercise'] = params.research_exercise;
    }
    if (params.teach_example) {
        cond['teach_example'] = params.teach_example;
    }
    if (params.teach_exercise) {
        cond['teach_exercise'] = params.teach_exercise;
    }
    if (params.teach_homework) {
        cond['teach_homework'] = params.teach_homework;
    }
    if (params.teach_evaluation) {
        cond['teach_evaluation'] = params.teach_evaluation;
    }
    if (params.teach_customize) {
        cond['teach_customize'] = params.teach_customize;
    }

    if (params.provinces) {
        cond['provinces'] = params.provinces;
    }
    if (params.province) {
        cond['province'] = params.province;
    }
    if (params.city) {
        cond['city'] = params.city;
    }

    // utilsBiz.setSeProvinces(cond, params);

    if (params.elite || parseInt(params.elite) === 0) {
        cond['elite'] = parseInt(params.elite);
    }

    if (params.inspect || parseInt(params.inspect) === 0) { // 质检题
        cond['inspect'] = parseInt(params.inspect);
    }

    if (params.year) {
        cond['year'] = params.year;
    }

    if (Array.isArray(params.attach) && params.attach.length > 0) {
        let map = {
            'is_mom': 'attach_is_mom',
            'is_new_gaokao': 'attach_is_new_gaokao',
            'is_classic': 'attach_is_classic',
            'is_famous': 'attach_is_famous'
        };
        for (let item of params.attach) {
            if (map[item]) {
                cond[map[item]] = 1;
            }
        }
    }

    cond['dbs'] = params.dbs || ['basic'];

    if (params.from) {
        let from = {
            kbp: 'kbp',
            mkp: 'mkp',
            drm: 'drm',
            'drm,drm_yuanpei': 'drm,drm_yuanpei',
            mt: 'mt',
            other: { '$ne': 'kbp,mkp,drm,drm_yuanpei,mt' }
        };
        let _from = from[params.from];
        if (_from) {
            cond['from'] = _from;
        }
    }
    let sort_by = {};
    if (['小升初真卷', '中考真卷', '高考真卷'].includes(params.exam_type)) {
        sort_by = {
            year: [
                { year: -1 },
                { ctime: -1 }
                // { attach_is_real: -1 },
                // { attach_is_famous: -1 },
                // { elite: -1 },
                // { knows_len: -1 },
                // { difficulty: -1 }
            ],
            cite_num: [
                { ncite: -1 },
                { year: -1 },
                { attach_is_real: -1 },
                { attach_is_famous: -1 },
                { elite: -1 },
                { knows_len: -1 },
                { difficulty: -1 }
            ],
            test_num: [
                { ntest: -1 },
                { year: -1 },
                { attach_is_real: -1 },
                { attach_is_famous: -1 },
                { elite: -1 },
                { knows_len: -1 },
                { difficulty: -1 }
            ],
            integrated: [
                { year: -1 },
                { ctime: -1 },
                // { score: -1 }
            ],
            use_times: [
                { use_times: -1 },
                { year: -1 },
                // { attach_is_real: -1 },
                // { attach_is_famous: -1 },
                { elite: -1 },
                // { knows_len: -1 },
                { difficulty: -1 }
            ]
        };
    } else {
        sort_by = {
            year: [
                { year: -1 },
                { ctime: -1 }
                // { rtime: -1 },
                // { attach_is_famous: -1 },
                // { elite: -1 },
                // { knows_len: -1 },
                // { difficulty: -1 }
            ],
            cite_num: [
                { ncite: -1 },
                { rtime: -1 },
                { year: -1 },
                { attach_is_famous: -1 },
                { elite: -1 },
                { knows_len: -1 },
                { difficulty: -1 }
            ],
            test_num: [
                { ntest: -1 },
                { rtime: -1 },
                { year: -1 },
                { attach_is_famous: -1 },
                { elite: -1 },
                { knows_len: -1 },
                { difficulty: -1 }
            ],
            integrated: [
                { year: -1 },
                { ctime: -1 },
                // { score: -1 }
            ],
            use_times: [
                { use_times: -1 },
                // { rtime: -1 },
                { year: -1 },
                // { attach_is_famous: -1 },
                { elite: -1 },
                // { knows_len: -1 },
                { difficulty: -1 }
            ],
            dmp_cite_desc: [
                { dmp_cite: -1 }
            ],
            dmp_cite_asc: [
                { dmp_cite: 1 }
            ]
        };
    }
    let _sort_by = sort_by[params.sort_by || 'year'];
    if (_sort_by) {
        cond['sort_by'] = _sort_by;
    }
    // 获取所属平台
    let platformArr = ['iyunxiao_mt', 'iyunxiao_tiku', 'iyunxiao_mkp', 'iyunxiao_kbp', 'iyunxiao_drm'];
    let platform = platformArr.find(item => api_key.indexOf(item) > -1) || 'iyunxiao_kbp';
    if (platform) {
        let platformFilter = {
            'iyunxiao_mt': 'mt',
            'iyunxiao_tiku': 'tiku',
            'iyunxiao_mkp': 'mkp',
            'iyunxiao_kbp': 'kbp',
            'iyunxiao_drm': 'drm'
        };
        platform = platformFilter[platform];
    }
    // 如果传了grades，并且全选的时候不加grades
    if (params.grades) {
        let result = gradeCompare(params.grades, params.period);
        if (result) {
            cond['sort_by'].push({ grades: 1 });
        }
    }
    //
    if (params.multi_or) cond['multi_or'] = params.multi_or;
    cond['platform'] = platform;
    cond[platform] = true;
    cond['offset'] = parseInt(params.offset) || 0;
    cond['limit'] = parseInt(params.limit) || 10;
    cond['set_mode'] = Object.assign({ knowledges: 'union', targets: 'union', methods: 'union' }, params['set_mode']);
    return cond;
};

// 年级排序
const getGradeSort = function (data) {
    let grades = data || [];
    if (Array.isArray(grades) && grades.length) {
        const trans1 = {
            '一年级': 1,
            '二年级': 2,
            '三年级': 3,
            '四年级': 4,
            '五年级': 5,
            '六年级': 6,
            '七年级': 7,
            '八年级': 8,
            '九年级': 9,
            '高一': 10,
            '高二': 11,
            '高三': 12
        };
        grades = grades.map(i => trans1[i]).sort((a, b) => a - b);
        const trans2 = {
            1: '一年级',
            2: '二年级',
            3: '三年级',
            4: '四年级',
            5: '五年级',
            6: '六年级',
            7: '七年级',
            8: '八年级',
            9: '九年级',
            10: '高一',
            11: '高二',
            12: '高三'
        };
        grades = grades.map(i => trans2[i]);
        return Array.from(new Set(grades));
    } else {
        return [];
    }
};

const gradeCompare = (grade, period) => {
    let filter = {
        '高中': '高一,高二,高三',
        '初中': '七年级,八年级,九年级',
        '小学': '一年级,二年级,三年级,四年级,五年级,六年级'
    };
    let allGrade = filter[period];
    let condGrade = (getGradeSort(grade.split(','))).join(',');
    if (allGrade === condGrade) {
        return false;
    }
    return true;
};

// 查询出的字段
const getQuestionProject = () => {
    return {
        _id: 1,
        comment: 1,
        blocks: 1,
        description: 1,
        type: 1,
        knowledges: 1,
        period: 1,
        subject: 1,
        difficulty: 1,
        refer_exampapers: 1,
        refer_times: 1,
        use_type: 1,
        core_knowledges: 1,
        elite: 1,
        year: 1,
        reco_questions: 1,
        use_times: 1,
        type_tags: 1,
        tags: 1,
        ctime: 1,
        from: 1,
        elements: 1,
        attach: 1,
        inspect: 1,
        dmp_cite: 1,
        is_final:1,
        landmark_reco_questions: 1,
    };
};

/**
 * 查询试题列表 DMP
 * DMP v2.4
 * @param {*} req   - 具体参见接口文档 知识库API V2 / 试题 试卷 API
 * @param {*} res
 * @return {Promise<void>}
 */
async function knowledge_questions3(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let mobile_ready = req.body.mobile_ready;
    let fun_name = arguments.callee.name;
    try {
        let params = params_utils.create_params(req, kb_api[fun_name]);
        let cond = knowledgeQuestionsParams(params);
        let proj = getQuestionProject();
        // let coll; // 因question_mobile无索引，所以不支持直接从question_mobile查询
        // if (params.device === 'mobile') {
        //     coll = db.collection('question_mobile');
        // } else {
        // }
        let { offset, limit } = params;
        let { sortBy } = req.body;
        let coll = db.collection('question');
        let cursor = await coll.find(cond, { readPreference: 'secondaryPreferred' }).project({ _id: 1, knowledges: 1 })
            .sort(sortBy);
        // .skip(params.offset)
        // .limit(params.limit);
        let totalNum = await cursor.count();
        let questionsArr = await cursor.toArray();

        questionsArr = questionsArr.sort((a, b) => a.knowledges.length - b.knowledges.length)
            .slice(offset, offset + limit);

        let quesIds = questionsArr.map((ele) => ele._id);
        questionsArr = await coll.find({ _id: { $in: quesIds } }).project(proj).toArray();

        for (let question of questionsArr) {
            filter_fields(question, params.fields_type);
        }

        // if ((mobile_ready === true || mobile_ready === 'true') && questionsArr.length) {
        //     if (params.device === 'mobile') {
        //         for (let q of questionsArr) {
        //             q.mobile_ready = true;
        //         }
        //     } else {
        //         let mobiles = await db.collection('question_mobile').find({
        //             _id: { $in: quesIds },
        //         }, {
        //             readPreference: 'secondaryPreferred',
        //             fields: { _id: 1 },
        //         }).toArray();
        //         let mobileIds = mobiles.map(e => e._id);
        //         for (let q of questionsArr) {
        //             q.mobile_ready = (mobileIds.indexOf(q.id) >= 0);
        //         }
        //     }
        // }
        let result = {
            total_num: totalNum,
            questions: questionsArr
        };
        responseWrapper.succ(result);
    } catch (err) {
        Logger.error(err);
        responseWrapper.error('HANDLE_ERROR', err.message || err);
    }
}

// 通过试题id获取试题信息
const getQuestionsByQId = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let qids = req.query.qids;
        if (!qids) {
            return responseWrapper.error('PARAMETERS_ERROR', '缺少试题id');
        }
        let qidArr = qids.split(',');
        qidArr = qidArr.map(function (item) {
            return Number(item);
        });
        let proj = getQuestionProject();
        let cond = { _id: { $in: qidArr } };
        let questions = await db.collection('question').find(cond)
            .project(proj).toArray();
        for (let ques of questions) {
            filter_fields(ques);
        }
        responseWrapper.succ(questions);
    } catch (err) {
        Logger.error(err);
        responseWrapper.error('HANDLE_ERROR', err.message || err);
    }
};

async function questionsBySearch(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let params = params_utils.create_params(req, kb_api['question_by_search']);
        let cond = questionsBySearchParams(params, req.query.api_key);

        // if (!cond.knowledges && !cond.know_methods && !cond.targets && !cond.landmark_point_ids) {
        //     return responseWrapper.error('PARAMS_ERROR', '缺少知识点、对象、细分或地标点！');
        // }

        if (req.query.api_key.indexOf('tiku') > -1) {
            cond['serv_range'] = 'public';
            if (req.body.limit == 500){
                cond['limit'] = 500
            }
        }
        let uri = url.format({
            protocol: Config.SE_API_SERVER.protocol,
            hostname: Config.SE_API_SERVER.hostname,
            port: Config.SE_API_SERVER.port,
            pathname: '/se_kb/v2/filter/questions',
            search: qs.stringify({
                api_key: Config.SE_API_SERVER.appKey
            })
        });
        console.log(JSON.stringify(cond));
        let result = await axios.post(uri, cond, {
            timeout: 10000
        });
        let resultData = result.data;
        if (resultData.code === 0) {
            let searchData = resultData.data.datas || [];
            let proj = getQuestionProject();
            if (Array.isArray(params.landmark_point_ids) && params.landmark_point_ids.length > 0) {
                proj.landmark_point_ids = 1;
            }
            let questions = await db.collection('question').find({ _id: { '$in': searchData } }, { readPreference: 'secondaryPreferred' }).project(proj).toArray();
            questions = utilsBiz.to_sort(searchData, questions, '_id');
            for (let ques of questions) {
                let blocks = ques.blocks || {};
                // 是否有解答
                let solutions = blocks.solutions || [];
                for (let solu in solutions) {
                    if (solutions[solu] && typeof solutions[solu] == 'string' && solutions[solu].length > 30){
                        ques['has_solutions'] = 1
                        break
                    }
                }
                // 是否有解析
                let explanations = blocks.explanations || [];
                for (let exp in explanations) {
                    if (explanations[exp] && typeof explanations[exp] == 'string' && explanations[exp].length > 30){
                        ques['has_explanations'] = 1
                        break
                    }
                }
                filter_fields(ques, 'stem2');
                if (ques.refer_exampapers && ques.refer_exampapers.length > 0) { // 模糊试卷名称
                    for (let exam of ques.refer_exampapers) {
                        exam.vague_name = filterExampaperName(exam);
                        exam.name = mapExampaperName(exam, req.query);
                    }
                }
                if (req.body.request_landmark && Array.isArray(ques.landmark_point_ids) && ques.landmark_point_ids.length > 0) {
                    let landmarkCond = {
                        _id: { $in: ques.landmark_point_ids }
                    };
                    let proj = { name: 1, invalid: 1 };
                    let rawData = await db.collection('landmark_point').find(landmarkCond).project(proj).toArray();
                    let landmarkPoints = [];
                    for (let data of rawData) {
                        if (data.invalid === 0) {
                            delete data.invalid;
                            data['id'] = data._id;
                            delete data._id;
                            landmarkPoints.push(data);
                        }
                    }
                    ques['landmark_points'] = landmarkPoints;
                }
            }
            responseWrapper.succ({
                total_num: resultData.data.total,
                questions: questions
            });
        } else {
            responseWrapper.succ({
                total_num: 0,
                questions: []
            });
        }
    } catch (err) {
        Logger.error(err);
        responseWrapper.error('HANDLE_ERROR', err.message || err);
    }
}

async function questionsBySearchV2(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let params = params_utils.create_params(req, kb_api['question_by_search_v2']);
        let cond = questionsBySearchParamsV2(params, req.query.api_key);

        if (req.query.api_key.indexOf('tiku') > -1) {
            cond['serv_range'] = 'public';
            // if (req.body.limit == 500){
            //     cond['limit'] = 500
            // }
        }
        let uri = url.format({
            protocol: Config.SE_API_SERVER.protocol,
            hostname: Config.SE_API_SERVER.hostname,
            port: Config.SE_API_SERVER.port,
            pathname: '/se_kb/v2/search/questions',
            search: qs.stringify({
                api_key: Config.SE_API_SERVER.appKey
            })
        });
        let result = await axios.post(uri, cond, { timeout: 10000 });
        let resultData = result.data;
        if (resultData.code === 0) {
            let searchData = (resultData.data.docs || []).map(e => e.id);
            let proj = getQuestionProject();
            if (Array.isArray(params.landmark_point_ids) && params.landmark_point_ids.length > 0) {
                proj.landmark_point_ids = 1;
            }
            let questions = await db.collection('question').find({ _id: { '$in': searchData } }, { readPreference: 'secondaryPreferred' }).project(proj).toArray();
            questions = utilsBiz.to_sort(searchData, questions, '_id');
            for (let ques of questions) {
                let blocks = ques.blocks || {};
                // 是否有解答
                let solutions = blocks.solutions || [];
                for (let solu in solutions) {
                    if (solutions[solu] && typeof solutions[solu] == 'string' && solutions[solu].length > 30){
                        ques['has_solutions'] = 1
                        break
                    }
                }
                // 是否有解析
                let explanations = blocks.explanations || [];
                for (let exp in explanations) {
                    if (explanations[exp] && typeof explanations[exp] == 'string' && explanations[exp].length > 30){
                        ques['has_explanations'] = 1
                        break
                    }
                }
                filter_fields(ques, 'stem2');
                if (ques.refer_exampapers && ques.refer_exampapers.length > 0) { // 模糊试卷名称
                    for (let exam of ques.refer_exampapers) {
                        exam.vague_name = filterExampaperName(exam);
                        exam.name = mapExampaperName(exam, req.query);
                    }
                }
                if (req.body.request_landmark && Array.isArray(ques.landmark_point_ids) && ques.landmark_point_ids.length > 0) {
                    let landmarkCond = {
                        _id: { $in: ques.landmark_point_ids }
                    };
                    let proj = { name: 1, invalid: 1 };
                    let rawData = await db.collection('landmark_point').find(landmarkCond).project(proj).toArray();
                    let landmarkPoints = [];
                    for (let data of rawData) {
                        if (data.invalid === 0) {
                            delete data.invalid;
                            data['id'] = data._id;
                            delete data._id;
                            landmarkPoints.push(data);
                        }
                    }
                    ques['landmark_points'] = landmarkPoints;
                }
            }
            responseWrapper.succ({
                total_num: resultData.data.total,
                questions: questions
            });
        } else {
            responseWrapper.succ({
                total_num: 0,
                questions: []
            });
        }
    } catch (err) {
        Logger.error(err);
        responseWrapper.error('HANDLE_ERROR', err.message || err);
    }
}

module.exports = {
    knowledge_questions3: knowledge_questions3,
    filters2: filters2,
    getQuestionsByQId,
    questionsBySearch,
    questionsBySearchV2,
};
