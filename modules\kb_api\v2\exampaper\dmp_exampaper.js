const _ = require('lodash');
const rediser = require('../../../common/utils/rediser');
const logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const _diff = require('../question/utils').transferDiffDecimal2Five;
const OneDaySeconds = 24 * 60 * 60;
const CacheFreshTime = 1000 * 60 * 30;

const _cacheKey = (period, subject) => `kb_api:v2:dmp_exampaper:getKnowledgeMap:${period}_${subject}`;
const _lockKey = (period, subject) => `kb_api:v2:dmp_exampaper:getKnowledgeMap:${period}_${subject}:lock`;

/**
 * 获取知识树
 * @param {*} period 
 * @param {*} subject 
 */
const _getKnowledgeMap = async (period, subject) => {
    let knowledgeMap = {};

    let catalog = await db.collection('catalog').findOne({ _id: 'knowledge_tree' });
    let trees = [];
    for (let p of catalog.periods) {
        if (p.name === period) {
            for (let s of p.subjects) {
                if (s.name === subject) {
                    trees = s.trees;
                    break;
                }
            }
            if (trees) break;
        }
    }
    let tree = await db.collection('knowledge_tree').findOne({ _id: trees[0].id });

    let nodeIds = [];
    let order = 0;
    let nodeMap = {};
    const traverse = (nodes, parentId) => {
        let children = {};

        for (let i = 0; i < nodes.length; i++) {
            nodes[i].order = ++order;
            nodes[i].parentId = parentId;
            nodeMap[nodes[i].id] = nodes[i];

            nodeIds.push(nodes[i].id);
            children[nodes[i].name] = { order };
            if (nodes[i].nodes) {
                children[nodes[i].name].children = traverse(nodes[i].nodes, nodes[i].id);
            }
        }
        return children;
    };

    knowledgeMap.parentMap = traverse(tree.nodes, order);

    const _parents = (node) => {
        let p = [];
        p.push(node.name);
        let parentId = node.parentId;
        while (parentId) {
            node = nodeMap[parentId];
            p.push(node.name);
            parentId = node.parentId;
        }

        if (p.length > 2) {
            p = p.slice(p.length - 2);
        } else if (p.length === 1) {
            p[1] = p[0];
        }
        return p.reverse();
    };

    let hasKnowledgesNodes = [];
    let chapters = await db.collection('knowledge_tree_chapter').find({
        _id: { $in: nodeIds }
    }).project({ path: 0, utime: 0, ctime: 0 }).toArray();
    for (let e of chapters) {
        nodeMap[e._id].name = e.name;
        if (e.knowledges) {
            hasKnowledgesNodes.push(e);
        }
    }

    for (let e of hasKnowledgesNodes) {
        let node = nodeMap[e._id];
        if (!node) continue;
        for (let i = 0; i < e.knowledges.length; i++) {
            knowledgeMap[e.knowledges[i].id] = {
                name: e.knowledges[i].name,
                order: (node.order * 1000 + i),
                path: _parents(node),
            };
        }
    }

    return knowledgeMap;
};

const getCachedTree = async (period, subject) => {
    let cacheKey = _cacheKey(period, subject);
    let lockKey = _lockKey(period, subject);
    let cacheData = await rediser.get(cacheKey);

    if (!cacheData) {
        let tree = await _getKnowledgeMap(period, subject);
        rediser.set(cacheKey, {
            data: tree,
            utime: new Date().getTime(),
        }, OneDaySeconds);
        return tree;
    }

    const reFresh = async () => {
        let lock = await rediser.get(lockKey);
        if (lock) return;
        let setLock = await rediser.getCache().set(lockKey, 1, 'EX', 5, 'NX');
        if (!setLock) return;
        let tree = await _getKnowledgeMap(period, subject);
        tree.lostKids = cacheData.data.lostKids || {};
        await rediser.set(cacheKey, {
            data: tree,
            utime: new Date().getTime(),
        }, OneDaySeconds);
        let result = await rediser.del(lockKey);
        return result;
    };

    if (new Date() - cacheData.utime > CacheFreshTime) {
        reFresh().catch(err => logger.error(err));
    }

    return cacheData.data;
};

// 缓存知识树上没有的知识点
const updateLostKids = async (period, subject, lostKids) => {
    let cacheKey = _cacheKey(period, subject);
    let lockKey = _lockKey(period, subject);
    let cacheData = await rediser.get(cacheKey);
    if (cacheData) {
        let setLock;
        for (let i = 1; i <= 2; i++) {
            let lock = await rediser.get(lockKey);
            if (!lock) {
                setLock = await rediser.getCache().set(lockKey, 1, 'EX', 5, 'NX');
                if (setLock) break;
            }
            await new Promise((resolve, reject) => {
                setTimeout(resolve, 1000, 1);
            });
        }
        if (setLock) {
            let _lostKids = cacheData.data.lostKids || {};
            for (let e of lostKids) {
                _lostKids[e.id] = e.path;
            }
            cacheData.data.lostKids = _lostKids;
            cacheData.utime = new Date().getTime();
            await rediser.set(cacheKey, cacheData, OneDaySeconds);
            rediser.del(lockKey);
        }
    }
};

const getParentKnowledgeMap = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);

    let { period, subject } = req.query;

    if (!period || !subject) return resWrapper.error('PARAMETERS_ERROR');

    let ret = await getCachedTree(period, subject);

    resWrapper.succ(ret.parentMap);
};

const _getQuestionsOfExampaper = async (blocks = []) => {
    let _questionIds = [];
    let scoreMap = {};
    for (let i = 0; i < blocks.length; i++) {
        let block = blocks[i];
        let questions = block.questions || [];
        if (!questions.length) continue;
        let score = Number((block.score / questions.length).toFixed(1)) || 0;
        for (let j = 0; j < questions.length; j++) {
            let ques = questions[j];
            let qid = '', _score = score;
            if (_.isObject(ques) && ques.id) {
                qid = ques.id;
                _score = ques.score || score;
            } else {
                qid = ques;
            }
            _questionIds.push(qid);
            scoreMap[qid] = _score;
        }
    }

    let questions = await db.collection('question').find({ _id: { $in: _questionIds } }, {
        readPreference: 'secondaryPreferred'
    }).project({
        type: 1, difficulty: 1, knowledges: 1
    }).toArray();

    let questionMap = {};
    for (let q of questions) {
        questionMap[q._id] = q;
        q.score = _.round(scoreMap[q._id], 1);
    }

    return _questionIds.map(id => questionMap[id]).filter(e => e);
};

// 试卷细目分析，包括（试题题型、难度、分值、一二三级知识点、一级知识点分值、二级知识点分值/考频）
const _getExampaperStatistics = async ({ blocks, period, subject }) => {
    let questions = await _getQuestionsOfExampaper(blocks);
    let knowledgeMap = await getCachedTree(period, subject);
    let lostKids = [];

    // 知识树上没有的知识点，从knowledge_tree_chapter中获取包含该知识点的知识树上的路径
    const _getKnowledgePath = async (kid) => {
        if (knowledgeMap.lostKids && knowledgeMap.lostKids[kid]) {
            return knowledgeMap.lostKids[kid];
        }
        let path = [];
        let chapter = await db.collection('knowledge_tree_chapter').findOne({
            'knowledges.id': kid,
        });
        if (chapter) {
            let _path = chapter.path || '';
            // 如：高中_物理_综合知识库_电磁学_交变电流
            let [, , , first, second] = _path.split('_');
            let knowledge = chapter.knowledges.find(e => e.id === kid);
            let third = knowledge.name;
            second = second || first;
            path = [first, second, third];
        }
        lostKids.push({ id: kid, path });
        return path;
    };

    const defaultPoint = { order: 100000000, path: ['', ''] };
    // 二级知识点考频：{[二级知识点名字]: number}
    let examTimesMap = {};
    let knowledgeModuleMap = {};
    for (let q of questions) {
        q.knowledges = q.knowledges || [];
        let [firstLevel, secondLevel, thirdLevel] = [[], [], []];
        let kids = q.knowledges.map(e => e.id);
        if (kids.length > 1) kids.sort((a, b) => {
            return (knowledgeMap[a] || defaultPoint).order - (knowledgeMap[b] || defaultPoint).order;
        });

        let averageScore = kids.length && q.score ? (q.score / kids.length) : 0;

        for (let i = 0; i < kids.length; i++) {
            let path;
            if (knowledgeMap[kids[i]]) {
                path = [...knowledgeMap[kids[i]].path, knowledgeMap[kids[i]].name];
            } else {
                path = await _getKnowledgePath(kids[i], knowledgeMap);
            }
            let [first, second, third] = path;
            if (!third) continue;

            !~firstLevel.indexOf(first) && firstLevel.push(first);
            thirdLevel.push(third);

            if (!~secondLevel.indexOf(second)) {
                secondLevel.push(second);
                examTimesMap[second] ? (examTimesMap[second]++) : (examTimesMap[second] = 1);
            }

            if (knowledgeModuleMap[first]) {
                knowledgeModuleMap[first][second] = (knowledgeModuleMap[first][second] || 0) + averageScore;
            } else {
                knowledgeModuleMap[first] = { [second]: averageScore };
            }
        }

        q.first_knowledges = firstLevel;
        q.second_knowledges = secondLevel;
        q.third_knowledges = thirdLevel;

        q.difficulty = _diff(q.difficulty);
        q.id = q._id;
        delete q._id;
        delete q.knowledges;
    }
    if (lostKids.length) {
        updateLostKids(period, subject, lostKids).catch(err => logger.error(err));
    }
    let parentMap = knowledgeMap.parentMap;
    let defaultParent = { order: 100000, children: {} };
    let firstKnowledges = Object.keys(knowledgeModuleMap).sort((a, b) => {
        return (parentMap[a] || defaultParent).order - (parentMap[b] || defaultParent).order;
    });

    let knowledgeModules = [];
    for (let first of firstKnowledges) {
        let secondMap = (parentMap[first] || defaultParent).children;
        let secondKnowledges = Object.keys(knowledgeModuleMap[first]);
        secondKnowledges.sort((a, b) => {
            return (secondMap[a] || defaultParent).order - (secondMap[b] || defaultParent).order;
        });

        let knowledgeModule = { name: first };
        knowledgeModule.children = secondKnowledges.map(second => ({
            name: second,
            score: _.round(knowledgeModuleMap[first][second] || 0, 1),
            exam_times: examTimesMap[second] || 0,
        }));
        knowledgeModule.score = _.round(knowledgeModule.children.reduce((sum, e) => sum + e.score, 0), 1);

        knowledgeModules.push(knowledgeModule);
    }

    return { questions, examTimesMap, knowledgeModules };
};

// 获取试卷分析：包括题目细目分析（分值/一二三级知识点）、试卷知识点分析
const getExampaperStatistics = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);

    try {
        let exampaperId = +req.params.exampaper_id;
        let exampaper = await db.collection('exampaper').findOne({ _id: exampaperId });
        if (!exampaper) return resWrapper.error('PARAMETERS_ERROR', `ID: ${exampaperId} 试卷不存在`);

        let { questions, knowledgeModules } = await _getExampaperStatistics(exampaper);

        resWrapper.succ({
            exampaper_id: exampaper._id,
            year: exampaper.to_year,
            questions,
            knowledges: knowledgeModules
        });
    } catch (err) {
        err.message = `dmp_exampaper.getExampaperStatistics: ${err.message}`;
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

const _getExamScopeOfExampaper = async ({ blocks, period, subject, statistics }) => {
    let secondKnowledges = [];
    if (statistics && statistics.length) {
        for (let first of statistics) {
            let _statistics = first.statistics || [];
            secondKnowledges.push(..._statistics.map(e => e.name));
        }
        return secondKnowledges;
    }

    let questionIds = [];
    for (let i = 0; i < blocks.length; i++) {
        if (Array.isArray(blocks[i].questions) && blocks[i].questions.length>0 ) {
            for (let ques of blocks[i].questions) {
                if (_.isObject(ques) && ques.id) {
                    questionIds.push(ques.id);
                } else {
                    questionIds.push(ques);
                }
            }
        }
    }

    let questions = await db.collection('question').find({ _id: { $in: questionIds } }, {
        readPreference: 'secondaryPreferred'
    }).project({ knowledges: 1 }).toArray();

    let knowledgeMap = await getCachedTree(period, subject);

    const defaultPoint = { order: 100000000, path: ['', ''] };

    let kids = [];
    for (let q of questions) {
        if (!q.knowledges || !q.knowledges.length) continue;
        for (let e of q.knowledges) {
            !kids.includes(e.id) && kids.push(e.id);
        }
    }
    kids.sort((a, b) => {
        return (knowledgeMap[a] || defaultPoint).order - (knowledgeMap[b] || defaultPoint).order;
    });

    for (let kid of kids) {
        let [, second] = (knowledgeMap[kid] || defaultPoint).path;
        second && !secondKnowledges.includes(second) && secondKnowledges.push(second);
    }

    return secondKnowledges;
};

// 获取多个试卷的知识点汇总分析（二级知识点）
const getExamScopeByManyExampapers = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);

    try {
        let ids = req.params.exampaper_ids;
        ids = ids.split(',').map(e => +e).filter(e => e);
        if (!ids.length) return resWrapper.succ([]);

        let exampapers = await db.collection('exampaper').find({ _id: { $in: ids } }, {
            readPreference: 'secondaryPreferred'
        }).project({
            period: 1, subject: 1, blocks: 1, statistics: 1
        }).toArray();
        let statisticsRecords = await db.collection('exampaper_knowledge_statistics').find({
            $and: [{
                exampapers: { $size: 1 },
            }, {
                exampapers: { $in: ids },
            }]
        }).toArray();
        if (statisticsRecords.length) {
            for (let e of exampapers) {
                let record = statisticsRecords.find(s => s.exampapers[0] === e._id);
                if (record) e.statistics = record.statistics;
            }
        }

        let ret = await Promise.all(exampapers.map(async (exampaper) => {
            let scope = {
                id: exampaper._id,
                secondKnowledges: [],
            };
            if (!exampaper.blocks || !exampaper.blocks.length) return scope;
            scope.secondKnowledges = await _getExamScopeOfExampaper(exampaper);
            return scope;
        }));

        resWrapper.succ(ret);
    } catch (err) {
        err.message = `dmp_exampaper.getExamScopeByManyExampapers: ${err.message}`;
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

// 获取试卷概要信息，供dmp筛选区域真卷
const _getBriefByCond = async ({ utime, province, city, period, subject, type, year, press_version }) => {
    let cond = {};
    if (province) cond.province = province;
    if (city) cond.city = city;
    if (period) cond.period = period;
    if (subject) cond.subject = subject;
    if (type) cond.type = type;
    if (press_version) cond.press_version = press_version;
    if (year) {
        year = year.split(',').map(e => +e).filter(e => e);
        if (year.length) cond.to_year = { $in: year };
    }

    if (+utime) {
        cond = {
            to_year: { $gte: 2015 },
            ...cond,
            type: { $in: ['中考真卷', '高考真卷'] },
            press_version: 'all_version',
            utime: { $gte: new Date(+utime) },
            is_test: { $ne: true },
            $and: [{
                province: { $ne: '' },
            }, {
                province: { $exists: 1 },
            }]
        };
    }

    let proj = {
        name: 1, period: 1, subject: 1, province: 1, city: 1, to_year: 1, press_version: 1,
        quality: 1, score: 1, from: 1, type: 1, view_times: 1,
    };
    let briefs = await db.collection('exampaper').find(cond, {
        readPreference: 'secondaryPreferred'
    }).project(proj).toArray();

    for (let e of briefs) {
        e.id = e._id;
        e.year = e.to_year;
        delete e._id;
        delete e.to_year;
    }

    return briefs;
};

// 获取试卷条目，指定省份/城市、学段/学科、年份
const getExampapersBrief = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);
    try {
        let briefs = await _getBriefByCond(req.query);
        resWrapper.succ(briefs);
    } catch (err) {
        err.message = `dmp_exampaper.getExampapersBrief: ${err.message}`;
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getExampapersBrief,
    getParentKnowledgeMap,
    getExampaperStatistics,
    getExamScopeByManyExampapers,
};