const Logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const schema = require('../schemas/landmark_point');
const counter = require('../../../common/utils/counter');
const lodash = require('lodash');
const landmarkPoint = 'landmark_point';
const question = 'question';
const knowledgeColl = 'knowledge';

// 检查名称是否已经存在
const checkPointNameExists = async (landmarkPoints) => {
    let result = []; // 已经存在的地标点名称
    if (Array.isArray(landmarkPoints) && landmarkPoints.length > 0) {
        await Promise.all(landmarkPoints.map(async point => {
            let cond = { name: point.name, subject: point.subject, period: point.period, invalid: 0 };
            let data = await db.collection(landmarkPoint).findOne(cond, { fields: { _id: 1 } });
            if (point.id) { // 存在id, 编辑是否存在
                if (data && point.id !== data._id) {
                    result.push(point.name);
                }
            } else { // 新增是否存在
                if (data) {
                    result.push(point.name);
                }
            }
        }));
    }
    return result.length > 0 ? result.join(',') + ':kb库已存在' : '';
};

const getKnowledgeName = async (ids) => {
    let know = await db.collection(knowledgeColl).find({
        _id: { $in: ids }
    }).project({
        name: 1
    }).toArray();
    let knowObj = {};
    if (Array.isArray(know)) {
        // know转换为对象
        know.forEach(i => {
            knowObj[i._id] = i.name;
        });
    }
    return knowObj;
};

const mapLandmarkPointByIds = async (data, sort) => {
    // 将data转成对象，并收集knowledge_id
    let obj = {};
    let knowledge_arr = new Set();
    data.forEach(i => {
        if (!obj[i._id]) {
            obj[i._id] = i;
            knowledge_arr.add(i.knowledge_id);
        }
    });
    let know = await getKnowledgeName(Array.from(knowledge_arr));
    let newData = [];
    sort.forEach(i => {
        let _i = obj[i];
        _i.knowledge_name = know[_i.knowledge_id];
        _i.id = _i._id;
        delete _i._id;
        newData.push(_i);
    });
    return newData;
};

const getLandmarkPointByIds = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let pointIds = req.params.point_ids;
    try {
        const pointIdsArr = pointIds.split(',').map(function (value) {
            return Number(value);
        });
        const cond = { _id: { $in: pointIdsArr }, invalid: 0 };
        const project = { ctime: 0, utime: 0, has_modified: 0, invalid: 0 };
        let result = await db.collection(landmarkPoint).find(cond).project(project).toArray();
        // 处理数据
        result = await mapLandmarkPointByIds(result, pointIdsArr);
        return responseWrapper.succ(result);
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getLandPointList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let knowledgeId = req.params.knowledge_id;
    try {
        const cond = { knowledge_id: parseInt(knowledgeId) };
        const project = { ctime: 0, utime: 0, has_modified: 0 };
        let result = await db.collection(landmarkPoint).find(cond).project(project).toArray();
        let know = await db.collection(knowledgeColl).findOne({ _id: parseInt(knowledgeId) }, { fields: { name: 1 } });
        let knowName = '';
        if (know) {
            knowName = know.name;
        }
        let newRes = [];
        for (let item of result) {
            if (0 === item.invalid) {
                item.knowledge_name = knowName;
                delete item.invalid;
                item['id'] = item._id;
                delete item._id;
                newRes.push(item);
            }
        }
        return responseWrapper.succ({ list: newRes, total_num: newRes.length });
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const deleteOriginalQues = async (originalQuesIds) => {
    let cond = { 'question_ids.original_ques_id': { $in: originalQuesIds } };
    let pro = { question_ids: 1, invalid: 1 };
    let originalQuesArr = await db.collection(landmarkPoint).find(cond).project(pro).toArray();
    let originalMap = new Map();
    let existsOriginalIds = [];
    for (const point of originalQuesArr) {
        if (point.invalid === 1) {
            continue;
        }
        let pointId = point._id;
        for (const ques of point.question_ids) {
            let original_ques_id = ques.original_ques_id;
            if (original_ques_id) {
                existsOriginalIds.push(original_ques_id);
                let value = {};
                if (originalMap.has(original_ques_id)) {
                    value = originalMap.get(original_ques_id);
                    value.landmark_point_ids.push(pointId);
                    value.landmark_reco_questions.push(...ques.similar_ques_ids);
                } else {
                    value = {
                        landmark_point_ids: [pointId],
                        landmark_reco_questions: [...ques.similar_ques_ids]
                    };
                }
                originalMap.set(original_ques_id, value);
            }
        }
    }
    // 存在地标点，重置
    originalMap.forEach(async (value, original_ques_id) => {
        let cond = { _id: original_ques_id };
        let set = {
            $set: {
                landmark_point_ids: value.landmark_point_ids,
                landmark_reco_questions: value.landmark_reco_questions,
                'attach.is_landmark_ques': 1,
                utime: new Date()
            }
        };
        await db.collection(question).updateOne(cond, set);
    });
    // 不存在地标点，转成普通题
    let toDelOriginalIds = lodash.difference(originalQuesIds, existsOriginalIds);
    if (toDelOriginalIds.length > 0) {
        let cond = { _id: { $in: toDelOriginalIds } };
        let set = {
            $set: {
                'attach.is_landmark_ques': 0,
                utime: new Date()
            },
            $unset: {
                landmark_point_ids: 1,
                landmark_reco_questions: 1,
            }
        };
        await db.collection(question).updateMany(cond, set);
    }
};

const deleteSimQues = async (similarQuesIds,) => {
    let condSim = { 'question_ids.similar_ques_ids': { $in: similarQuesIds } };
    let proSim = { question_ids: 1, invalid: 1 };
    let similarQuesArr = await db.collection(landmarkPoint).find(condSim).project(proSim).toArray();
    let similarMap = new Map();
    let existsSimIds = [];
    for (const point of similarQuesArr) {
        if (point.invalid === 1) {
            continue;
        }
        let pointId = point._id;
        for (const ques of point.question_ids) {
            let similar_ques_ids = ques.similar_ques_ids;
            if (Array.isArray(similar_ques_ids)) {
                existsSimIds.push(...similar_ques_ids);
                for (let similar of similar_ques_ids) {
                    let value = {};
                    if (similarMap.has(similar)) {
                        value = similarMap.get(similar);
                        value.push(pointId);
                    } else {
                        value = [pointId];
                    }
                    similarMap.set(similar, value);
                }
            }
        }
    }
    // 存在地标点，重置
    similarMap.forEach(async (value, qId) => {
        let cond = { _id: qId };
        let set = {
            $set: {
                landmark_point_ids: value,
                'attach.is_landmark_ques': 2,
                utime: new Date()
            }
        };
        await db.collection(question).updateOne(cond, set);
    });

    let toDelSimIds = lodash.difference(similarQuesIds, existsSimIds);
    // 它不是相似题，转成普通题
    if (toDelSimIds.length > 0) {
        let cond = { _id: { $in: toDelSimIds } };
        let set = {
            $set: {
                'attach.is_landmark_ques': 0,
                utime: new Date()
            },
            $unset: { landmark_point_ids: 1 }
        };
        await db.collection(question).updateMany(cond, set);
    }
};


const putLandmarkPoints = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        schema.putValidate(req.body);
        let landmarkPoints = req.body.landmark_points;
        let exists = await checkPointNameExists(landmarkPoints);
        if (exists) {
            return responseWrapper.error('HANDLE_ERROR', exists);
        }
        let point = landmarkPoints[0]; // kbp更新是只更新一条
        let pointId = point.id;
        delete point.id;
        let oldPoint = await db.collection(landmarkPoint).findOne({ _id: pointId }, { fields: { question_ids: 1 } });
        let oldQuestionIds = oldPoint.question_ids || [];

        let oldOriQuesIds = [];
        let newOriQuesIds = [];
        let oldSimQuesIds = [];
        let newSimQuesIds = [];
        for (const item of oldQuestionIds) {
            if (item.original_ques_id) {
                oldOriQuesIds.push(item.original_ques_id);
            }
            if (Array.isArray(item.similar_ques_ids)) {
                oldSimQuesIds.push(...item.similar_ques_ids);
            }
        }
        for (const item of point.question_ids) {
            if (item.original_ques_id) {
                newOriQuesIds.push(item.original_ques_id);
            }
            if (Array.isArray(item.similar_ques_ids)) {
                newSimQuesIds.push(...item.similar_ques_ids);
            }
        }
        let quesIdsObj = {}; // 构建原题与相似题键值对
        for (const item of point.question_ids) {
            if (item.original_ques_id) {
                quesIdsObj[item.original_ques_id] = item.similar_ques_ids || [];
            }
        }

        oldOriQuesIds = Array.from(new Set(oldOriQuesIds));
        newOriQuesIds = Array.from(new Set(newOriQuesIds));
        oldSimQuesIds = Array.from(new Set(oldSimQuesIds));
        newSimQuesIds = Array.from(new Set(newSimQuesIds));


        let addOriQuesIds = lodash.difference(newOriQuesIds, oldOriQuesIds);
        let addSimQuesIds = lodash.difference(newSimQuesIds, oldSimQuesIds);
        let resetOriQuesIds = lodash.difference([...oldOriQuesIds, ...newOriQuesIds], addOriQuesIds); // 所有的除去新增的
        let resetSimQuesIds = lodash.difference([...oldSimQuesIds, ...newSimQuesIds], addSimQuesIds); // 所有的除去新增的

        point.utime = new Date();
        point.has_modified = 1;
        await db.collection(landmarkPoint).updateOne({ _id: pointId }, { $set: point });
        // 添加新增原题
        if (addOriQuesIds.length > 0) {
            await Promise.all(addOriQuesIds.map(async (qId) => {
                let landmark_reco_questions = quesIdsObj[qId];
                let cond = { _id: qId };
                let set = {
                    $addToSet: {
                        landmark_point_ids: pointId,
                        landmark_reco_questions: { $each: landmark_reco_questions },
                    },
                    $set: { 'attach.is_landmark_ques': 1, utime: new Date() }
                };
                await db.collection(question).updateOne(cond, set);
            }));
        }
        // 添加新增相似题
        if (addSimQuesIds.length > 0) {
            let cond = { _id: { $in: addSimQuesIds } };
            let set = {
                $addToSet: { landmark_point_ids: pointId },
                $set: { 'attach.is_landmark_ques': 2, utime: new Date() }
            };
            await db.collection(question).updateMany(cond, set);
        }
        // 重置地标原题
        await deleteOriginalQues(resetOriQuesIds);
        // 重置地标相似题
        await deleteSimQues(resetSimQuesIds);

        responseWrapper.succ({});
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const postLandmarkPoints = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        schema.postValidate(req.body);
        let landmarkPoints = req.body.landmark_points;
        let exists = await checkPointNameExists(landmarkPoints);
        if (exists) {
            return responseWrapper.error('HANDLE_ERROR', exists);
        }
        for (let point of landmarkPoints) {
            const seq = await counter.nextSeq('landmark_point', 1);
            point._id = seq[0];
            point.invalid = 0;
            point.has_modified = 1;
            point.ctime = new Date();
            point.utime = new Date();
            delete point.id;
            await db.collection(landmarkPoint).insertOne(point);
            if (Array.isArray(point.question_ids) && point.question_ids.length > 0) {
                let addSimQuesIds = [];
                await Promise.all(point.question_ids.map(async (item) => {
                    let landmark_reco_questions = item.similar_ques_ids;
                    addSimQuesIds.push(...landmark_reco_questions);
                    let cond = { _id: item.original_ques_id };
                    let set = {
                        $addToSet: {
                            landmark_point_ids: point._id,
                            landmark_reco_questions: { $each: landmark_reco_questions },
                        },
                        $set: { 'attach.is_landmark_ques': 1, utime: new Date() }
                    };
                    await db.collection(question).updateOne(cond, set);
                }));
                // 添加新增相似题
                if (addSimQuesIds.length > 0) {
                    let cond = { _id: { $in: addSimQuesIds } };
                    let set = {
                        $addToSet: { landmark_point_ids: point._id },
                        $set: { 'attach.is_landmark_ques': 2, utime: new Date() }
                    };
                    await db.collection(question).updateMany(cond, set);
                }
            }
        }
        return responseWrapper.succ({});
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const checkExists = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        schema.postValidate(req.body);
        let landmarkPoints = req.body.landmark_points;
        let exists = await checkPointNameExists(landmarkPoints);
        if (exists) {
            return responseWrapper.error('HANDLE_ERROR', exists);
        }
        return responseWrapper.succ({});
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const checkQuestionHasError = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        schema.checkLandmarkQuesValidate(req.body);
        let originQues = req.body.original_questions;
        let similarQues = req.body.similar_questions;
        let quesIds = [...originQues, ...similarQues];
        let cond = {
            _id: { $in: quesIds }
        };
        let proj = {
            _id: 1,
            'attach.is_landmark_ques': 1
        }
        let quesInKB = await db.collection(question).find(cond).project(proj).toArray();

        let original_error_ids = [];
        let similar_error_ids = [];
        // 判断原题中是否含有已标为相似题的试题
        for (let id of originQues) {
            let record = lodash.find(quesInKB, (q) => {
                return q._id === id;
            });
            if (record.attach && record.attach.is_landmark_ques === 2) {
                original_error_ids.push(id);
            }
        }

        // 判断相似题中是否含有已标为原题的试题
        for (let id of similarQues) {
            let record = lodash.find(quesInKB, (q) => {
                return q._id === id;
            });
            if (record.attach && record.attach.is_landmark_ques === 1) {
                similar_error_ids.push(id);
            }
        }

        let ret = {};
        if (original_error_ids.length > 0 || similar_error_ids.length > 0) {
            ret = { original_error_ids, similar_error_ids }
        }
        return responseWrapper.succ(ret);
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getLandmarkPointByIds,
    putLandmarkPoints,
    postLandmarkPoints,
    checkExists,
    getLandPointList,
    checkQuestionHasError,
};

