<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="150" viewBox="0 0 120 150" xmlns="http://www.w3.org/2000/svg">
    <!-- 阴影效果定义 -->
    <defs>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.1)"/>
            <feDropShadow dx="0" dy="5" stdDeviation="5" flood-color="rgba(0,0,0,0.1)"/>
        </filter>
    </defs>

    
    <defs>
        <linearGradient id="grad_8840" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#8c2318;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#5d001e;stop-opacity:1" />
        </linearGradient>
    </defs>

    <!-- 主背景（包含整个封面的渐变） -->
    <rect width="120" height="150" fill="url(#grad_8840)" rx="6" style="filter: url(#shadow);"/>

    <!-- 装饰图案 -->
    
                <defs>
                    <pattern id="chineseGrid1" patternUnits="userSpaceOnUse" width="15" height="15">
                        <path d="M 15 0 L 0 0 0 15" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                    </pattern>
                </defs>
                <rect x="0" y="0" width="120" height="150" fill="url(#chineseGrid1)" opacity="0.08"/>
            

    <!-- 头部分隔线（从左到右完整覆盖） -->
    <line x1="0" y1="18" x2="120" y2="18" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>

    <!-- 标题文字 -->
    <text x="60" y="12" font-family="Noto Sans SC, Helvetica Neue, Arial, sans-serif"
          font-size="9" font-weight="500" fill="white" text-anchor="middle"
          letter-spacing="0.5px" style="text-transform: uppercase;">语文 | CHINESE</text>

    <!-- 外边框 -->
    <rect width="120" height="150" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5" rx="6"/>
</svg>