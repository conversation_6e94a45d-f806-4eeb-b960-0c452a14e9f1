const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const ResponseWrapper = require('../../common/utils/response_wrapper');

const educationAssistantBooks = 'education_assistant_book';
const text_book = 'text_book';
const catalog = 'catalog';

//删除教辅
const education_assistant_books = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    try {
        let result = await db.collection(educationAssistantBooks).remove({ _id: parseInt(id) });
        responseWrapper.succ(result);
    } catch (e) {
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//删除教材
const text_books = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;

    let dataCat = await db.collection(catalog).findOne({ _id: 'text_book' });
    let dataSou = await db.collection(text_book).findOne({ _id: parseInt(id) });

    try {
        for (let per of dataCat.periods) {
            if (per.name === dataSou.period) {
                for (let sub of per.subjects) {
                    if (sub.name === dataSou.subject) {
                        for (let pre = 0; pre < sub.press_versions.length; pre++) {
                            if (sub.press_versions[pre].name === dataSou.press_version) {
                                for (let gra = 0; gra < sub.press_versions[pre].grades.length; gra++) {
                                    if (sub.press_versions[pre].grades[gra].name === dataSou.grade) {
                                        sub.press_versions[pre].grades.splice(gra, 1);
                                        if (sub.press_versions[pre].grades.length === 0) {
                                            sub.press_versions.splice(pre, 1);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        let res1 = await db.collection(catalog).updateOne({ _id: 'text_book' }, dataCat);
        let res2  = await db.collection(text_book).remove({ _id: parseInt(id) });
        responseWrapper.succ(res1,res2);
    } catch (e) {
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

module.exports = {
    education_assistant_books,
    text_books
};