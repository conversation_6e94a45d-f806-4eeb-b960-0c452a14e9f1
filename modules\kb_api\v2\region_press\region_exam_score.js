const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const { ObjectID } = require('mongodb');
const db = mongodber.use('KB');

const regionExam = db.collection('region_exam_score');

// 地区考试列表
const getRegionExamList = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let limit = Number(req.query.limit) || 10;
    let offset = Number(req.query.offset) || 0;
    let type = req.query.type;
    let period = req.query.period;
    let provinces = req.query.provinces;
    if (!type || !period || !provinces) {
        return resWrapper.error('PARAMETERS_ERROR', '参数不能为空');
    }
    let provinceArr = [];
    let cityArr = [];
    let cond = {
        type: type, period: period
    };
    if (provinces !== 'all') {
        provinces = JSON.parse(provinces);
        provinces.forEach(pro => {
            provinceArr.push(pro.name);
            cityArr = cityArr.concat(pro.cities);
        });
        cond.province = { $in: provinceArr };
        if (period === '初中') {
            cond.city = { $in: cityArr };
        }
    }
    try {
        let count = await regionExam.find(cond).count();
        let list = await regionExam.find(cond).limit(limit).skip(offset).toArray();
        list.forEach((value) => {
            if (value) {
                value.id = value._id;
                delete value._id;
            }
        });
        return resWrapper.succ({ count: count, list: list });
    } catch (error) {
        return resWrapper.error('HANDLE_ERROR', error.message);
    }

};

// 获取地区考试信息
const getRegionExamById = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let id = req.params.region_exam_id.toString();
    if (!id) {
        return resWrapper.error('PARAMS_ERROR', '参数不能为空');
    }
    let cond = { _id: ObjectID(id) };
    try {
        let result = await regionExam.findOne(cond);
        if (result) {
            result.id = result._id;
            delete result._id;
        }
        return resWrapper.succ(result);
    } catch (error) {
        return resWrapper.error('HANDLE_ERROR', error.message);
    }

};

// 修改地区考试信息
const updateRegionExam = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    let id = req.params.region_exam_id.toString();
    let subjects = req.body;
    if (!id || !subjects || subjects.length === 0) {
        return resWrapper.error('PARAMETERS_ERROR', '参数不能为空');
    }
    let cond = {
        _id: ObjectID(id),
    };
    let data = {
        subjects: subjects,
        utime: new Date()
    };
    let records = await regionExam.updateOne(cond, { '$set': data });
    return resWrapper.succ({ id: records });
};

module.exports = {
    getRegionExamList,
    getRegionExamById,
    updateRegionExam
};
