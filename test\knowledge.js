/**
 * 知识点 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 9500,
    app_key: 'iyunxiao_kbplat180305',
};

describe('知识点', function () {
    describe('修改知识点', function () {
        this.timeout(900000);
        it('/kb_api/v2/knowledges/:knowledge_id', async function () {
            let knowledge_id = '1512964095';
            let pathname = `/kb_api/v2/knowledges/${knowledge_id}`;
            let url = URL.format({
                protocol: 'http',
                hostname: localEnv.host,
                port: localEnv.port,
                pathname: pathname,
                search: qs.stringify({
                    api_key: localEnv.app_key
                })
            });
            let data = {
                fields: {
                    name: '自然环境人类活动的区域'
                }
            };
            let result = await request.put(url).send(data);
            assert.deepStrictEqual(result.body, {});
        });
    });

});
