const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const _ = require('underscore');
const cols_to_tree = require('./utils.js').cols_to_tree;

const LIST_KEYS = ['period', 'subject', 'grade'];
function manualListParams(req) {
    let params = {};
    let cacheKey = `kb_api:v2:tutor_manuals:${req.query.period}:${req.query.subject}:${req.query.grade}`;
    params['cacheKey'] = cacheKey;
    LIST_KEYS.forEach(function (key) {
        if (req.query.hasOwnProperty(key) && req.query[key]) {
            params[key] = req.query[key];
        }
    });
    if (params['grade']) {
        params['grade'] = new RegExp(params['grade']);
    }
    return params;
}

function manualListFunc(params, callback) {
    let cond = {};
    LIST_KEYS.forEach(function (key) {
        if (params.hasOwnProperty(key)) {
            cond[key] = params[key];
        }
    });
    db.collection('tutor_manual').find(cond).toArray(function (err, items) {
        if (err) {
            return callback(err);
        }
        if (items.length === 0) {
            return callback('NULL_ERROR');
        }
        // console.log(JSON.stringify(items));
        items.sort(function (a, b) {
            return a.order_no - b.order_no;
        });
        var keys = [
            ['period', 'period'],
            ['subject', 'subject'],
            ['grade', 'grade'],
        ];
        items = cols_to_tree('tutor_manuals', items, keys);
        return callback(null, items);
    });
}

function tutor_manuals(req, res) {
    var responseWrapper = new ResponseWrapper(res);
    var params = null;
    try {
        params = manualListParams(req);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    manualListFunc(params, function (error, retObj) {
        if (error) {
            if (typeof (error) === 'string') {
                if (error === 'NULL_ERROR') {
                    return responseWrapper.error('NULL_ERROR');
                }
                return responseWrapper.error('HANDLE_ERROR', error);
            }
            return responseWrapper.error('HANDLE_ERROR');
        }
        return responseWrapper.succ(retObj);
    });
}

function manualParams(req) {
    let params = {};
    params['id'] = parseInt(req.params.id);
    if (isNaN(params['id'])) {
        throw ('id 非法');
    }
    if (req.query.hasOwnProperty('fields_type')) {
        if (req.query.fields_type === 'full') {
            params['fields_type'] = 'full';
        }
    }
    let cacheKey = `kb_api:v2:tutor_manuals:${params['id']}:${params['fields_type']}`;
    params['cacheKey'] = cacheKey;
    return params;
}

async function manualFunc(params) {
    let cond = {};
    cond['_id'] = params['id'];
    let item = await db.collection('tutor_manual').findOne(cond);
    if (!item)  throw new Error('cannot query data');
    item['id'] = item['_id'];
    delete item['_id'];
    if (params['fields_type'] === 'full') {
        let ids = [];
        item.chapters.forEach(function (chapter) {
            ids.push(chapter.id);
        });
        let cond = { '_id': { $in: ids } };
        let fullChapters = await db.collection('tutor_manual_chapter').find(cond).toArray();
        fullChapters.forEach(function (chapter) {
            chapter['id'] = chapter['_id'];
            delete chapter['_id'];
        });
        let curTopicArray = [];
        item.chapters.forEach(function (chapter) {
            let findFullTopic = _.find(fullChapters, function (fullTopic) {
                return fullTopic.id === chapter.id;
            });
            if (findFullTopic) {
                curTopicArray.push(findFullTopic);
            } else {
                console.error('not find chapter ' + chapter.id);
            }
        });
        item['chapters'] = curTopicArray;
        // console.log('set cache full ' + params['cacheKey']);
        // rediser.set(params['cacheKey'], item, 60 * 30);
        return item;
    }
    return item;
}

// 根据id获取某个知识体系
async function tutor_manual(req, res) {
    var responseWrapper = new ResponseWrapper(res);
    var params = null;
    try {
        params = manualParams(req);
        let retObj = await manualFunc(params);
        return responseWrapper.succ(retObj);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
}

const getIds = (nodes) => {
    var retval = [];
    try {
        for (let ix in nodes) {
            let node = nodes[ix];
            let obj = {};
            obj.name = node.name;
            obj.key = 'knowledge_node';
            if (node.id) {
                retval.push(node.id)
            }
            if (node.nodes) {
                let children = getIds(node.nodes);
                for (let j in children) {
                    retval.push(children[j])
                }
            }
        }
    } catch (err) {

    }
    return retval;
}

const loadTreeNode = (nodes) => {
    var retval = [];
    try {
        for (let ix in nodes) {
            let node = nodes[ix];
            let obj = {};
            obj.name = node.name;
            obj.key = 'knowledge_node';
            if (node.id) {
                obj.id = node.id;
            }
            if (node.nodes) {
                obj.children = loadTreeNode(node.nodes);
            }
            retval.push(obj);
        }
    } catch (err) {

    }
    return retval;
}

const filter = (nodes, exists) => {
    let retval = [];
    try {
        for (let ix in nodes) {
            let node = nodes[ix];
            let obj = {};
            let ps = false;
            obj.name = node.name;
            if (node.id && exists.indexOf(node.id) >= 0) {
                obj.id = node.id;
                ps = true;
            }
            if (node.nodes) {
                obj.nodes = filter(node.nodes, exists);
                ps = true;
            }

            if (ps == true) {
                retval.push(obj);
            }

        }
    } catch (err) {
        debugger
    }
    return retval;
}

// 改革开放了 2018-8-17
const getKnowledgeTreeTutor = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let id = req.params.id * 1;
        let tree = await db.collection('knowledge_tree').findOne({ _id: id });
        if (!tree) {
            throw new Error();
        }
        const ids = getIds(tree.nodes);
        let exists = await db.collection('tutor_manual_chapter').find({ 'nodes.id': { $in: ids } }, { 'nodes.id': 1 }).toArray();
        exists = _.pluck(_.flatten(_.pluck(exists, 'nodes')), 'id');
        tree.nodes = filter(tree.nodes, exists);
        tree.children = {
            children: loadTreeNode(tree.nodes)
        };
        delete tree.nodes;
        // delete tree.nodes;
        tree.id = tree._id;
        delete tree._id;
        return responseWrapper.succ(tree);
    } catch (err) {
        console.log(err.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
}

module.exports = {
    tutor_manuals: tutor_manuals,
    tutor_manual: tutor_manual,
    getKnowledgeTreeTutor: getKnowledgeTreeTutor,
};
