const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const Logger = require('../../../common/utils/logger');

const landmarkTreeSchema = {
    type: 'object',
    additionalProperties: false,
    required: ['period', 'chapters', 'subject'],
    properties: {
        period: {
            type: 'string',
        },
        chapters: {
            type: 'array',
        },
        subject: {
            type: 'string',
        }
    }
};

const valiLandmarkTreeDate = (data) => {
    if (!ajv.validate(landmarkTreeSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('数据格式错误');
    }
};
module.exports = {
    valiLandmarkTreeDate: valiLandmarkTreeDate,

};