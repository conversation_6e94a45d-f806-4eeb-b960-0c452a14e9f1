const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const ResponseWrapper = require('../../common/utils/response_wrapper');
let Logger = require('../../common/utils/logger');
const schemaEducationAssistantBook = require('./schemas/education_assistant_book');
const counter = require('../../common/utils/counter');

const educationAssistantBooks = 'education_assistant_book';
const catalog = 'catalog';

//获取编目
const getCatelogs = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let period = req.query.period;
    let subject = req.query.subject;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    try {
        let result = await db.collection(catalog).findOne({ _id: 'education_assistant_book' });
        for (let per of result.periods) {
            if (per.name === period) {
                for (let sub of per.subjects) {
                    if (sub.name === subject) {
                        return responseWrapper.succ({ province: sub.provinces });
                    }
                }
            }
        }
        return responseWrapper.error('NULL_ERROR', '该学段学科下暂无教材');
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//获取编目下教材信息
const getEducationAssistantBooks = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let limit = req.query.limit || 25;
    let offset = req.query.offset || 0;
    let period = req.query.period;
    let subject = req.query.subject;
    let province = req.query.province || '';
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    let result = [];
    let cond = {
        period: period,
        subject: subject
    };
    if (province) {
        let provinces = province.split(',');
        cond.province = {$in:provinces};
    }
    try {
        let data = await db.collection(educationAssistantBooks).find(cond).skip(parseInt(offset)).limit(parseInt(limit)).toArray();
        let total_num = await db.collection(educationAssistantBooks).find(cond).count();
        for (let profile of data) {
            let id = profile._id;
            let profile_url = profile.profile_url;
            let host = profile.host;
            let name = profile.name;
            let books = {
                id: id,
                profile_url: profile_url,
                name: name,
                host: host
            };
            result.push(books);
        }
        let final = {
            total_num: total_num,
            books: result
        };
        return responseWrapper.succ(final);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//获取教材详细信息
const getEducationAssistantBookById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    try {
        let data = await db.collection(educationAssistantBooks).findOne({ _id: parseInt(id) });
        data.id = data._id;
        delete data._id;
        responseWrapper.succ(data);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};
//新增教辅
const addEducationAssistantBook = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let data = req.body;
    let now = new Date();
    try {
        // 格式校验
        schemaEducationAssistantBook.postValidate(data);
        const seq = await counter.nextSeq('education_assistant_book', 1);
        data._id = seq[0];
        data.ctime = now;
        data.utime = now;
        let result = await db.collection(educationAssistantBooks).insertOne(data);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '新增出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//修改教辅
const updateEducationAssistantBook = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let data = req.body;
    let now = new Date();
    try {
        // 格式校验
        schemaEducationAssistantBook.putValidate(data);
        data._id = data.id;
        delete data.id;
        data.utime = now;
        data.has_modified = 1;
        await db.collection(educationAssistantBooks).updateOne({ _id: data._id }, data);
        responseWrapper.succ({});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//查重
const checkEducationAssistantBook = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let name = req.params.name;
    if (!name) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    let data;
    let count = await db.collection(educationAssistantBooks).find({ name: name }).count;
    let result = await db.collection(educationAssistantBooks).find({ name: name }).toArray();
    data.id = result._id;
    data.name = result.name;
    if (count !== 0) {
        return responseWrapper.succ(data);
    }
    return responseWrapper.succ({ msg: '未找到名称相同资源' });

};

module.exports = {
    getCatelogs,
    getEducationAssistantBooks,
    getEducationAssistantBookById,
    addEducationAssistantBook,
    updateEducationAssistantBook,
    checkEducationAssistantBook
};