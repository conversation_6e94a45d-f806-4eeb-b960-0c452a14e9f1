const _ = require('lodash');
const axios = require('axios');
const Config = require('config');
const url = require('url');
const qs = require('qs');
const rediser = require('../../../common/utils/rediser');
const logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const _diff = require('../question/utils').transferDiffDecimal2Five;
const briefSchema = require('../schemas/exampaper_brief');
const OneDaySeconds = 24 * 60 * 60;
const CacheFreshTime = 1000 * 60 * 30;

const _cacheKey = (period, subject) => `kb_api:v2:dmp_exampaper:getKnowledgeMap:${period}_${subject}`;
const _lockKey = (period, subject) => `kb_api:v2:dmp_exampaper:getKnowledgeMap:${period}_${subject}:lock`;

/*
    统计试卷试题细目信息，包括试题的 题型，难度，分数，一二三级知识点
 */


/**
 * 获取知识树
 * @param {*} period 
 * @param {*} subject 
 */
const _getKnowledgeMap = async (period, subject) => {
    let knowledgeMap = {};

    let catalog = await db.collection('catalog').findOne({ _id: 'knowledge_tree' });
    let trees = [];
    for (let p of catalog.periods) {
        if (p.name === period) {
            for (let s of p.subjects) {
                if (s.name === subject) {
                    trees = s.trees;
                    break;
                }
            }
            if (trees) break;
        }
    }
    let tree = await db.collection('knowledge_tree').findOne({ _id: trees[0].id });

    let nodeIds = [];
    let order = 0;
    let nodeMap = {};
    const traverse = (nodes, parentId) => {
        let children = {};

        for (let i = 0; i < nodes.length; i++) {
            nodes[i].order = ++order;
            nodes[i].parentId = parentId;
            nodeMap[nodes[i].id] = nodes[i];

            nodeIds.push(nodes[i].id);
            children[nodes[i].name] = { order };
            if (nodes[i].nodes) {
                children[nodes[i].name].children = traverse(nodes[i].nodes, nodes[i].id);
            }
        }
        return children;
    };

    knowledgeMap.parentMap = traverse(tree.nodes, order);

    const _parents = (node) => {
        let p = [];
        p.push(node.name);
        let parentId = node.parentId;
        while (parentId) {
            node = nodeMap[parentId];
            p.push(node.name);
            parentId = node.parentId;
        }

        if (p.length > 2) {
            p = p.slice(p.length - 2);
        } else if (p.length === 1) {
            p[1] = p[0];
        }
        return p.reverse();
    };

    let hasKnowledgesNodes = [];
    let chapters = await db.collection('knowledge_tree_chapter').find({
        _id: { $in: nodeIds }
    }).project({ path: 0, utime: 0, ctime: 0 }).toArray();
    for (let e of chapters) {
        nodeMap[e._id].name = e.name;
        if (e.knowledges) {
            hasKnowledgesNodes.push(e);
        }
    }

    for (let e of hasKnowledgesNodes) {
        let node = nodeMap[e._id];
        if (!node) continue;
        for (let i = 0; i < e.knowledges.length; i++) {
            knowledgeMap[e.knowledges[i].id] = {
                name: e.knowledges[i].name,
                order: (node.order * 1000 + i),
                path: _parents(node),
            };
        }
    }

    return knowledgeMap;
};

const getCachedTree = async (period, subject) => {
    let cacheKey = _cacheKey(period, subject);
    let lockKey = _lockKey(period, subject);
    let cacheData = await rediser.get(cacheKey);

    if (!cacheData) {
        let tree = await _getKnowledgeMap(period, subject);
        rediser.set(cacheKey, {
            data: tree,
            utime: new Date().getTime(),
        }, OneDaySeconds);
        return tree;
    }

    const reFresh = async () => {
        let lock = await rediser.get(lockKey);
        if (lock) return;
        let setLock = await rediser.getCache().set(lockKey, 1, 'EX', 5, 'NX');
        if (!setLock) return;
        let tree = await _getKnowledgeMap(period, subject);
        tree.lostKids = cacheData.data.lostKids || {};
        await rediser.set(cacheKey, {
            data: tree,
            utime: new Date().getTime(),
        }, OneDaySeconds);
        let result = await rediser.del(lockKey);
        return result;
    };

    if (new Date() - cacheData.utime > CacheFreshTime) {
        reFresh().catch(err => logger.error(err));
    }

    return cacheData.data;
};

// 缓存知识树上没有的知识点
const updateLostKids = async (period, subject, lostKids) => {
    let cacheKey = _cacheKey(period, subject);
    let lockKey = _lockKey(period, subject);
    let cacheData = await rediser.get(cacheKey);
    if (cacheData) {
        let setLock;
        for (let i = 1; i <= 2; i++) {
            let lock = await rediser.get(lockKey);
            if (!lock) {
                setLock = await rediser.getCache().set(lockKey, 1, 'EX', 5, 'NX');
                if (setLock) break;
            }
            await new Promise((resolve, reject) => {
                setTimeout(resolve, 1000, 1);
            });
        }
        if (setLock) {
            let _lostKids = cacheData.data.lostKids || {};
            for (let e of lostKids) {
                _lostKids[e.id] = e.path;
            }
            cacheData.data.lostKids = _lostKids;
            cacheData.utime = new Date().getTime();
            await rediser.set(cacheKey, cacheData, OneDaySeconds);
            rediser.del(lockKey);
        }
    }
};


const _getQuestionsOfExampaper = async (blocks = []) => {
    let _questionIds = [];
    let scoreMap = {};
    for (let i = 0; i < blocks.length; i++) {
        let block = blocks[i];
        let questions = block.questions || [];
        if (!questions.length) continue;
        let score = Number((block.score / questions.length).toFixed(1)) || 0;
        for (let j = 0; j < questions.length; j++) {
            if (questions[j].id) {
                scoreMap[questions[j].id] = questions[j].score || score;
                _questionIds.push(questions[j].id);
            }
        }
    }

    let questions = await db.collection('question').find({ _id: { $in: _questionIds } }, {
        readPreference: 'secondaryPreferred'
    }).project({
        type: 1, difficulty: 1, knowledges: 1
    }).toArray();

    let questionMap = {};
    for (let q of questions) {
        questionMap[q._id] = q;
        q.score = _.round(scoreMap[q._id], 1);
    }

    return _questionIds.map(id => questionMap[id]).filter(e => e);
};

// 试卷细目分析，包括（试题题型、难度、分值、一二三级知识点、一级知识点分值、二级知识点分值/考频）
const _getExampaperStatistics = async ({ blocks, period, subject }) => {
    let questions = await _getQuestionsOfExampaper(blocks);
    let knowledgeMap = await getCachedTree(period, subject);
    let lostKids = [];

    // 知识树上没有的知识点，从knowledge_tree_chapter中获取包含该知识点的知识树上的路径
    const _getKnowledgePath = async (kid) => {
        if (knowledgeMap.lostKids && knowledgeMap.lostKids[kid]) {
            return knowledgeMap.lostKids[kid];
        }
        let path = [];
        let chapter = await db.collection('knowledge_tree_chapter').findOne({
            'knowledges.id': kid,
        });
        if (chapter) {
            let _path = chapter.path || '';
            // 如：高中_物理_综合知识库_电磁学_交变电流
            let [, , , first, second] = _path.split('_');
            let knowledge = chapter.knowledges.find(e => e.id === kid);
            let third = knowledge.name;
            second = second || first;
            path = [first, second, third];
        }
        lostKids.push({ id: kid, path });
        return path;
    };

    const defaultPoint = { order: 100000000, path: ['', ''] };
    // 二级知识点考频：{[二级知识点名字]: number}
    let examTimesMap = {};
    let knowledgeModuleMap = {};
    for (let q of questions) {
        q.knowledges = q.knowledges || [];
        let [firstLevel, secondLevel, thirdLevel] = [[], [], []];
        let kids = q.knowledges.map(e => e.id);
        if (kids.length > 1) kids.sort((a, b) => {
            return (knowledgeMap[a] || defaultPoint).order - (knowledgeMap[b] || defaultPoint).order;
        });

        let averageScore = kids.length && q.score ? (q.score / kids.length) : 0;

        for (let i = 0; i < kids.length; i++) {
            let path;
            if (knowledgeMap[kids[i]]) {
                path = [...knowledgeMap[kids[i]].path, knowledgeMap[kids[i]].name];
            } else {
                path = await _getKnowledgePath(kids[i], knowledgeMap);
            }
            let [first, second, third] = path;
            if (!third) continue;

            !~firstLevel.indexOf(first) && firstLevel.push(first);
            thirdLevel.push(third);

            if (!~secondLevel.indexOf(second)) {
                secondLevel.push(second);
                examTimesMap[second] ? (examTimesMap[second]++) : (examTimesMap[second] = 1);
            }

            if (knowledgeModuleMap[first]) {
                knowledgeModuleMap[first][second] = (knowledgeModuleMap[first][second] || 0) + averageScore;
            } else {
                knowledgeModuleMap[first] = { [second]: averageScore };
            }
        }

        q.first_knowledges = firstLevel;
        q.second_knowledges = secondLevel;
        q.third_knowledges = thirdLevel;

        q.difficulty = _diff(q.difficulty);
        q.id = q._id;
        delete q._id;
        delete q.knowledges;
    }
    if (lostKids.length) {
        updateLostKids(period, subject, lostKids).catch(err => logger.error(err));
    }
    let parentMap = knowledgeMap.parentMap;
    let defaultParent = { order: 100000, children: {} };
    let firstKnowledges = Object.keys(knowledgeModuleMap).sort((a, b) => {
        return (parentMap[a] || defaultParent).order - (parentMap[b] || defaultParent).order;
    });

    let knowledgeModules = [];
    for (let first of firstKnowledges) {
        let secondMap = (parentMap[first] || defaultParent).children;
        let secondKnowledges = Object.keys(knowledgeModuleMap[first]);
        secondKnowledges.sort((a, b) => {
            return (secondMap[a] || defaultParent).order - (secondMap[b] || defaultParent).order;
        });

        let knowledgeModule = { name: first };
        knowledgeModule.children = secondKnowledges.map(second => ({
            name: second,
            score: _.round(knowledgeModuleMap[first][second] || 0, 1),
            exam_times: examTimesMap[second] || 0,
        }));
        knowledgeModule.score = _.round(knowledgeModule.children.reduce((sum, e) => sum + e.score, 0), 1);

        knowledgeModules.push(knowledgeModule);
    }

    return { questions, examTimesMap, knowledgeModules };
};


// 单个试卷id分析（kbp调用）
const getSingleExampaperStatistics = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);
    try {
        let exampaperId = +req.params.exampaper_id;
        let exampaper = await db.collection('exampaper').findOne({ _id: exampaperId });
        if (!exampaper) {
            return resWrapper.error('PARAMETERS_ERROR', `ID: ${exampaperId} 试卷不存在`);
        }
        let { questions, knowledgeModules } = await _getExampaperStatistics(exampaper);

        resWrapper.succ({
            questions,
            knowledges: knowledgeModules
        });
    } catch (err) {
        err.message = `dmp_exampaper.getSingleExampaperStatistics: ${err.message}`;
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

// 获取近N年试卷分析（kbp调用）
const getManyExampaperStatistics = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);
    let exampaper_ids = req.params.exampaper_ids;
    exampaper_ids = exampaper_ids.split(',');
    try {
        let exampaperArr = [];
        let statistics = await Promise.all(exampaper_ids.map(async exampaper_id => {
            let cond = { _id: parseInt(exampaper_id) };
            let exampaper = await db.collection('exampaper').findOne(cond);
            if (!exampaper) {
                return {};
            }
            exampaperArr.push(exampaper);
            let { questions, knowledgeModules } = await _getExampaperStatistics(exampaper);
            return { exampaper_id: parseInt(exampaper_id), questions, knowledges: knowledgeModules };
        }));
        //let { period, subject } = exampaperArr[0];
        //let knowledgeMap = await getCachedTree(period, subject);
        let years = exampaperArr.map(e => e.to_year).sort((a, b) => a - b);
        let fullMap = {};
        let yearKnowledgeMap = {};
        for (let s of statistics) {
            let exam = exampaperArr.find(b => b._id === s.exampaper_id);
            if (!exam) {
                continue;
            }
            let knowledges = s.knowledges;
            let moduleMap = {};
            for (let first of knowledges) {
                if (!fullMap[first.name]) {
                    fullMap[first.name] = {};
                }
                moduleMap[first.name] = first;
                let pointMap = {};
                for (let second of first.children) {
                    fullMap[first.name][second.name] = 1;
                    pointMap[second.name] = second;
                }
                moduleMap[first.name].children = pointMap;
            }
            yearKnowledgeMap[exam.to_year] = moduleMap;
        }

        //const _default = { order: 100000, children: {} };
        //const compare = (a, b) => (knowledgeMap[a] || _default).order - (knowledgeMap[b] || _default).order;
        let allModules = Object.keys(fullMap); //.sort(compare);

        let yearColumns = [];
        let yearPropMap = {};
        for (let i = 0; i < years.length; i++) {
            let column = {
                label: `${years[i]}`,
                prop: `year${i + 1}`,
            };
            yearColumns.push(column);
            yearPropMap[years[i]] = column.prop;
        }
        let moduleColumns = [{
            label: '知识模块/分值/年份',
            prop: 'name',
        }, ...yearColumns];
        let pointScoreColumns = [{
            label: '知识点/分值/年份',
            prop: 'name',
        }, ...yearColumns];
        let pointTimesColumns = [{
            label: '知识点/考频/年份',
            prop: 'name'
        }, ...yearColumns];

        let moduleData = [];
        let knowledgePoints = [];
        for (let i = 0, moduleName; i < allModules.length; i++) {
            moduleName = allModules[i];
            let data = { name: moduleName };

            for (let year of years) {
                data[yearPropMap[year]] = {
                    year,
                    value: (yearKnowledgeMap[year][moduleName] || {}).score || 0,
                };
            }
            moduleData.push(data);

            let pointScoreTable = {
                columns: pointScoreColumns,
                data: [],
            };
            let pointTimesTable = {
                columns: pointTimesColumns,
                data: [],
            };
            let points = Object.keys(fullMap[moduleName]);
            // let points = Object.keys(fullMap[moduleName]).sort((a, b) => {
            //     ((knowledgeMap[moduleName] || _default).children[a] || _default).order - ((knowledgeMap[moduleName] || _default).children[b] || _default).order;
            // });
            for (let j = 0, pointName; j < points.length; j++) {
                pointName = points[j];
                let scoreData = { name: pointName };
                let timesData = { name: pointName };
                for (let year of years) {
                    scoreData[yearPropMap[year]] = {
                        year,
                        value: (((yearKnowledgeMap[year][moduleName] || {}).children || {})[pointName] || {}).score || 0,
                    };
                    timesData[yearPropMap[year]] = {
                        year,
                        value: (((yearKnowledgeMap[year][moduleName] || {}).children || {})[pointName] || {}).exam_times || 0,
                    };
                }
                pointScoreTable.data.push(scoreData);
                pointTimesTable.data.push(timesData);
            }
            knowledgePoints.push({
                name: moduleName,
                scores: pointScoreTable,
                times: pointTimesTable,
            });
        }

        let knowledgeModules = {
            columns: moduleColumns,
            data: moduleData,
        };
        let result = {
            knowledge_modules: knowledgeModules,
            knowledge_points: knowledgePoints
        };
        resWrapper.succ(result);
    } catch (err) {
        err.message = `dmp_exampaper.getManyExampaperStatistics: ${err.message}`;
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

// 通过搜索引擎获取试卷列表的条件
const exampaperBySearchParams = (params) => {
    let cond = {};
    if ('高中' === params.period && '数学' === params.subject) {
        if (!params.subject_class) {
            throw new Error('高中数学要传文理科');
        }
        cond['limit'] = 15;
    } else {
        cond['limit'] = 8;
    }

    if (params.period === '高中') {
        cond.grade = '高考专题';
        cond.type = '高考真卷';
    } else if (params.period === '初中') {
        cond.grade = '中考专题';
        cond.type = '中考真卷';
    } else if (params.period === '小学') {
        cond.grade = '小升初专题';
        cond.type = '小升初真卷';
    }
    if (params.subject) {
        cond['subject'] = params.subject;
    }
    if (params.period) {
        cond['period'] = params.period;
    }
    if (params.province) {
        cond['province'] = params.province;
    }
    if (params.city) {
        cond['city'] = params.city;
    }
    if (params.subject_class) {
        cond['subject_class'] = params.subject_class;
    }

    cond['sort_by'] = [{ to_year: -1 }];
    cond['offset'] = 0;
    return cond;
};

// 按照searchData的顺序排
const filterPapers = (exampapers, searchData, cond) => {
    let { period, subject, subject_class } = cond;
    let examArr = [];
    let json = {};
    for (let i = 0, len = exampapers.length; i < len; i++) {
        json[exampapers[i]._id] = exampapers[i];
    }
    let yearObj = {}; // 用于年级唯一
    for (let j = 0, len = searchData.length; j < len; j++) {
        let exam = json[searchData[j]];
        if (exam.to_year < 2015) {
            break;
        }
        if ('高中' === period && '数学' === subject) { // 高中数学有文理分科
            if ('文科' === subject_class) {
                if (exam.name.includes('文科')) {
                    if (!yearObj[exam.to_year]) {
                        examArr.push(exam);
                        yearObj[exam.to_year] = true;
                    }
                }
            } else if ('理科' === subject_class) {
                if (!exam.name.includes('文科')) {
                    if (!yearObj[exam.to_year]) {
                        examArr.push(exam);
                        yearObj[exam.to_year] = true;
                    }
                }
            }
        } else {
            if (!yearObj[exam.to_year]) {
                examArr.push(exam);
                yearObj[exam.to_year] = true;
            }
        }
        if (examArr.length === 5) { // 取5年
            break;
        }
    }
    return examArr;
};

// 获取试卷分析tabs（kbp调用）
const getTabs = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);
    try {
        let cond = exampaperBySearchParams(req.query);
        let uri = url.format({
            protocol: Config.SE_API_SERVER.protocol,
            hostname: Config.SE_API_SERVER.hostname,
            port: Config.SE_API_SERVER.port,
            pathname: '/se_kb/v2/filter/exampapers',
            search: qs.stringify({
                api_key: Config.SE_API_SERVER.appKey
            })
        });
        let seResult = await axios.post(uri, cond, { timeout: 10000 });
        let resultData = seResult.data;
        if (resultData.code !== 0) {
            logger.error('试卷分析/se_kb/v2/filter/exampapers;' + resultData.code);
            return resWrapper.succ([]);
        }
        let searchData = resultData.data.datas || [];
        let proj = { to_year: 1, name: 1 };
        let exampapers = await db.collection('exampaper')
            .find({ _id: { '$in': searchData } }, { readPreference: 'secondaryPreferred' })
            .project(proj)
            .toArray();
        exampapers = filterPapers(exampapers, searchData, cond);

        let exampaperIdArr = [];
        let singleExampapersArr = exampapers.map((exam) => {
            let r = {
                type: 'single',                   // many: 多个试卷分析汇总，single: 单个年份试卷分析
                name: exam.name,                       // tab展示名字
                exampaper_id: exam._id
            };
            exampaperIdArr.push(exam._id);
            return r;
        });
        let result = [];
        if (singleExampapersArr.length > 1) {
            let yearN = {
                type: 'many',               // many: 多个试卷分析汇总，single: 单个年份试卷分析
                name: '近五年试卷整体分析',        // tab展示名字
                exampaper_id: exampaperIdArr.join(',')
            };
            result.push(yearN);
        }
        result = result.concat(singleExampapersArr);
        resWrapper.succ(result);
    } catch (err) {
        err.message = `dmp_exampaper.getTabs: ${err.message}`;
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

// 试卷分析概览查询的条件
const getCommentCond = (query) => {
    let cond = {
        exampaper_id: Number(query.exampaper_id) || 0,
        period: query.period,
        subject: query.subject,
        province: query.province,
        city: query.city || '',
        subject_class: query.subject_class || ''
    };
    return cond;
};

// 试卷分析概览查询
const getComment = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);
    try {
        let cond = getCommentCond(req.query);
        let exampaper = await db.collection('exampaper_brief').find(cond).limit(1).project({ comment: 1 }).toArray();
        let result = exampaper[0] || {};
        resWrapper.succ({ comment: result.comment || '' });
    } catch (err) {
        err.message = `dmp_exampaper.getComment: ${err.message}`;
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

const setCommentData = (body) => {
    let cond = {};
    let data = {};
    let exampaperId = body.exampaper_id || 0;
    if (exampaperId) {
        cond = { exampaper_id: exampaperId };
        data = {
            period: body.period,
            subject: body.subject,
            province: body.province,
            city: body.city || '',
            subject_class: body.subject_class || '',
            comment: body.comment,
            utime: new Date()
        };
    } else {
        cond = {
            exampaper_id: exampaperId,
            period: body.period,
            subject: body.subject,
            province: body.province,
            city: body.city || '',
            subject_class: body.subject_class || ''
        };
        data = {
            comment: body.comment,
            utime: new Date()
        };
    }
    return { cond, data };
};

// 试卷分析概览保存
const setComment = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);
    try {
        briefSchema.putValidate(req.body);
        let { cond, data } = setCommentData(req.body);
        let exampaper = await db.collection('exampaper_brief').updateOne(cond, { $set: data }, { upsert: true });
        if (exampaper.result.ok === 1) {
            resWrapper.succ({});
        } else {
            resWrapper.error('PARAMETERS_ERROR', '更新失败');
        }
    } catch (err) {
        err.message = `dmp_exampaper.setComment: ${err.message}`;
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getSingleExampaperStatistics,
    getManyExampaperStatistics,
    getTabs,
    getComment,
    setComment,
};