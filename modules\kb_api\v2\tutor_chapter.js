const assert = require('assert');
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const _ = require('underscore');

function tutorChapterParams(req) {
    let params = {};
    if (req.params.id.indexOf(',') !== -1) {
        let idsStr = req.params.id.split(',');
        let idsArr = [];
        idsStr.forEach(function (idStr) {
            let curNum = parseInt(idStr);
            if (isNaN(curNum)) {
                throw ('id 非法');
            }
            idsArr.push(curNum);
        });
        params['id'] = idsArr;
    } else {
        params['id'] = parseInt(req.params.id);
        if (isNaN(params['id'])) {
            throw ('id 非法');
        }
    }
    if (req.query.hasOwnProperty('level')) {
        assert(['0', '1', '2', '3'].indexOf(req.query.level) !== -1, 'level error');
        params['level'] = parseInt(req.query.level);
    }
    return params;
}

async function tutorChapterFunc(params) {
    let cond = {};
    if (typeof params['id'] === 'number') {
        cond['_id'] = params['id'];
    } else {
        cond['_id'] = { $in: params['id'] };
    }
    let items = await db.collection('tutor_manual_chapter').find(cond).toArray();
    items.forEach(function (item) {
        item['id'] = item['_id'];
        delete item['_id'];
    });
    if (Array.isArray(params['id'])) {
        items.sort(function (x, y) {
            return params['id'].indexOf(x.id) - params['id'].indexOf(y.id);
        });
    }
    // 是否需要过滤level
    if (params['level'] && ([1, 2, 3].indexOf(params['level']) !== -1)) {
        if (typeof params['id'] === 'number') { // 只获取一个id，并且过滤level
            if (items.length === 0) {
                throw new Error('NULL_ERROR');
            } else {
                let retObj = items[0];
                retObj.levels = _.filter(retObj.levels, function (level) {
                    return level.num === params['level'];
                });
                return retObj;
            }
        } else {
            // 获取多个id详情 并且过滤level
            items.forEach(function (item) {
                item.levels = _.filter(item.levels, function (level) {
                    return level.num === params['level'];
                });
            });
            return items;
        }
    } else {    // 不需要过滤level
        if (typeof params['id'] === 'number') {
            if (items.length === 0) {
                throw new Error('NULL_ERROR');
            } else {
                let retObj = items[0];
                return retObj;
            }
        } else {
            return items;
        }
    }
}

async function tutor_chapter(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        params = tutorChapterParams(req);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    try {
        let result = await tutorChapterFunc(params);
        responseWrapper.succ(result);
    } catch (err) {
        Logger.error(err);
        responseWrapper.error('NULL_ERROR');
    }
}

const getKnowledgeTreeChapterTutor = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let id = req.params.id * 1;
        let level = req.query.level * 1;
        let manual = await db.collection('tutor_manual_chapter').findOne({ 'nodes.id': id });
        if (!manual) {
            return responseWrapper.error('NULL_ERROR');
        }
        delete manual.nodes
        if (!isNaN(level)) {
            manual.levels = _.filter(manual.levels, (x) => {
                return x.num === level;
            })
        }
        manual.id = manual._id;
        delete manual._id;
        return responseWrapper.succ(manual)
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR')
    }
};

module.exports = {
    tutor_chapter: tutor_chapter,
    getKnowledgeTreeChapterTutor: getKnowledgeTreeChapterTutor,
};
