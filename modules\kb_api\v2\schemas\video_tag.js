
const value = {
    type: 'object',
    required: ['name'],
    properties: {
        name: {
            type: 'string',
        }
    }
};
// 视频
const putVideoTag = {
    type: 'object',
    additionalProperties: false,
    required: ['subject', 'period', 'name', 'tag_type', 'values'],
    properties: {
        subject: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        name: {
            type: 'string',
        },
        tag_type: {
            type: 'string',
        },
        values: {
            type: 'array',
            minItems: 1,
            items: value
        }
    }
};

// 视频专辑
const putVideoCateTag = {
    type: 'object',
    additionalProperties: false,
    required: ['subject', 'period', 'name', 'tag_type', 'values'],
    properties: {
        subject: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        name: {
            type: 'string',
        },
        tag_type: {
            type: 'string',
        },
        values: {
            type: 'array',
            minItems: 1,
            items: value
        }
    }
};

module.exports = {
    putVideoTag,
    putVideoCateTag,
};
