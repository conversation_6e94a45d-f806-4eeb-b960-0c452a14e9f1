const auth = require('../../modules/kb_api/v2/auth');
const book = require('../../modules/kb_api/v2/book');
const book_batch = require('../../modules/kb_api/v2/book/batch');
const ebook = require('../../modules/kb_api/v2/ebook');
const region = require('../../modules/kb_api/v2/region');
const resource = require('../../modules/kb_api/v2/resource');
const eng_src = require('../../modules/kb_api/v2/eng_src');
const cm_api = require('../../modules/kb_api/v2/common');
const table = require('../../modules/kb_api/v2/tw_specification');
const homework = require('../../modules/kb_api/v2/homework');
const guide_book = require('../../modules/kb_api/v2/guide_book');
const region_book = require('../../modules/kb_api/v2/region_book');
const school_book = require('../../modules/kb_api/v2/school_book');
const tutor_manual = require('../../modules/kb_api/v2/tutor_manual');
const tutor_chapter = require('../../modules/kb_api/v2/tutor_chapter');
const counter = require('../../modules/kb_api/v2/counter');
const textBook = require('../../modules/kb_api/v2/text_book');
const catalog = require('../../modules/kb_api/v2/catalog');
const educationassistantbook = require('../../modules/kb_api/v2/education_assistant_book');
const magicCard = require('../../modules/kb_api/v2/magic_card');
const englishWord = require('../../modules/kb_api/v2/englishWord');
const scoreline = require('../../modules/kb_api/v2/exam_analysis/score_line');
const color_note = require('../../modules/kb_api/v2/dmp_resource/color_note');
const learning_video = require('../../modules/kb_api/v2/dmp_resource/learning_video');
const region_exam = require('../../modules/kb_api/v2/dmp_resource/region_exam');
const common_error = require('../../modules/kb_api/v2/common_error');
const educationassistantfile = require('../../modules/kb_api/v2/education_assistant_file');
const educationassistanttool = require('../../modules/kb_api/v2/education_assistant_tool');


const knowledge = require('./knowledge/index');
const videoResource = require('./video/resource');
const videoTag = require('./video/tag');
const question = require('./question/question');
const questionTag = require('./question/question_tag');
const element = require('./question/element');
const exampaper_analysis = require('./exampaper/exampaper_analysis');
const dmp_exampaper = require('./exampaper/dmp_exampaper');
const exampaper = require('./exampaper/exampaper');
const region_exam_score = require('./region_press/region_exam_score');
const grade_order = require('./region_press/grade_order');
const exam_analy = require('./exam_analy/index');
const landmark = require('./landmark');

//3.3删除教材教辅接口
const temp_v33 = require('../../modules/kb_api/v2/temp_v33');

const resource_album = require('./resource_album');

module.exports = function (router) {
    // 认证接口
    router.get('/api_key/', auth.api_key);

    // 请求教材信息（打包信息）
    router.get('/books/', book.books);

    // 检查教材是否冲突
    router.get('/books/check/', book.check_book_conflict);

    // 批量请求某教材下所有章节及其知识点集 根据多个 id
    router.get('/books/detail', book_batch.batchGetBookDetail);
    // 请求某教材下所有章节及其知识点集
    router.get('/books/:book_id', book.book);
    // 请求某教材下所有知识点列表
    router.get('/books/knowledges/by_book/:book_id', book.getKnowledgesByBook);

    // 请求某本教材结构内容
    router.get('/books/:book_id/struct', book.book_struct);
    // 修改某本教材结构内容
    // router.put('/books/:book_id/struct', book.put_book_struct);
    // 新建教材
    // router.post('/books/', book.post_book);
    // 根据教材id复制教材
    router.post('/books/:book_id/dup/', book.dup_book);

    // 请求某个章节id内容
    router.get('/book_chapters/:book_chapter_id/', book.book_chapter);
    // 修改某个章节id内容
    router.put('/book_chapters/:book_chapter_id/', book.put_book_chapter);
    // 同步教材chapter
    router.post('/book_chapters/', book.post_book_chapters);
    // 同步教材结构
    router.post('/books/struct/', book.post_book_struct);
    //调整教材编目顺序
    router.put('/book_catalogs/', book.put_book_catalog);

    // 删除教材内容
    router.delete('/books/:id', book.delete_book);

    exampaper(router); // 试卷相关接口
    exampaper_analysis(router); // kbp试卷分析 放在dmp_exampaper的前面，因为路由匹配
    dmp_exampaper(router); // dmp 根据试卷ID获取试卷试题细目和知识点分值/考频统计
    question(router); // 试题相关接口
    knowledge(router); // 知识点相关路由
    videoResource(router); // 视频资源路由
    videoTag(router); // 视频标签路由
    region_exam_score(router); // 地区考试管理
    grade_order(router); // 教材顺序管理
    exam_analy(router); // 考情分析
    landmark(router);
    resource_album(router); // 资源专辑
    /* 电子书相关接口 */
    // 请求某学科下图书列表
    router.get('/ebooks/', ebook.ebooks);
    // 根据图书id号获取图书数据资源
    router.get('/ebooks/:ebook_id([0-9]+)/', ebook.ebook);
    // 根据图书id号下载图书资源
    router.get('/ebooks/:ebook_id/download/', ebook.ebook_download);
    // 获取热门书单
    router.get('/ebooks/popularity/', ebook.popularity);

    /* 地区相关接口 */
    // 获取地区
    router.get('/regions/', region.regions);
    // 获取全量二级地区
    router.get('/regions/simple/', region.simple_regions);

    /* 基础资源相关接口 */
    // 获取基础资源
    router.post('/resources/', cm_api.resources);
    // 修改基础资源
    router.put('/resources/', cm_api.put_resources);
    // 修改基础资源
    router.get('/resources/indexes', cm_api.get_resources_indexes);

    // 全局资源统计信息
    router.get('/resources/profile/', resource.profile);
    router.get('/resources/profile/new', resource.profileNew);
    router.get('/resources/profile/video', resource.getVideoStatistics); // TODO 待删除
    router.get('/resources/profile/exam/update', resource.getExamUpdateStatistics); // TODO 待删除
    router.get('/resources/profile/exampaper/region', resource.getExampaperRegionStatistic); // TODO 待删除
    // 获取更新统计信息
    router.get('/resources/update', resource.update);
    router.get('/resources/hots', resource.newHot);
    router.get('/resources/region', resource.region);
    router.get('/resources/update/exampaper/subject', resource.exampaperUpdateSubject);
    // 获取试卷资源的统计信息
    router.get('/resources/filters/', resource.filters);
    // 获取资源的seq id
    router.get('/resources/id/', resource.getSeqIds);

    // 同步更新试卷标签
    router.put('/:resource_filter_id/filters/', resource.updateResourceFilter);

    // 最近更新数量
    router.get('/resources/new/num', resource.newNum);

    // 创建双向细目表
    router.post('/tw_specifications/', table.createTable);
    router.post('/tw_specifications/exampapers', table.createExampaperByTable);
    router.post('/tw_specifications/knowledge', table.getUnableKnowledge);

    // 查看我的双向细目表
    router.get('/tw_specifications/:table_ids/list', table.getTableList);
    router.get('/tw_specifications', table.getTableByRefId);
    // 修改某双向细目表
    router.put('/tw_specifications/:table_id', table.updateTable);
    router.get('/tw_specifications/:table_id/info', table.getTableById);
    router.get('/tw_specifications/hot', table.getHotTable);
    router.post('/tw_specifications/search', table.searchTable); // 搜索细目表
    router.delete('/tw_specifications/hot', table.deleteTable);
    router.delete('/tw_specifications/:table_id', table.deleteTable);
    router.post('/tw_specifications/:table_id/download-times', table.downloadTimesInc);

    // 获取英语单词信息
    router.get('/eng_words/:ids/', eng_src.eng_words);
    router.get('/eng_sentences/:ids/', eng_src.eng_sentences);
    router.get('/eng_dialogs/:ids/', eng_src.eng_dialogs);
    router.get('/eng_words/:id/top/', eng_src.get_eng_word_top);
    router.get('/eng_sentences/:id/top/', eng_src.get_eng_sentence_top);
    router.put('/eng_words/:id/score/', eng_src.put_eng_word_score);
    router.put('/eng_sentences/:id/score/', eng_src.put_eng_sentence_score);

    router.get('/book_answers/subjects', homework.getSubjects);
    router.get('/books/answer', homework.getBooksCategory);
    router.get('/book_chapters/:chapter_ids/answer', homework.getChapters);
    router.get('/books/:book_id/answer', homework.getBook);

    //天天练习相关接口
    router.get('/guide_books', guide_book.guideBooks);
    router.get('/guide_books/:guide_book_id', guide_book.guideBookStrcut);
    router.get('/guide_chapters/:chapter_id/', guide_book.guideChapter);

    //学校教材资源
    router.get('/school_books/schools', school_book.get_school_books_schools);
    router.put('/school_books/schools/:sch_id/region', school_book.put_school_books_region);
    router.put('/school_books/schools/:sch_id/books', school_book.put_school_books_book);
    router.get('/school_books/schools/:sch_id', school_book.get_school_books_school_books);

    router.post('/school_books/schools', school_book.add_school_books);
    router.put('/school_books/schools/:sch_id', school_book.put_school_books);
    router.delete('/school_books/schools/:sch_id', school_book.delete_school_books);

    // 查询区域学校列表接口
    router.get('/school_books/region/schools', school_book.getRegionSchools);
    // 查询学校简介
    router.get('/school_books/region/:sch_id/brief', school_book.getSchoolBrief);
    // 保存学校简介
    router.put('/school_books/region/:sch_id/brief', school_book.updateSchoolBrief);

    //区域教材资源
    router.get('/region_books/statistics', region_book.get_region_book_statistics);
    router.get('/region_books/regions', region_book.get_region_book_regions);
    router.put('/region_books/:id', region_book.put_region_book_by_id);
    router.put('/region_books/regions/:rb_id/books', region_book.put_region_book_regions);
    router.get('/region_books/:rb_id', region_book.get_region_book_by_id);

    // 知识体系相关接口
    //获取辅导手册打包接口
    router.get('/tutor_manuals/', tutor_manual.tutor_manuals);
    //根据id获取辅导手册
    router.get('/tutor_manuals/:id', tutor_manual.tutor_manual);
    router.get('/tutor_manual_chapters/:id', tutor_chapter.tutor_chapter);
    router.get('/knowledge_tree_chapters/:id/tutor_chapter', tutor_chapter.getKnowledgeTreeChapterTutor);
    router.get('/knowledge_trees/:id/tutor_manual', tutor_manual.getKnowledgeTreeTutor);

    // 魔术卡
    router.get('/magicCard/list', magicCard.getMagicCardList);
    router.post('/magicCard', magicCard.createMagicCard);
    router.put('/magicCard/:id', magicCard.editMagicCard);
    router.delete('/magicCard/:id/:type/remove', magicCard.delMagicCard);
    router.get('/magicCard/:id/detail', magicCard.getMagicCardDetail);
    router.get('/magicCard/by_knowledge', magicCard.getMagicCardByKnowledgeId);

    router.put('/magicCard/:id/knowledge', magicCard.editMagicCardKnowledge);

    router.put('/resources/downloads', resource.incDownloads);
    router.get('/counter', counter.getCounter);
    router.put('/counter', counter.setCounter);

    questionTag(router); // 试题标签相关
    element(router); // 叠加要素相关

    //kbp_v3.2新增电子教材
    //获取编目信息
    router.get('/text_books/catalogs', textBook.getCatelogs);
    //获取编目下教材信息
    router.get('/text_books/list', textBook.getTextBooks);
    //获取教材信息
    router.get('/text_books/:id', textBook.getTextbookById);
    //kbp_v3.3新增教材管理
    router.post('/text_books/add', textBook.addTextbook);
    //修改教材
    router.put('/text_books/:id', textBook.updateTextbook);

    //kbp_v3.3新增电子教辅+编目管理
    //编目管理
    router.post('/text_books/catalogs/add', catalog.updateCatalogs);
    //编目是否可删
    router.get('/text_books/catalogs/canUpdate', catalog.updateRes);

    //电子教辅
    //获取编目信息
    //router.get('/education_assistant_books/catalogs', educationassistantbook.getCatelogs);
    //获取编目下教辅信息
    router.get('/education_assistant_books/list', educationassistantbook.getEducationAssistantBooks);
    //获取教辅信息
    router.get('/education_assistant_books/:id', educationassistantbook.getEducationAssistantBookById);
    //kbp_v3.3新增教辅管理
    router.post('/education_assistant_books/add', educationassistantbook.addEducationAssistantBook);
    //修改教辅
    router.put('/education_assistant_books/:id', educationassistantbook.updateEducationAssistantBook);

    //删除kbpv3.3测试数据接口
    router.delete('/education_assistant_books/:id', temp_v33.education_assistant_books);
    router.delete('/text_books/:id', temp_v33.text_books);

    //kbp_v3.4录取分数线相关
    router.get('/score_lines/list', scoreline.getLines);
    router.put('/score_lines/edit', scoreline.updateLines);
    router.get('/score_lines/exam_description', scoreline.getDesc);
    router.put('/score_lines/exam_description', scoreline.updateDesc);
    router.get('/score_lines/kb_exampaper/:id', scoreline.getkbexampaper);

    //三色笔记
    router.get('/color_notes', color_note.getResources);
    router.post('/color_notes/count_by_knowledge', color_note.countByKnowledges);
    router.post('/color_notes/save_resource', color_note.saveResource);
    router.put('/color_notes/set_knowledge', color_note.setKnowledges);
    router.put('/color_notes/set_note', color_note.setNote);
    router.delete('/color_notes/remove_note', color_note.deleteNote);

    //学习视频（预习复习视频）
    router.get('/learning_videos', learning_video.getResources);
    router.post('/learning_videos/count_by_knowledge', learning_video.countByKnowledges);
    router.post('/learning_videos/save_resource', learning_video.saveResource);
    router.put('/learning_videos/set_knowledge', learning_video.setKnowledges);
    router.put('/learning_videos/edit_resource', learning_video.editResource);
    router.delete('/learning_videos/remove_knowledge', learning_video.removeKnowledge);

    //命题平台查看试题数字化试卷(根据g_paper_id查看试卷)
    router.get('/mt_exampaper/:g_paper_id', scoreline.getmtexampaper);

    // 区域考试分析（dmp）
    router.post('/region_exam/statistics', region_exam.getRegionExamStatistic);
    router.post('/region_exam/knowledges', region_exam.getRegionExamKnowledge);
    router.post('/region_exam/questions', region_exam.getRegionExamQuestion);
    router.get('/region_exam/questions/answer', region_exam.getRegionExamQuestionAnswer);
    router.post('/region_exam/school', region_exam.getRegionExamSchool);

    //单词列表
    router.get('/english_words/list', englishWord.getWordsList);
    //创建单词
    router.post('/english_words', englishWord.createWord);
    //修改单词
    router.put('/english_words/:word_id', englishWord.updateWord);
    //基于单词id，请求单词信息
    router.get('/english_words/:word_id', englishWord.getWord);
    //基于单词id，删除单词信息
    router.delete('/english_words/:word_id', englishWord.removeWord);
    //查询是否存在
    router.get('/english_words/:word/exists', englishWord.existsWord);

    // ------------------共性错题模块---------------------
    // 对外提供-共性错题定义
    router.get('/common_errors/defs/config', common_error.getDefsConfig);
    // 对外提供-共性错题推题
    router.get('/common_errors/recommend_nums/config', common_error.getRecommendConfig);
    // 查询共性错题定义
    router.get('/common_errors/defs', common_error.getDefs);
    // 根据id查询共性错题定义
    router.get('/common_errors/defs/:def_id', common_error.getDefById);
    // kb库更新共性错题定义
    router.put('/common_errors/defs/:def_id', common_error.updateDef);
    // 查询共性错题推题
    router.get('/common_errors/recommend_nums/', common_error.getRecommend);
    // 根据id查询共性错题推题
    router.get('/common_errors/recommend_nums/:recommend_id', common_error.getRecommendById);
    // kb库更新共性错题推题
    router.put('/common_errors/recommend_nums/:recommend_id', common_error.updateRecommend);
    //  获取学校信息
    router.get('/schools/list', school_book.getSchoolsByIdAndName);

    // 创建某个章节ID的同步本
    router.post('/chapter_books', book.postChapterBook);
    // 修改某个章节ID的同步本
    router.put('/chapter_books/:chapter_book_id', book.putChapterBook);
    // 获取某个章节ID的同步本
    router.get('/chapter_books/:chapter_book_id', book.getChapterBook);

    // 教辅文件
    // 获取列表-不使用
    router.get('/education_assistant_files/filters', educationassistantfile.getFilters);
    // 查询列表
    router.get('/education_assistant_files/list', educationassistantfile.getList);
    // 获取教材下资源数量
    router.get('/education_assistant_files/book/:id/total', educationassistantfile.getBookFileTotal);
    // 获取详细
    router.get('/education_assistant_files/:id', educationassistantfile.getById);
    // 根据
    router.get('/education_assistant_files/:ids/list', educationassistantfile.getByIds);
    // 新增
    router.post('/education_assistant_files', educationassistantfile.add);
    // 修改
    router.put('/education_assistant_files', educationassistantfile.update);
    // 增加浏览或者下载次数
    router.put('/education_assistant_files/times', educationassistantfile.updateTimes);
    // 删除
    router.delete('/education_assistant_files', educationassistantfile.delByIds);

    // 教学工具
    // 分类
    router.get('/education_assistant_tools/category', educationassistanttool.getCategory);
    // 增加分类
    router.post('/education_assistant_tools/category', educationassistanttool.addCategory);
    // 修改分类
    router.put('/education_assistant_tools/category/:id', educationassistanttool.updateCategory);
    // 删除分类
    router.delete('/education_assistant_tools/category/:id', educationassistanttool.deleteCategory);
    // 排序
    router.put('/education_assistant_tools/category/:id/order', educationassistanttool.updateCategoryOrder);
    // 工具列表
    router.get('/education_assistant_tools/list', educationassistanttool.getList);
    // 根据ID获取列表
    router.get('/education_assistant_tools/:ids/list', educationassistanttool.getByIds);
    // 根据ID获取
    router.get('/education_assistant_tools/:id', educationassistanttool.getById);
    // 添加工具
    router.post('/education_assistant_tools', educationassistanttool.add);
    // 修改工具
    router.put('/education_assistant_tools', educationassistanttool.update);
    // 删除
    router.delete('/education_assistant_tools', educationassistanttool.delByIds);
    // 增加浏览或者下载次数
    router.put('/education_assistant_tools/times', educationassistanttool.updateTimes);

};
