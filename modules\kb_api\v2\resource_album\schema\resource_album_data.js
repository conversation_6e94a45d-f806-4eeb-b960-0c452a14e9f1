const ajv = require('ajv');
const ajvInstance = new ajv({
    removeAdditional: true,
});


const createResourceAlbumDataSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        name: { type: 'string' },
        title: { type: 'string' },
        subtitle: { type: 'string' },
        description: { type: 'string' },
        view_times: { type: 'integer' },
        download_times: { type: 'integer' },
        resource_count: { type: 'integer' },
        children: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    type: { type: 'integer' },
                    title: { type: 'string' },
                    style: {
                        anyOf: [
                            {
                                type: 'object',
                                properties: {
                                    key: { type: 'string' }
                                }
                            },
                            {
                                type: 'array',
                                items: {
                                    type: 'string',
                                }
                            }
                        ]
                    },
                    children: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                type: { type: 'integer' },
                                label: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            value: {
                                                type: 'array',
                                                items: {
                                                    type: 'object',
                                                    properties: {
                                                        value: { type: 'string' },
                                                    }
                                                }
                                            },
                                            style: {
                                                anyOf: [
                                                    {
                                                        type: 'object',
                                                        properties: {
                                                            key: { type: 'string' }
                                                        }
                                                    },
                                                    {
                                                        type: 'array',
                                                        items: {
                                                            type: 'string',
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                },
                                content: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            id: { type: ['string', 'integer'] }
                                        },
                                        required: ['id']
                                    },
                                },
                                style: {
                                    anyOf: [
                                        {
                                            type: 'object',
                                            properties: {
                                                key: { type: 'string' }
                                            }
                                        },
                                        {
                                            type: 'array',
                                            items: {
                                                type: 'string',
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        },
        dbs: {
            type: 'array',
            items: {
                type: 'string',
            }
        },
    }
};


const updateResourceAlbumDataSchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        name: { type: 'string' },
        title: { type: 'string' },
        subtitle: { type: 'string' },
        description: { type: 'string' },
        view_times: { type: 'integer' },
        download_times: { type: 'integer' },
        resource_count: { type: 'integer' },
        children: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    type: { type: 'integer' },
                    title: { type: 'string' },
                    style: {
                        anyOf: [
                            {
                                type: 'object',
                                properties: {
                                    key: { type: 'string' }
                                }
                            },
                            {
                                type: 'array',
                                items: {
                                    type: 'string',
                                }
                            }
                        ]
                    },
                    children: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                type: { type: 'integer' },
                                label: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            value: {
                                                type: 'array',
                                                items: {
                                                    type: 'object',
                                                    properties: {
                                                        value: { type: 'string' },
                                                    }
                                                }
                                            },
                                            style: {
                                                anyOf: [
                                                    {
                                                        type: 'object',
                                                        properties: {
                                                            key: { type: 'string' }
                                                        }
                                                    },
                                                    {
                                                        type: 'array',
                                                        items: {
                                                            type: 'string',
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                },
                                content: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            id: { type: ['string', 'integer'] }
                                        },
                                        required: ['id']
                                    },
                                },
                                style: {
                                    anyOf: [
                                        {
                                            type: 'object',
                                            properties: {
                                                key: { type: 'string' }
                                            }
                                        },
                                        {
                                            type: 'array',
                                            items: {
                                                type: 'string',
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        },
        dbs: {
            type: 'array',
            items: {
                type: 'string',
            }
        },
        valid: {
            type: 'integer',
            enum: [0, 1]
        }
    }
};




const createResourceAlbumDataValidator = ajvInstance.compile(createResourceAlbumDataSchema);
const updateResourceAlbumDataValidator = ajvInstance.compile(updateResourceAlbumDataSchema);

module.exports = {
    createResourceAlbumDataValidator,
    updateResourceAlbumDataValidator,
};