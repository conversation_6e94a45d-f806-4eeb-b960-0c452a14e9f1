const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const Logger = require('../../../common/utils/logger');

const putSchema = {
    type: 'object',
    additionalProperties: false,
    required: ['exampaper_id', 'subject', 'period', 'province', 'city', 'subject_class', 'comment'],
    properties: {
        exampaper_id: { type: 'number' },
        subject: { type: 'string', },
        period: { type: 'string', },
        province: { type: 'string', },
        city: { type: 'string', },
        subject_class: { type: 'string' },
        comment: { type: 'string' },
    }
};

const putValidate = (data) => {
    if (!ajv.validate(putSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('格式错误');
    }
};

module.exports = {
    putValidate
};
