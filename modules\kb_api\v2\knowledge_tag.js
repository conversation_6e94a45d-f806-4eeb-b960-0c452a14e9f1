const ObjectID = require('mongodb').ObjectID;
const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const schemaGroup = require('./schemas/target_group');

// 数据库集合名
const groupColl = 'knowledge_tag';
const knowColl = 'knowledge';

/**
 * kbp审核通过，推送过来，添加
 * @param {*} req 
 * @param {*} res 
 */
const candelKnowledgeTag = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let name = req.params.name;
        let types = {
            'dimension': 'dimensions.tags.name',
            'cognition': 'cognitions.tags.name'
        };
        let { period, subject, type } = req.query;
        let ty = types[type];
        if (!ty) {
            return responseWrapper.error('PARAMETERS_ERROR', 'type error');
        }
        if (!period || !subject || !type) {
            return responseWrapper.error('PARAMETERS_ERROR', '请传必要参数');
        }
        let cond = {
            period: period,
            subject: subject,
        };
        cond[ty] = name;
        let result = await db.collection(knowColl).findOne(cond);
        responseWrapper.succ(!result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

/**
  * kbp审核通过，推送过来，更新
  * @param {*} req 
  * @param {*} res 
  */
const updateKnowledgeTag = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    let data = req.body;
    try {
        data.utime = new Date();
        // 更新数据
        let cond = { _id: ObjectID(id) };
        let setData = { $set: data };
        let result = await db.collection(groupColl).updateOne(cond, setData);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '修改出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

/**
 * 获取详情，根据id
 * @param {*} req 
 * @param {*} res 
 */
const getKnowledgeTagById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let cond = { _id: ObjectID(req.params.id) };
        let result = await db.collection(groupColl).findOne(cond);
        if (result) {
            result.id = result._id;
            delete result._id;
        }
        let knowtagcond = {
            period: result.period,
            subject: result.subject,
        };
        if (result.type === 'dimension') {
            knowtagcond.dimensions = { $exists: true };
        } else if (result.type === 'cognition') {
            knowtagcond.cognitions = { $exists: true };
        }
        let knowtag = await db.collection(knowColl).findOne(knowtagcond);
        result.is_used = !!knowtag;
        let values = result.values;
        let types = {
            'dimension': 'dimensions.tags.name',
            'cognition': 'cognitions.tags.name'
        };
        let ty = types[result.type];
        let tagcond = {
            period: result.period,
            subject: result.subject,
        };
        let tagsUsedName = [];
        await Promise.all(values.map(async item => {
            tagcond[ty] = item.name;
            let knowledge = await db.collection(knowColl).findOne(tagcond, { fields: { _id: 1 } });
            if (knowledge) {
                tagsUsedName.push(item.name);
            }
        }));
        result.tags_used = tagsUsedName;
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

/**
 * 获取详情
 * @param {*} req 
 * @param {*} res 
 */
const getKnowledgeTagsList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let { period, subject } = req.query;
    if (!period || !subject) {
        return responseWrapper.error('HANDLE_ERROR', '请传学段学科');
    }
    let cond = { period: period, subject: subject };
    try {
        let resultList = await db.collection(groupColl).find(cond).toArray();
        if (resultList && resultList.length > 0) {
            for (let i = 0; i < resultList.length; i++) {
                resultList[i].id = resultList[i]._id;
                delete resultList[i]._id;
            }
        }
        responseWrapper.succ({ knowledge_tags: resultList });
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

/**
 * kbp审核通过，推送过来，删除
 * @param {*} req 
 * @param {*} res 
 */
const repeatKnowledgeTag = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let name = req.params.name;
        let { period, subject, type } = req.query;
        if (!period || !subject || !type) {
            return responseWrapper.error('HANDLE_ERROR', '请传必要参数');
        }
        let cond = {
            period: period,
            subject: subject,
            type: type,
            'values.name': name
        };
        let result = await db.collection(groupColl).findOne(cond);
        if (result) {
            responseWrapper.succ(false);
        } else {
            responseWrapper.succ(true);
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

module.exports = {
    getKnowledgeTagsList,
    getKnowledgeTagById, // 
    updateKnowledgeTag, // 
    repeatKnowledgeTag, // 
    candelKnowledgeTag, // 
};
