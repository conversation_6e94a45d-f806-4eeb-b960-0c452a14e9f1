/* eslint-disable no-undef */
const config = require('config');
const expect = require('chai').expect;
const superagent = require('superagent');
const url = require('url');
const _ = require('underscore');

const port = process.env.NODE_PORT || 80;

const host = url.format({
	protocol: 'http',
	hostname: config.get('host'),
	port: port
});

describe('获取知识点图片', function () {
	var url = [host, '/kb_api/v2/knowledges/2147418111,2147352575/card_img'].join('');
	it('正例测试', function (done) {
		superagent
			.get(url)
			.query({
				api_key: 'iyunxiao_tester'
			})
			.end(function (err, res) {
				expect(err).to.be.a('null');
				expect(res).to.not.be.an('null');
				expect(res.status).to.be.equal(200);
				var data = JSON.parse(res.text);
				expect(data).to.be.an('array');
				_.each(data, function (x) {
					expect(x).to.have.keys('id', 'url');
					expect(x.id).to.not.be.a('null');
					expect(x.url).to.not.be.a('null');
				});
				done();
			});
	});
});

describe('获取试题图片', function () {
	var url = [host, '/kb_api/v2/questions/3289704959,2888755711/stem_img'].join('');
	it('正例测试', function (done) {
		superagent
			.get(url)
			.query({
				api_key: 'iyunxiao_tester'
			})
			.end(function (err, res) {
				expect(err).to.be.a('null');
				expect(res).to.not.be.an('null');
				expect(res.status).to.be.equal(200);
				var data = JSON.parse(res.text);
				expect(data).to.be.an('array');
				_.each(data, function (x) {
					expect(x).to.have.keys('id', 'url');
					expect(x.id).to.not.be.a('null');
					expect(x.url).to.not.be.a('null');
				});
				done();
			});
	});
});
