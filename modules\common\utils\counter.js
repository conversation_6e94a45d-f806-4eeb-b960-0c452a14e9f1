
const utils = require('./utils');

const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');

function getNextSequenceValue(sequenceName, count, callback) {

	let cond = { _id: sequenceName };
	let update = { $inc: { seq: count }, $set: { utime: new Date() } };
	let opts = { new: true };

	db.collection('counter').findAndModify(cond, [], update, opts, function (err, item) {
		if (err) {
			return callback(err);
		}
		if (item.value == null) {
			return callback('不支持该种类型的数据');
		}
		let retval = [];
		let base = item.value.seq - count;
		for (let ix = 0; ix < count; ix++) {
			retval.push(utils.encode(base + ix + 1));
		}
		return callback(null, retval);
	});
}

const nextSeq = async (sequenceName, count) => {
	const cond = { _id: sequenceName };
	const update = { $inc: { seq: count }, $set: { utime: new Date() } };
	const opts = { new: true };

	try {
		const item = await db.collection('counter').findAndModify(cond, [], update, opts);
		if (item.value == null) {
			throw new Error('不支持该种类型的数据');
		}
		const retval = [];
		const base = item.value.seq - count;
		for (let ix = 0; ix < count; ix++) {
			retval.push(utils.encode(base + ix + 1));
		}
		return retval;
	} catch (e) {
		throw e;
	}
};

module.exports = {
	getNextSequenceValue: getNextSequenceValue,
	nextSeq
};
