const Logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const loosen_tree = require('../utils.js').loosen_tree;
const _getChapterIdsInBook = require('../book')._getChapterIdsInBook;
const _assignChaptersToBook = require('../book')._assignChaptersToBook;
const cacheWrapper = require('../../../common/utils/cache_wrapper');

/**
 * 根据 ID 批量获得知识树详情
 * @param {*} req   - ID 列表
 * @param {*} res
 * @return {Promise<void>}
 */
async function batchGetKnowledgeTreeDetail(req, res) {
    const responseWrapper = new ResponseWrapper(res);
    try {
        let ids = (req.query.ids || '').split(',').map(e => +e).filter(e => e);
        if (ids.length <= 0) {
            responseWrapper.error('PARAMETERS_ERROR', 'ids 参数异常');
            return;
        }
        const knowledgeTreeCollection = db.collection('knowledge_tree');
        const knowledgeTreeChapterCollection = db.collection('knowledge_tree_chapter');
        let records = await knowledgeTreeCollection.find({ _id: { $in: ids } }).toArray();
        // 权限过滤
        const queryDbs = new Set((req.query.dbs || 'basic').split(','));
        records = records.filter(record => (record.dbs || ['basic']).some(item => queryDbs.has(item)));
        if (records.length === 0) {
            responseWrapper.error('HANDLE_ERROR', '未找到知识树');
        }
        await Promise.all(records.map(async (record, index) => {
            let chapterIds = [];
            _getChapterIdsInBook('nodes', record.nodes, chapterIds);
            let chapters = await knowledgeTreeChapterCollection.find({ _id: { $in: chapterIds } }).project({
                ctime: 0,
                utime: 0,
                path: 0
            }).toArray();
            let newRecords = _assignChaptersToBook('knowledge_tree', record, chapters);
            records[index] = newRecords;
            newRecords['id'] = record['_id'];
            delete newRecords['_id'];
            newRecords['knowledge_tree'] = {
                'nodes': newRecords['nodes'],
            };
            delete newRecords['nodes'];
            let keys = {
                'nodes': 'knowledge_node',
                'knowledges': 'knowledge',
            };
            loosen_tree(record['knowledge_tree'], keys);
        }));
        responseWrapper.succ(records);
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e.toString());
    }
}

module.exports = {
    batchGetKnowledgeTreeDetail: cacheWrapper(batchGetKnowledgeTreeDetail, { validTime: 60 * 30 }),
};
