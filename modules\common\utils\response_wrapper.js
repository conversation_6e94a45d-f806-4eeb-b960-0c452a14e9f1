
const RETURN_CODE = {
	'OK': 0,                   // 处理成功
	'URL_ERROR': 1,            // api错误
	'AUTH_ERROR': 2,           // app_key, app_seceret认证信息错误
	'PARAMETERS_ERROR': 3,     // 上送参数错误
	'HANDLE_ERROR': 4,         // 业务处理错误
	'NULL_ERROR': 5,           // 空数据
	'EXCEED_FRQ_ERROR': 6,     // 访问频率过快
	'ILLEGAL_USER': 7,         // 被封的用户
	'NEED_VIP_ERROR': 8,       // 非会员
	'OUT_OF_LIMITS_ERROR': 9   // 数量过多
};

const RETURN_MSG = {
	'OK': 'OK',                                 // 处理成功
	'URL_ERROR': 'api not found',               // api错误
	'AUTH_ERROR': 'authentication error',   	// app_key, app_seceret认证信息错误
	'PARAMETERS_ERROR': 'parameters error',     // 上送参数错误
	'HANDLE_ERROR': 'servercie error',          // 业务处理错误
	'NULL_ERROR': 'cannot query data',          // 查询不到数据
	'EXCEED_FRQ_ERROR': 'api freq out of limit',// 访问频率过快
	'ILLEGAL_USER': 'user is untrusted',        // 被封的用户 
	'NEED_VIP_ERROR': 'user must be vip',       // 用户必须为vip才可以访问
};

function ResponseWrapper(res) {
	this.res = res;
}

ResponseWrapper.prototype.error = function (type, desc, data) {
	if (this.res.finished) {
		return;
	}
	let msg = desc ? desc : RETURN_MSG[type];
	let retval = null;
	if (this.res.resMode === 'normal') {
		this.res.status(200);
		retval = {
			code: RETURN_CODE[type],
			msg: msg,
			data: data || null,
		};
	} else {
		this.res.status(400);
		retval = {
			code: RETURN_CODE[type],
			msg: msg,
		};
	}
	return this.res.json(retval);
};

ResponseWrapper.prototype.succ = function (data) {
	if (this.res.finished) {
		return;
	}
	this.res.status(200);
	let retval = null;
	if (this.res.resMode === 'normal') {
		retval = {
			code: RETURN_CODE['OK'],
			msg: 'OK',
			data: data
		};
	} else {
		retval = data;
		if (this.res.out_of_limits === true) {
			//this.res.status(400);
			retval = {
				code: RETURN_CODE['EXCEED_FRQ_ERROR'],
				msg: RETURN_MSG['EXCEED_FRQ_ERROR'],
				data: data
			};
		}
	}
	return this.res.json(retval);
};

ResponseWrapper.prototype.send = function (data) {
	if (this.res.finished) {
		return;
	}
	this.res.status(400);
	// data.data = data.data || null;
	return this.res.json(data);
};

module.exports = ResponseWrapper;
