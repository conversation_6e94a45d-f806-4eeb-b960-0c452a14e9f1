{"name": "kb_api", "version": "2.0.0", "description": "kb_api's web project building by node", "main": "app.js", "dependencies": {"@hapi/joi": "15.1.0", "@baiducloud/sdk": "^1.0.0-rc.14", "ajv": "^6.5.2", "axios": "^0.18.0", "body-parser": "^1.15.1", "cheerio": "^1.0.0-rc.3", "config": "^1.20.1", "cookie": "^0.3.1", "express": "^4.13.4", "heapdump": "^0.3.12", "ioredis": "^2.3.0", "lodash": "^4.12.0", "log4js": "^0.6.36", "mongodb": "^2.2.36", "path-to-regexp": "^1.7.0", "request": "latest", "thenjs": "^2.0.3", "node-xlsx": "^0.11.2", "node-ecdict": "^1.1.0", "multer": "^1.4.2"}, "devDependencies": {"@types/express": "^4.16.0", "babel-eslint": "^9.0.0", "chai": "^4.1.2", "eslint": "^5.6.0", "heapdump": "^0.3.12", "mocha": "^4.0.1", "superagent": "^3.6.3", "underscore": "^1.8.3"}, "scripts": {"start": "NODE_ENV=development NODE_PORT=8500 pm2 start app.js -i 2 --name kb_api", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["recommend"], "author": "mi<PERSON><PERSON>_<PERSON>_zhang", "license": "ISC"}