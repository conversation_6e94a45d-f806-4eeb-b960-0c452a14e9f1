const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const { ObjectId } = require('mongodb');
const db = mongodber.use('KB');

const { createResourceAlbumDataValidator, updateResourceAlbumDataValidator } = require('./schema/resource_album_data');


/**
 * 创建专辑资源数据
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const createResourceAlbumData = async (req, res) => {
    res.resMode = 'normal';
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    if (!createResourceAlbumDataValidator(body)) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }

    const dbs = (req.query.dbs || 'basic').split(',');
    const isKbp = dbs.includes('kbp');
    body.dbs = (body.dbs || dbs).filter(db => isKbp || dbs.includes(db));

    body.ctime = new Date();
    body.utime = new Date();
    body.valid = 1;
    try {
        res = await db.collection('resource_album_data').insertOne(body);
        return responseWrapper.succ({ id: res.insertedId.toString() });
    } catch (error) {
        return responseWrapper.error('HANDLE_ERROR', error);
    }
};

/**
 * 更新专辑资源数据
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const updateResourceAlbumData = async (req, res) => {
    res.resMode = 'normal';
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    if (!updateResourceAlbumDataValidator(body)) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }

    let { id, dbs = 'basic' } = req.query;
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }

    dbs = dbs.split(',');

    const query = {
        _id: new ObjectId(id),
        dbs: { $in: dbs },
    };

    if (!dbs.includes('kbp')) {
        delete body.dbs;
        delete body.valid;
        query.valid = 1;
    }

    try {
        let resourceAlbumData = await db.collection('resource_album_data').findOne(query, { fields: { _id: 1 } });
        if (!resourceAlbumData) {
            return responseWrapper.error('NULL_ERROR', '未找到资源');
        }
        body.utime = new Date();
        res = await db.collection('resource_album_data').updateOne(query, { $set: body });
        return responseWrapper.succ({ id });
    } catch (error) {
        return responseWrapper.error('HANDLE_ERROR', error);
    }
};

/**
 * 删除专辑资源数据
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const deleteResourceAlbumData = async (req, res) => {
    res.resMode = 'normal';
    let responseWrapper = new ResponseWrapper(res);
    let { id, dbs = 'basic' } = req.query;
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');
    const query = {
        _id: new ObjectId(id),
        dbs: { $in: dbs },
    };

    try {
        let resourceAlbumData = await db.collection('resource_album_data').findOne(query, { fields: { _id: 1, dbs: 1 } });
        if (!resourceAlbumData) {
            return responseWrapper.error('NULL_ERROR', '未找到资源');
        }
        resourceAlbumData.dbs = resourceAlbumData.dbs.filter(db => dbs.includes(db));
        resourceAlbumData.utime = new Date();
        res = await db.collection('resource_album_data').updateOne(query, { $set: resourceAlbumData });
        return responseWrapper.succ({ id });
    } catch (error) {
        return responseWrapper.error('HANDLE_ERROR', error);
    }
};


/**
 * 获取专辑数据详情
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const getResourceAlbumData = async (req, res) => {
    res.resMode = 'normal';
    let responseWrapper = new ResponseWrapper(res);
    let { id, dbs = 'basic', valid = 1 } = req.query;
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');
    const query = {
        _id: new ObjectId(id),
        dbs: { $in: dbs },
        valid: valid,
    };
    if (!dbs.includes('kbp')) {
        query.valid = 1;
    }

    try {
        let resourceAlbumData = await db.collection('resource_album_data').findOne(query);
        if (!resourceAlbumData) {
            return responseWrapper.error('NULL_ERROR', '未找到资源');
        }
        return responseWrapper.succ(resourceAlbumData);
    } catch (error) {
        return responseWrapper.error('HANDLE_ERROR', error);

    }
};


/**
 * 获取专辑数据列表
 * @param {*} req 
 * @param {*} res 
 * @returns {ResponseWrapper}
 */
const getResourceAlbumDataList = async (req, res) => {
    res.resMode = 'normal';
    let responseWrapper = new ResponseWrapper(res);
    let { dbs = 'basic', valid = 1, limit = 10, offset = 0 } = req.query;
    limit = +limit;
    offset = +offset;
    if (limit !== limit || offset !== offset) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }
    dbs = dbs.split(',');
    const query = {
        dbs: { $in: dbs },
        valid: valid,
    };
    if (!dbs.includes('kbp')) {
        query.valid = 1;
    }
    const sort = {
        ctime: -1,
    };

    try {
        let resourceAlbumDataList = await db.collection('resource_album_data').find(query).sort(sort).skip(offset).limit(limit).toArray();
        return responseWrapper.succ(resourceAlbumDataList);
    } catch (error) {
        return responseWrapper.error('HANDLE_ERROR', error);

    }
};



module.exports = {
    createResourceAlbumData,
    updateResourceAlbumData,
    deleteResourceAlbumData,
    getResourceAlbumData,
    getResourceAlbumDataList
};