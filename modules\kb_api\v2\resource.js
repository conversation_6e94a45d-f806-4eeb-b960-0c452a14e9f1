const ObjectID = require('mongodb').ObjectID;
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const assert = require('assert');
const rediser = require('../../common/utils/rediser');
let Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
let kb_api = require('../config.js').kb_api['v2'];
let params_utils = require('../../common/utils/params.js');
const _ = require('underscore');
const Thenjs = require('thenjs');
const config = require('config');
const counter = require('../../common/utils/counter');
const cacheWrapper = require('../../common/utils/cache_wrapper');
const lodash = require('lodash');
const mapExampaperName = require('./exampaper/mapExampaperName');
const filterExampaperName = require('./exampaper/filterExampaperName');
const inscreaceDownTimes = require('../../common/utils/download').inscreaceDownTimes;
const utils = require('../../common/utils/utils');
let DURATION = 120 * 60;

function find_resource(callback) {
    let key = 'kb_api:v2:resource:profile:' + process.env.NODE_PORT;

    rediser.get(key, function (err, obj) {
        if (err) {
            Logger.error(err);
        }
        if (null !== obj) {
            return callback(null, obj);
        }
        let collection = db.collection('resource_profile');
        collection.findOne({}, function (err, obj) {
            if (err) {
                Logger.error(err);
                return callback(err, null);
            }
            if (!obj) {
                return callback(null, null);
            }
            rediser.set(key, obj, DURATION);
            return callback(null, obj);
        });
    });
}

function getDayIndex() {
    let now = new Date();
    let firstDay = new Date(now.getFullYear(), 0, 1);
    let dateDiff = now - firstDay;
    let dayIndex = Math.floor(dateDiff / (1000 * 60 * 60 * 24));
    return dayIndex;
}

function filt(obj, cond) {
    let key = cond['key'];
    let value = cond['value'];
    for (let i in obj) {
        if (obj[i][key] == value) {
            return [obj[i]];
        }
    }
    return [];
}

function resource_profile(resource, callback) {
    find_resource(function (err, obj) {
        try {
            let resources = obj.resources;
            if ('all' != resource) {
                let cond = { key: 'resource', value: resource };
                resources = filt(resources, cond);
            }
            return callback(null, resources);
        } catch (err) {
            return callback(err, null);
        }
    });
}

function profile_single(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let resource = req.query.resource;
    let period = req.query.period;
    resource_profile(resource, function (err, obj) {
        if (err) {
            Logger.error(err);
            return responseWrapper.error('HANDLE_ERROR');
        }
        if (!obj) {
            return responseWrapper.error('NULL_ERROR');
        }
        try {
            let periods = obj[0].periods;
            if (period) {
                let cond = { key: 'period', value: period };
                periods = filt(periods, cond);
            }
            return responseWrapper.succ(periods);
        } catch (err) {
            return responseWrapper.error('HANDLE_ERROR');
        }
    });
}

function convertFormatResources2Periods(arrObj) {
    try {
        let resMap = new Map();
        //resource
        for (let i = 0; i < arrObj.length; i++) {
            let resource = arrObj[i].resource;
            let periodsArr = arrObj[i].periods;

            //periods
            for (let j = 0; j < periodsArr.length; j++) {
                let periodName = periodsArr[j].period;
                if (!resMap.has(periodName)) {
                    let periodObj = {
                        periods: periodName,
                        subjects: new Map()
                    };
                    resMap.set(periodName, periodObj);
                }
                let subjectsArr = periodsArr[j].subjects;
                let currPeriod = resMap.get(periodName);
                let currSubject = currPeriod.subjects;

                //subjects
                for (let z = 0; z < subjectsArr.length; z++) {
                    if (!currSubject.has(subjectsArr[z].subject)) {
                        let tmpObj = {
                            subject: subjectsArr[z].subject
                        };
                        tmpObj[resource] = subjectsArr[z].num;
                        currSubject.set(subjectsArr[z].subject, tmpObj);
                    } else {
                        let tmpObj2 = currSubject.get(subjectsArr[z].subject);
                        tmpObj2[resource] = subjectsArr[z].num;
                        currSubject.set(subjectsArr[z].subject, tmpObj2);
                    }
                }
            }
        }

        let resObj = [];
        for (let val of resMap.values()) {
            let obj = {};
            obj.period = val.periods;
            obj.subjects = [];
            for (let sub of val.subjects.values()) {
                //屏蔽初中-科学信息
                // if (obj.period === '初中' && sub.subject === '科学')
                //     continue;
                let subObj = {
                    subject: sub.subject,
                    resource: {}
                };
                subObj.resource.book = sub.book;
                subObj.resource.exampaper = sub.exampaper;
                subObj.resource.knowledge = sub.knowledge;
                subObj.resource.question = sub.question;
                subObj.resource.video = sub.video;
                obj.subjects.push(subObj);
            }
            resObj.push(obj);
        }

        return resObj;
    } catch (error) {
        return [];
    }
}

function profile(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let resource = req.query.resource || 'all';
    resource_profile(resource, function (err, obj) {
        if (err) {
            Logger.error(err);
            return responseWrapper.error('HANDLE_ERROR');
        }
        if (!obj) {
            return responseWrapper.error('NULL_ERROR');
        }

        return responseWrapper.succ(obj);
    });
}

function profileNew(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let resource = req.query.resource || 'all';
    resource_profile(resource, function (err, obj) {
        if (err) {
            Logger.error(err);
            return responseWrapper.error('HANDLE_ERROR');
        }
        if (!obj) {
            return responseWrapper.error('NULL_ERROR');
        }

        //转换数据格式
        obj = convertFormatResources2Periods(obj);

        return responseWrapper.succ(obj);
    });
}

function resource_filters(resource, dbs, callback) {
    if (!dbs) {
        dbs = 'basic';
    }
    let key = `kb_api:v2:${resource}:filters:${dbs}` + process.env.NODE_PORT;
    rediser.get(key, function (err, data) {
        if (err) {
            Logger.error(err);
        }
        if (data) {
            return callback(null, data);
        }
        let collection = db.collection('resource_filter');
        collection.findOne({ resource, dbs: dbs }, function (err, data) {
            if (err) {
                return callback(err, null);
            }
            if (!data) {
                return callback(null, null);
            }
            data = data.filter;
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth();
            if (month >= 8) {
                year = year + 1;
            }
            let years = [];
            for (var i = 0; i < 10; ++i) {
                years.push(year - i);
            }
            for (var i in data) {
                data[i]['year'] = years;
            }
            rediser.set(key, data, DURATION);
            callback(null, data);
        });
    });
}

function filters(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let resource = req.query.resource;
    let period = req.query.period;

    let dbs = req.query.dbs;
    if (!dbs) {
        dbs = 'basic';
    } else if (dbs instanceof Array) {
        dbs = dbs.length > 1 ? dbs[0] : 'basic';
    } else if (dbs instanceof String) {
        dbs = dbs.split(',')[0];
    }

    resource_filters(resource, dbs, function (err, data) {
        if (err) {
            Logger.error(err);
            return responseWrapper.error('HANDLE_ERROR');
        }
        if (!data) {
            return responseWrapper.error('NULL_ERROR');
        }
        if (period) {
            data = data.filter(function (item) {
                return item.period === period;
            });
        }
        try {
            if (config.get('kb_plat.appkey').indexOf(req.query.api_key) < 0) {
                let subjects = config.get('kb_plat.unique_subjects');
                for (let ix in data) {
                    let pd = data[ix];
                    pd.subjects = _.filter(pd.subjects, function (x) {
                        return subjects.indexOf(x.subject) < 0;
                    });
                }
            }
        } catch (err) {
        }
        return responseWrapper.succ(data);
    });
}

async function kb_filters(resource, dbs) {
    let redisKey = resource;
    if (resource === 'question') { // 防止重名，所以另起名key
        redisKey = 'question_kb_f';
    }
    if (!dbs) {
        dbs = 'basic';
    }
    let key = `kb_api:v2:${redisKey}:filters:${dbs}` + process.env.NODE_PORT;
    try { // redis缓存获取
        let data = await rediser.get(key);
        if (data) {
            return data;
        }
    } catch (err) {
        //Logger.error(err);
    }
    try { // mongodb 获取
        let data = await db.collection('kb_filter').findOne({ resource, dbs: dbs });
        if (data && data.filter) {
            data = data.filter;
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth();
            if (month >= 8) {
                year = year + 1;
            }
            let years = [];
            for (let i = 0; i < 10; ++i) {
                years.push(year - i);
            }
            for (let item of data) {
                item['year'] = years;
                if (Array.isArray(item.subjects)) {
                    for (let subject of item.subjects) {
                        if (Array.isArray(subject.types)) {
                            for (let type of subject.types) {
                                if (Array.isArray(type.tag_ids) && type.tag_ids.length > 0) {
                                    let tagIds = type.tag_ids.map(function (id) {
                                        return ObjectID(id);
                                    });
                                    let cond = { _id: { $in: tagIds } };
                                    let project = { name: 1, tag_type: 1, values: 1 };
                                    let tagArr = await db.collection('kb_tag').find(cond).project(project).toArray();
                                    for (let tag of tagArr) {
                                        tag.id = tag._id;
                                        delete tag._id;
                                    }
                                    type.tag_ids = tagArr;
                                }
                            }
                        }
                    }
                }
            }
            rediser.set(key, data, 60);
            return data;
        } else {
            return null;
        }
    } catch (err) {
        Logger.error(err);
        throw err;
    }
}

async function filters2(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let resource = req.query.resource;
    let period = req.query.period;
    let dbs = req.query.dbs;
    if (!dbs) {
        dbs = 'basic';
    } else if (dbs instanceof Array) {
        dbs = dbs.length > 1 ? dbs[0] : 'basic';
    } else if (dbs instanceof String) {
        dbs = dbs.split(',')[0];
    }

    try {
        let data = await kb_filters(resource, dbs);
        if (!data) {
            return responseWrapper.error('NULL_ERROR');
        }
        if (period) {
            data = data.filter(function (item) {
                return item.period === period;
            });
        }
        try {
            if (config.get('kb_plat.appkey').indexOf(req.query.api_key) < 0) {
                let subjects = config.get('kb_plat.unique_subjects');
                for (let ix in data) {
                    let pd = data[ix];
                    pd.subjects = _.filter(pd.subjects, function (x) {
                        return subjects.indexOf(x.subject) < 0;
                    });
                }
            }
        } catch (err) {
            //
        }
        return responseWrapper.succ(data);
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err);
    }
}

function _findUpdateResource(callback) {

    Thenjs(function (cont) {
        //前一天的时间
        let endDate = new Date(new Date().setHours(32, 0, 0, 0));
        let startDate = new Date(new Date().setHours(8, 0, 0, 0));

        let cond = { ctime: { $gte: startDate, $lt: endDate }, g_paper_id: { '$exists': 0 } };
        db.collection('exampaper').find(cond).project({
            _id: 1,
            name: 1,
            subject: 1,
            period: 1,
            ctime: 1,
            to_year: 1
        }).limit(9999).toArray(function (err, todayExampapers) {
            if (err) {
                return callback(err, null);
            }
            if (!todayExampapers) {
                return callback('NULL_ERROR', null);
            }
            return cont(null, todayExampapers);
        });
    }).then(function (cont, todayExampapers) {
        if (todayExampapers.length > 0) {
            return cont(null, todayExampapers);
        }
        db.collection('exampaper').find({ g_paper_id: { '$exists': 0 } }).project({
            _id: 1,
            name: 1,
            subject: 1,
            period: 1,
            ctime: 1,
            to_year: 1
        }).limit(500).sort({ ctime: -1 }).toArray(function (err, exampapers) {
            if (err) {
                return callback(err, null);
            }
            try {
                if (!exampapers) {
                    return callback('NULL_ERROR', null);
                }
                let dayIndex = getDayIndex();
                let reminder = dayIndex % 5;
                let index = 0;
                exampapers = _.filter(exampapers, function (exampaper) {
                    ++index;
                    return (index % 5 === reminder);
                });
                return cont(null, exampapers);
            } catch (err) {
                return callback('HANDLE_ERROR', null);
            }
        });


    }).then(function (cont, exampapers) {
        db.collection('daily_count').find().limit(1).toArray(function (err, years) {
            try {

                if (err) {
                    return callback(err, null);
                }
                if (!years) {
                    return callback('NULL_ERROR', null);
                }
                let retObj = {};
                let now = new Date();
                let dayIndex = getDayIndex();
                let findYearInfo = _.find(years[0].years, function (yearInfo) {
                    return yearInfo.year === now.getFullYear();
                });
                let questionNum = 0;
                let exampaperNum = 0;
                let totalQuestionNum, totalExampaperNum;
                if (findYearInfo) {
                    let dayInfo = findYearInfo.detail[dayIndex];
                    if (!dayInfo.ques_inc) {
                        for (let findIndex = dayIndex; findIndex > 0; --findIndex) {
                            dayInfo = findYearInfo.detail[findIndex];
                            if (dayInfo.questionNum) {
                                break;
                            }
                        }
                    }
                    questionNum = dayInfo.ques_inc;
                    exampaperNum = dayInfo.exam_inc;
                    totalQuestionNum = dayInfo.ques_base;
                    totalExampaperNum = dayInfo.exam_base;
                } else {
                    questionNum = 2000 + dayIndex;
                    exampaperNum = exampapers.length;
                    totalQuestionNum = 450000 + dayIndex;
                    totalExampaperNum = 530000 + dayIndex;
                }

                _.each(exampapers, function (exampaper) {
                    exampaper.id = exampaper._id;
                    delete exampaper._id;
                });
                exampapers.sort(function (x, y) {
                    return ((x.to_year - y.to_year) === 0) ? (y.ctime - x.ctime) : (y.to_year - x.to_year);
                });
                retObj['exampaper'] = { 'num': exampaperNum, 'totalNum': totalExampaperNum, 'exampapers': exampapers };
                retObj['question'] = { 'num': questionNum, 'totalNum': totalQuestionNum };
                return callback(null, retObj);
            } catch (e) {
                return callback('HANDLE_ERROR', null);
            }
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return callback(error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return callback(error);
    });
}

function _getExampapersByPeriodSubject(queryPeriod, querySubject, queryLimit, queryOffset, callback) {
    let cond = {};
    cond.g_paper_id = { '$exists': 0 };

    if (queryPeriod) {
        cond.period = queryPeriod;
    }
    if (querySubject) {
        cond.subject = querySubject;
    }
    if (!queryLimit) {
        queryLimit = 20;
    }
    if (!queryOffset) {
        queryOffset = 0;
    }

    let proj = { _id: 1, name: 1, subject: 1, period: 1, ctime: 1, to_year: 1 };

    db.collection('exampaper').find(cond).project(proj).limit(500).skip(0).sort({ ctime: -1 }).toArray(function (err, exampapers) {
        if (err) {
            return callback(err, null);
        }
        try {
            if (!exampapers) {
                return callback('NULL_ERROR', null);
            }
            return callback(null, exampapers);
        } catch (err) {
            return callback('HANDLE_ERROR', null);
        }
    });
}

function _findUpdateResourceNew(queryPeriod, querySubject, callback) {
    Thenjs(function (cont) {
        let cond = {};
        cond.g_paper_id = { '$exists': 0 };

        if (querySubject) {
            cond.subject = querySubject;
        }
        if (queryPeriod) {
            cond.period = queryPeriod;
        }

        db.collection('exampaper').find(cond).project({
            _id: 1,
            name: 1,
            subject: 1,
            period: 1,
            ctime: 1,
            to_year: 1
        }).limit(2000).sort({ ctime: -1 }).toArray(function (err, exampapers) {
            if (err) {
                return callback(err, null);
            }
            try {
                if (!exampapers) {
                    return callback('NULL_ERROR', null);
                }
                return cont(null, exampapers);
            } catch (err) {
                return callback('HANDLE_ERROR', null);
            }
        });
    }).then(function (cont, exampapers) {
        db.collection('daily_count').find().limit(1).toArray(function (err, years) {
            try {
                if (err) {
                    return callback(err, null);
                }
                if (!years) {
                    return callback('NULL_ERROR', null);
                }
                let retObj = {};
                let now = new Date();
                let dayIndex = getDayIndex();
                let findYearInfo = _.find(years[0].years, function (yearInfo) {
                    return yearInfo.year === now.getFullYear();
                });
                let questionNum = 0;
                let exampaperNum = 0;
                let totalQuestionNum, totalExampaperNum;
                if (findYearInfo) {
                    let dayInfo = findYearInfo.detail[dayIndex];
                    if (!dayInfo.ques_inc) {
                        for (let findIndex = dayIndex; findIndex > 0; --findIndex) {
                            dayInfo = findYearInfo.detail[findIndex];
                            if (dayInfo.questionNum) {
                                break;
                            }
                        }
                    }
                    questionNum = dayInfo.ques_inc;
                    exampaperNum = dayInfo.exam_inc;
                    totalQuestionNum = dayInfo.ques_base;
                    totalExampaperNum = dayInfo.exam_base;
                } else {
                    questionNum = 2000 + dayIndex;
                    exampaperNum = exampapers.length;
                    totalQuestionNum = 450000 + dayIndex;
                    totalExampaperNum = 530000 + dayIndex;
                }

                _.each(exampapers, function (exampaper) {
                    exampaper.id = exampaper._id;
                    delete exampaper._id;
                });
                //随机获取试卷
                // let dayIndex = getDayIndex();
                let reminder = dayIndex % 5;
                let index = 0;
                exampapers = _.filter(exampapers, function (exampaper) {
                    ++index;
                    return (index % 5 === reminder);
                });
                exampapers.sort(function (x, y) {
                    return ((x.to_year - y.to_year) === 0) ? (y.ctime - x.ctime) : (y.to_year - x.to_year);
                });
                retObj['exampaper'] = { 'num': exampaperNum, 'totalNum': totalExampaperNum, 'exampapers': exampapers };
                retObj['question'] = { 'num': questionNum, 'totalNum': totalQuestionNum };
                return callback(null, retObj);
            } catch (e) {
                return callback('HANDLE_ERROR', null);
            }
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return callback(error);
    }).finally(function (cont, error) {
        Logger.error(error);
        return callback(error);
    });
}

function _genUpdateKey() {
    let date = new Date();
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    return `kb_api:v2:update:${year}-${month}-${day}:${process.env.NODE_PORT}`;
}

function getExampaperUpdateSubjects(period, items) {
    let retArray = [];
    _.each(items.exampaper.exampapers, function (exampaper) {
        if (exampaper.period === period && retArray.indexOf(exampaper.subject) < 0) {
            retArray.push(exampaper.subject);
        }
    });
    return retArray;
}

function genUpdateData(params, items) {
    if (params.fields_type === 'question') {
        delete items.exampaper;

    } else if (params.fields_type === 'exampaper') {
        delete items.question;
        if (params.period || params.subject) {
            items.exampaper.exampapers = _.filter(items.exampaper.exampapers,
                function (exampaper) {
                    if (params.period) {
                        if (exampaper.period !== params.period) {
                            return false;
                        }
                    }
                    if (params.subject) {
                        if (exampaper.subject !== params.subject) {
                            return false;
                        }
                    }
                    return true;
                });
            // items.exampaper.num = items.exampaper.exampapers.length;
        }

        items.exampaper.exampapers = items.exampaper.exampapers.slice(params.offset, params.offset + params.limit);
    } else {
        items.exampaper.exampapers = items.exampaper.exampapers.slice(0, 20);
    }
    return items;
}

function update(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    try {
        //根据学段和科目查询
        params.subject = params.subject || '';
        if (params.fields_type === 'exampaper' && params.subject.length === 0) {
            _getExampapersByPeriodSubject(params.period, params.subject, params.limit, params.offset, function (err, exampapers) {
                if (err) {
                    return responseWrapper.error('HANDLE_ERROR');
                }
                try {
                    _.each(exampapers, function (exampaper) {
                        exampaper.id = exampaper._id;
                        delete exampaper._id;
                    });
                    //随机获取试卷
                    let dayIndex = getDayIndex();
                    let reminder = dayIndex % 5;
                    let index = 0;
                    exampapers = _.filter(exampapers, function (exampaper) {
                        ++index;
                        return (index % 5 === reminder);
                    });
                    exampapers = exampapers.slice(params.offset, params.offset + params.limit);

                    exampapers.sort(function (x, y) {
                        return ((x.to_year - y.to_year) === 0) ? (y.ctime - x.ctime) : (y.to_year - x.to_year);
                    });

                    let resObj = {};
                    resObj.exampaper = {};
                    resObj.exampaper.num = 0;
                    resObj.exampaper.totalNum = 0;
                    resObj.exampaper.exampapers = exampapers;
                    return responseWrapper.succ(resObj);
                } catch (error) {
                    return responseWrapper.error('HANDLE_ERROR');
                }
            });
        }
        else {
            let cacheKey = _genUpdateKey();
            cacheKey += params.period ? ':' + params.period : '';
            cacheKey += params.subject ? ':' + params.subject : '';
            rediser.get(cacheKey, function (err, items) {
                try {
                    if (!items) {
                        _findUpdateResourceNew(params.period, params.subject, function (err, retObj) {
                            if (err) {
                                return responseWrapper.error('HANDLE_ERROR');
                            }
                            try {
                                rediser.set(cacheKey, retObj, 60 * 60);
                                retObj = genUpdateData(params, retObj);
                                return responseWrapper.succ(retObj);
                            } catch (e) {
                                return responseWrapper.error('HANDLE_ERROR');
                            }
                        });
                    } else {
                        items = genUpdateData(params, items);
                        return responseWrapper.succ(items);
                    }
                } catch (e) {
                    Logger.error(e);
                    return responseWrapper.error('HANDLE_ERROR');
                }
            });
        }
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
}

function exampaperUpdateSubject(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    try {
        _findUpdateResourceNew(params.period, null, function (err, retObj) {
            try {
                if (err) {
                    Logger.error(err);
                    return responseWrapper.error('HANDLE_ERROR');
                }
                retObj = getExampaperUpdateSubjects(params.period, retObj);
                return responseWrapper.succ(retObj);
            } catch (err) {
                Logger.error(err);
                return responseWrapper.error('HANDLE_ERROR');
            }
        });
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
}

// 获取某项资源 的seq id
function getSeqIds(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        var num = parseInt(req.query.num);
        var resource = req.query.resource;
        assert(num > 0);
        assert(resource);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    // 数据查询 ----------------------------
    Thenjs(function (cont) {
        counter.getNextSequenceValue(resource, num, function (err, seq) {
            if (err) {
                return cont(err, null);
            }
            return cont(null, seq);
        });
    }).then(function (cont, seq) {
        return responseWrapper.succ(seq);
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

// 统计视频资源
async function getVideoStatistics(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        // 总数量统计
        let videoNum = await db.collection('video').count();
        let videoCategoryNum = await db.collection('video_category').count();
        // 学段数量统计
        let periodGroupPipeline = [
            { $group: { _id: '$period', period: { $first: '$period' }, num: { $sum: 1 } } },
            { $project: { _id: 0, period: 1, num: 1 } },
            { $match: { period: { $exists: 1, $ne: null } } },
        ];
        let videoPeriods = await db.collection('video').aggregate(periodGroupPipeline).toArray();
        let videoCategoryPeriods = await db.collection('video_category').aggregate(periodGroupPipeline).toArray();
        responseWrapper.succ({
            videoNum,
            videoCategoryNum,
            videoPeriods,
            videoCategoryPeriods,
        });
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
}

// 获取 试卷&试题 今日更新
async function getExamUpdateStatistics(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        // 前一天的时间
        let endDate = new Date(new Date().setHours(32, 0, 0, 0));
        let startDate = new Date(new Date().setHours(8, 0, 0, 0));
        let exampaperUpdate = await db.collection('exampaper').aggregate([
            { $match: { ctime: { $gte: startDate, $lt: endDate }, g_paper_id: { '$exists': 0 } } },
            { $project: { _id: 1, period: 1, subject: 1 } },
            {
                $group: {
                    _id: '$period',
                    period: { $first: '$period' },
                    num: { $sum: 1 },
                    subjects: { $push: '$subject' },
                }
            },
        ]).toArray();
        let questionUpdate = await db.collection('question').aggregate([
            { $match: { ctime: { $gte: startDate, $lt: endDate } } },
            { $project: { _id: 1, period: 1, subject: 1 } },
            {
                $group: {
                    _id: '$period',
                    period: { $first: '$period' },
                    num: { $sum: 1 },
                    subjects: { $push: '$subject' },
                }
            },
        ]).toArray();
        const groupSubjects = sourceSubjects => {
            let subjects = [];
            sourceSubjects.forEach(subjectName => {
                let subject = _.find(subjects, e => e.name === subjectName);
                if (subject) {
                    subject.num++;
                } else {
                    subjects.push({ name: subjectName, num: 1 });
                }
            });
            return subjects;
        };
        responseWrapper.succ({
            exampaperNum: exampaperUpdate.reduce((value, b) => value + b.num, 0),
            questionNum: questionUpdate.reduce((value, b) => value + b.num, 0),
            exampaperPeriods: exampaperUpdate.map(exampaper => ({
                period: exampaper.period,
                num: exampaper.num,
                subjects: groupSubjects(exampaper.subjects),
            })),
            questionPeriods: questionUpdate.map(question => ({
                period: question.period,
                num: question.num,
                subjects: groupSubjects(question.subjects),
            })),
        });
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
}

const _getDailyCountData = (years, newExampapers, hotExampapers) => {
    let retObj = {};
    let now = new Date();
    let dayIndex = getDayIndex();
    let findYearInfo = _.find(years[0].years, function (yearInfo) {
        return yearInfo.year === now.getFullYear();
    });

    let questionNum = 0;
    let exampaperNum = 0;
    let totalQuestionNum = 0;
    let totalExampaperNum = 0;
    if (findYearInfo) {
        let dayInfo = findYearInfo.detail[dayIndex];
        if (!dayInfo.ques_inc) {
            for (let findIndex = dayIndex; findIndex > 0; --findIndex) {
                dayInfo = findYearInfo.detail[findIndex];
                if (dayInfo.questionNum) {
                    break;
                }
            }
        }
        questionNum = dayInfo.ques_inc;
        exampaperNum = dayInfo.exam_inc;
        totalQuestionNum = dayInfo.ques_base;
        totalExampaperNum = dayInfo.exam_base;
    }

    retObj.exampaper = {};
    retObj.exampaper.num = exampaperNum;
    retObj.exampaper.total_num = totalExampaperNum;
    retObj.exampaper.new = newExampapers;
    retObj.exampaper.hot = hotExampapers;
    retObj.question = {};
    retObj.question.num = questionNum;
    retObj.question.totalNum = totalQuestionNum;

    return retObj;
};

function _pick_by_cond(source_list, pick_cond) {
    pick_cond = [
        { 'to_year': 2024, 'type': '高考' },
        { 'to_year': 2024, 'type': '期末' },
        { 'to_year': 2023, 'type': '期末' },
        { 'to_year': 2024, 'type': '期中' },
        { 'to_year': 2024, 'type': '期末' },
    ];



}

function getNewHotExampaper(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;

    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    let cond = {}, cond2 = {};
    cond.to_year = { '$gt': new Date().getFullYear() - 2 }
    cond2.to_year = { '$gt': new Date().getFullYear() - 2 }
    if (params.filter_mkp) {
        cond.from = { $nin: ['mkp'] };
        cond2.from = { $nin: ['mkp'] };
    }
    if (params.subject) {
        cond.subject = params.subject;
        cond2.subject = params.subject
    }
    if (params.period) {
        cond.period = params.period;
        cond2.period = params.period;
        if (['初中', '高中'].indexOf(params.period) >= 0) {
            cond2.grade = params.period == '高中' ? '高考专题' : '中考专题';
            cond2.type = params.period == '高中' ? '高考真卷' : '中考真卷';
        }
    }
    if (params.grade) {
        grade = params.grade;
        grade_list = [_getCurrSemester(grade)];
        if (['高三', '九年级'].indexOf(grade) >= 0) {
            grade_list.push(grade == '高三' ? '高考专题' : '中考专题');
        }
        cond.grade = { '$in': grade_list };
    }
    let new_mongo_cond = { '$or': [cond, cond2] };

    let cacheKey = _genUpdateKey();

    Thenjs.parallel([(function (cont) {
        //查找最新试卷
        let newKey = cacheKey + (params.period ? ':' + params.period : '');
        newKey += params.subject ? ':' + params.subject : '';
        newKey += params.grade ? ':' + params.grade : '';
        newKey += ':new';
        rediser.get(newKey, function (err, items) {
            try {
                if (!items) {
                    //const newCond = lodash.assign({},cond);
                    ////newCond._id = {'$in':[2253741252, 554065604, 554131140, 2304597188, 2338020548, 2278513860, 2252627140]}
                    //if (params.grade) {
                    //    newCond.grade = _getCurrSemester(params.grade);
                    //}
                    //cond2 = {period:'
                    //no_cursor_timeout:防止游标超时，导致服务异常
                    let proj = { _id: 1, name: 1, type: 1, vague_name: 1, ctime: 1, view_times: 1, download_times: 1, to_year: 1, from: 1, use_type: 1, province: 1, city: 1, grade: 1, subject: 1, type: 1, from_year: 1, user_name: 1 };
                    db.collection('exampaper').find(new_mongo_cond, { no_cursor_timeout: true }).project(proj).sort({ to_year: -1, ctime: -1 }).limit(200).toArray(function (err, newExampapers) {
                        if (err) {
                            return cont(err, null);
                        }
                        try {
                            if (!newExampapers) {
                                return cont('NULL_ERROR', null);
                            }
                            sorted_grade_type = {
                                '七年级': {
                                    2: ['中考真卷', '期末试卷', '同步练习'],
                                    3: ['中考真卷', '同步练习', '单元测试', '月考试卷'],
                                    4: ['中考真卷', '月考试卷', '期中试卷', '同步练习'],
                                    5: ['中考真卷', '期中试卷', '同步练习', '月考试卷'],
                                    6: ['中考真卷', '期末试卷'],
                                    7: ['中考真卷', '期末试卷'],
                                    8: ['中考真卷', '开学考试', '同步练习', '单元测试', '期末试卷'],
                                    9: ['中考真卷', '开学考试', '同步练习', '单元测试', '月考试卷'],
                                    10: ['中考真卷', '期中试卷', '同步练习', '单元测试', '月考试卷'],
                                    11: ['中考真卷', '期中试卷', '同步练习', '单元测试'],
                                    12: ['中考真卷', '期中试卷', '同步练习', '单元测试', '月考试卷'],
                                    1: ['中考真卷', '月考试卷', '期末试卷'],
                                },
                                '八年级': {
                                    2: ['中考真卷', '期末试卷', '同步练习'],
                                    3: ['中考真卷', '同步练习', '单元测试', '月考试卷'],
                                    4: ['中考真卷', '月考试卷', '期中试卷', '同步练习'],
                                    5: ['中考真卷', '期中试卷', '同步练习', '月考试卷'],
                                    6: ['中考真卷', '期末试卷'],
                                    7: ['中考真卷', '期末试卷'],
                                    8: ['中考真卷', '开学考试', '同步练习', '单元测试', '期末试卷'],
                                    9: ['中考真卷', '开学考试', '同步练习', '单元测试', '月考试卷'],
                                    10: ['中考真卷', '期中试卷', '同步练习', '单元测试', '月考试卷'],
                                    11: ['中考真卷', '期中试卷', '同步练习', '单元测试'],
                                    12: ['中考真卷', '期中试卷', '同步练习', '单元测试', '月考试卷'],
                                    1: ['中考真卷', '月考试卷', '期末试卷'],
                                },
                                '九年级': {
                                    2: ['中考模拟', '中考真卷', '中考复习', '期末试卷', '期中试卷'],
                                    3: ['中考模拟', '中考真卷', '中考复习', '期末试卷', '期中试卷'],
                                    4: ['中考模拟', '中考真卷', '中考复习', '期末试卷', '期中试卷'],
                                    5: ['中考模拟', '高考真卷', '高考复习', '期末试卷', '期中试卷'],
                                    6: ['中考真卷', '中考复习', '中考模拟', '期末试卷'],
                                    7: ['中考真卷', '中考复习', '中考模拟', '期末试卷'],
                                    8: ['中考真卷', '开学考试', '中考复习', '中考模拟', '期末试卷'],
                                    9: ['中考真卷', '开学考试', '中考复习', '中考模拟', '期末试卷'],
                                    10: ['中考真卷', '期中试卷', '中考复习', '中考模拟', '期末试卷'],
                                    11: ['中考真卷', '中考复习', '中考模拟', '期末试卷'],
                                    12: ['中考真卷', '中考复习', '中考模拟', '期末试卷'],
                                    1: ['中考真卷', '中考复习', '中考模拟', '期末试卷'],
                                },
                                '高一': {
                                    2: ['高考真卷', '期末试卷', '同步练习'],
                                    3: ['高考真卷', '同步练习', '单元测试', '月考试卷'],
                                    4: ['高考真卷', '月考试卷', '期中试卷', '同步练习'],
                                    5: ['高考真卷', '期中试卷', '同步练习', '月考试卷'],
                                    6: ['高考真卷', '期末试卷'],
                                    7: ['高考真卷', '期末试卷'],
                                    8: ['高考真卷', '开学考试', '同步练习', '单元测试', '期末试卷'],
                                    9: ['高考真卷', '开学考试', '同步练习', '单元测试', '月考试卷'],
                                    10: ['高考真卷', '期中试卷', '同步练习', '单元测试', '月考试卷'],
                                    11: ['高考真卷', '期中试卷', '同步练习', '单元测试'],
                                    12: ['高考真卷', '期中试卷', '同步练习', '单元测试', '月考试卷'],
                                    1: ['高考真卷', '月考试卷', '期末试卷'],
                                },
                                '高二': {
                                    2: ['高考真卷', '期末试卷', '同步练习'],
                                    3: ['高考真卷', '同步练习', '单元测试', '月考试卷'],
                                    4: ['高考真卷', '月考试卷', '期中试卷', '同步练习'],
                                    5: ['高考真卷', '期中试卷', '同步练习', '月考试卷'],
                                    6: ['高考真卷', '期末试卷'],
                                    7: ['高考真卷', '期末试卷'],
                                    8: ['高考真卷', '开学考试', '同步练习', '单元测试', '期末试卷'],
                                    9: ['高考真卷', '开学考试', '同步练习', '单元测试', '月考试卷'],
                                    10: ['高考真卷', '期中试卷', '同步练习', '单元测试', '月考试卷'],
                                    11: ['高考真卷', '期中试卷', '同步练习', '单元测试'],
                                    12: ['高考真卷', '期中试卷', '同步练习', '单元测试', '月考试卷'],
                                    1: ['高考真卷', '月考试卷', '期末试卷'],
                                },
                                '高三': {
                                    2: ['高考模拟', '高考真卷', '高考复习', '期末试卷', '期中试卷'],
                                    3: ['高考模拟', '高考真卷', '高考复习', '期末试卷', '期中试卷'],
                                    4: ['高考模拟', '高考真卷', '高考复习', '期末试卷', '期中试卷'],
                                    5: ['高考模拟', '高考真卷', '高考复习', '期末试卷', '期中试卷'],
                                    6: ['高考真卷', '高考复习', '高考模拟', '期末试卷'],
                                    7: ['高考真卷', '高考复习', '高考模拟', '期末试卷'],
                                    8: ['高考真卷', '开学考试', '高考复习', '高考模拟', '期末试卷'],
                                    9: ['高考真卷', '开学考试', '高考复习', '高考模拟', '期末试卷'],
                                    10: ['高考真卷', '期中试卷', '高考复习', '高考模拟', '期末试卷'],
                                    11: ['高考真卷', '高考复习', '高考模拟', '期末试卷'],
                                    12: ['高考真卷', '高考复习', '高考模拟', '期末试卷'],
                                    1: ['高考真卷', '高考复习', '高考模拟', '期末试卷'],
                                },
                            }
                            curr_month = new Date().getMonth() + 1
                            // 有年级根据年级节奏，排序试卷
                            // 没有年级参数，根据学段，选个默认
                            curr_grade = ''
                            if (params.grade) {
                                curr_grade = params.grade
                            } else {
                                curr_grade = params.period == '高中' ? '高二' : '初二'
                            }

                            newExampapers.sort(function (x, y) {
                                x_type_no = y_type_no = 999
                                if (sorted_grade_type[curr_grade]) {
                                    x_type_no = sorted_grade_type[curr_grade][curr_month].indexOf(x.type)
                                    x_type_no = x_type_no >= 0 ? x_type_no : 999
                                    y_type_no = sorted_grade_type[curr_grade][curr_month].indexOf(y.type)
                                    y_type_no = y_type_no >= 0 ? y_type_no : 999
                                }

                                if (x_type_no == y_type_no) {
                                    return y.to_year - x.to_year;
                                }
                                return x_type_no > y_type_no ? 1 : -1;
                            });
                            //存取当年试卷数据
                            let currYearExampapers = [];
                            let otherYearExampapers = [];
                            let teacher_names = ['刘建雪', '陈晓慧', '隋晓妍', '莉莉', '孙敏', '花瑞琴', '李光英', '杨乃仟', '刘帅']
                            for (let i = 0; i < newExampapers.length; i++) {
                                let exampaper = newExampapers[i];
                                if (exampaper.from && exampaper.from == 'mkp' && !exampaper.vague_name) {
                                    let province = exampaper.province || '';
                                    let city = exampaper.city || '';
                                    let grade = exampaper.grade || '';
                                    let subject = exampaper.subject || '';
                                    let from_year = exampaper.from_year || '';
                                    let to_year = exampaper.to_year || '';
                                    let exam_type = exampaper.type || '';
                                    exampaper.vague_name = from_year + '-' + to_year + province + city + '某校' + grade + subject + exam_type
                                }
                                if (exampaper.vague_name) {
                                    exampaper.vague_name = exampaper.vague_name.replace('学科网', '')
                                }
                                // 过滤安阳五中
                                let paper_from = exampaper.from || '';
                                let paper_city = exampaper.city || '';
                                let user_name = exampaper.user_name || '';
                                let paper_name = exampaper.name || ''
                                delete exampaper.user_name;
                                if (paper_from == 'drm' && (paper_city == '安阳' || teacher_names.indexOf(user_name) != -1 || paper_name.match('测试|日清|作业|午练|课时|复习'))) {
                                    continue
                                }
                                // exampaper.vague_name = filterExampaperName(exampaper);
                                exampaper.name = mapExampaperName(exampaper, req.query);
                                exampaper.id = exampaper._id;
                                exampaper.ctime = utils.format_date(exampaper.ctime || new Date());
                                delete exampaper._id;
                                delete exampaper.from;
                                delete exampaper.use_type;
                                delete exampaper.province;
                                delete exampaper.grade;
                                delete exampaper.subject;
                                // delete exampaper.type;
                                delete exampaper.from_year;
                                delete exampaper.city;
                                delete exampaper.to_year;
                                currYearExampapers.push(exampaper);
                            }

                            currYearExampapers = currYearExampapers.slice(0, params.limit * 1.5);
                            currYearExampapers = shuffle_list(currYearExampapers);
                            newExampapers = currYearExampapers.slice(0, params.limit);
                            rediser.set(newKey, newExampapers, 60 * 60 * 2);
                            return cont(null, newExampapers);
                        } catch (error) {
                            return cont('HANDLE_ERROR', null);
                        }
                    });
                } else {
                    return cont(null, items);
                }
            } catch (e) {
                Logger.error(e);
                return cont('HANDLE_ERROR', null);
            }
        });
    }), (function (cont, newExampapers) {
        //查找最热试卷
        const limit = 1000;
        let hotKey = cacheKey + (params.period ? ':' + params.period : '');
        hotKey += params.subject ? ':' + params.subject : '';
        hotKey += params.grade ? ':' + params.grade : '';
        hotKey += ':hot';
        rediser.get(hotKey, function (err, items) {
            try {
                if (!items || items.length === 0) {
                    // 2020.06.01规则：8.1前数据展示to_year，8.1后数据为当前年数据+1
                    let to_year = (new Date()).getMonth() > 7 ? (new Date()).getFullYear() + 1 : (new Date()).getFullYear();
                    cond.to_year = { '$gte': to_year };
                    const newCond = lodash.assign({}, cond);
                    if (params.grade) {
                        newCond.grade = _getCurrSemester(params.grade);
                    }
                    db.collection('exampaper').count(newCond, { limit: limit }, function (err, num) {
                        if (err) {
                            return cont(err, null, null);
                        }
                        if (num < Number(params.limit)) {
                            to_year--;
                        }
                        newCond.to_year = { '$gte': to_year }
                        db.collection('exampaper').count(newCond, { limit: limit }, function (errs, paper_num) {
                            if (errs) {
                                return cont(errs, null, null);
                            }
                            if (paper_num < Number(params.limit)) {
                                to_year = to_year - 2;
                                newCond.grade = { '$in': _getCurrSemesterAll(params.grade) }
                            }
                            newCond.to_year = { '$gte': to_year }
                            let proj = { _id: 1, name: 1, vague_name: 1, view_times: 1, download_times: 1, from: 1, to_year: 1, use_type: 1, province: 1, grade: 1, subject: 1, type: 1, city: 1, ctime: 1, from_year: 1 };
                            db.collection('exampaper').find(newCond).project(proj).limit(limit).sort({ view_times: -1 }).toArray(function (err, hotExampapers) {
                                if (err) {
                                    return cont(err, null, null);
                                }
                                try {
                                    if (!hotExampapers) {
                                        return cont('NULL_ERROR', null, null);
                                    }
                                    // 增加排序参数
                                    const init = 1, m = 180, finish = 0.1;
                                    const now = Date.now();
                                    for (const item of hotExampapers) {
                                        const alpha = Math.log(init / finish) / m;
                                        const l = -Math.log(init) / alpha;
                                        let ctime = now;
                                        try {
                                            if (item.ctime) {
                                                if (typeof item.ctime === 'string') {
                                                    ctime = new Date(item.ctime).getTime();
                                                } else {
                                                    ctime = item.ctime.getTime();
                                                }
                                            }
                                        } catch (e) {
                                            Logger.error(e);
                                        }
                                        const t = (Date.now() - ctime) / 86400000; // 当天时间跟创建时间差(天)
                                        const decay = Math.exp(-alpha * (t + l));
                                        item.score = decay * (item.view_times || 0);
                                    }
                                    // 排序
                                    hotExampapers.sort((a, b) => {
                                        return b.score > a.score ? 1 : -1;
                                    });
                                    const param_limit = parseInt(params.limit);
                                    let sortHotExampapers = hotExampapers;
                                    if (hotExampapers.length > param_limit) {
                                        sortHotExampapers = sortHotExampapers.slice(0, param_limit);
                                    }
                                    let arrExams = [];
                                    for (let j = 0; j < sortHotExampapers.length; j++) {
                                        let exampaper = sortHotExampapers[j];
                                        exampaper.vague_name = filterExampaperName(exampaper);
                                        exampaper.name = mapExampaperName(exampaper, req.query);
                                        exampaper.id = exampaper._id;
                                        delete exampaper._id;
                                        delete exampaper.from;
                                        delete exampaper.use_type;
                                        delete exampaper.province;
                                        delete exampaper.grade;
                                        delete exampaper.subject;
                                        // delete exampaper.type;
                                        delete exampaper.from_year;
                                        delete exampaper.city;
                                        delete exampaper.score;
                                        exampaper.ctime = utils.format_date(exampaper.ctime);
                                        arrExams.push(exampaper);
                                    }
                                    arrExams.sort(function (x, y) {
                                        return y.to_year - x.to_year;
                                    });
                                    hotExampapers = arrExams;

                                    rediser.set(hotKey, hotExampapers, 60 * 60 * 2);
                                    return cont(null, hotExampapers);
                                } catch (error) {
                                    cont('HANDLE_ERROR', null, null);
                                }
                            });
                        });
                    });
                } else {
                    return cont(null, items);
                }
            } catch (e) {
                Logger.error(e);
                return cont('HANDLE_ERROR', null);
            }
        });
    })]).then(function (cont, Exampapers) {
        let dailyKey = cacheKey + ':daily_count';
        rediser.get(dailyKey, function (err, items) {
            try {
                if (!items) {
                    db.collection('daily_count').find().limit(1).toArray(function (err, years) {
                        try {
                            if (err)
                                return cont(err, null);
                            if (!years)
                                return cont('NULL_ERROR', null);

                            rediser.set(dailyKey, years, 60 * 60 * 2);
                            let retObj = _getDailyCountData(years, Exampapers[0], Exampapers[1]);
                            return responseWrapper.succ(retObj);
                        } catch (e) {
                            return responseWrapper.error('HANDLE_ERROR');
                        }
                    });
                } else {
                    let retObj = _getDailyCountData(items, Exampapers[0], Exampapers[1]);
                    return responseWrapper.succ(retObj);
                }
            } catch (err) {
                Logger.error(err);
                return responseWrapper.error('HANDLE_ERROR');
            }
        });
    }).fail(function (cont, err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

function shuffle_list(array) {
    let currentIndex = array.length, temporaryValue, randomIndex;
    // 循环直到当前索引超过数组的长度
    while (0 !== currentIndex) {
        // 随机选取一个索引
        randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex -= 1;
        // 交换元素
        temporaryValue = array[currentIndex];
        array[currentIndex] = array[randomIndex];
        array[randomIndex] = temporaryValue;
    }
    return array;
}


// 获取试卷区域统计
async function getExampaperRegionStatistic(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let regionAggregations = (await Promise.all(['整体', '高中', '初中', '小学'].map(period => db.collection('exampaper').aggregate([
            ...period === '整体' ? [] : [{ $match: { period: period } }],
            {
                $group: {
                    _id: '$province',
                    num: { $sum: 1 },
                }
            },
            { $project: { _id: 0, name: '$_id', num: 1 } },
        ]).toArray()))).map((regions, index) => {
            return {
                period: ['整体', '高中', '初中', '小学'][index],
                num: regions.reduce((value, e) => value + e.num, 0),
                regions: regions.sort((a, b) => b.num - a.num).filter(e => !!e.name),
            };
        });
        responseWrapper.succ(regionAggregations);
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
}

async function getRegionExampaper(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;

    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    let cond = {};
    if (params.filter_mkp) {
        cond = { $or: [{ from: { $in: ['ai_organize_paper', 'ai_exam', 'kbp_new', 'other', 'kbp', 'drm', 'tiku'] } }] };
    } else {
        cond = { $or: [{ from: { $in: ['ai_organize_paper', 'ai_exam', 'kbp_new', 'other', 'kbp', 'mkp', 'drm', 'tiku'] } }] };
    }
    cond.subject = params.subject;
    cond.period = params.period;
    if (params.region) {
        cond.region = params.region;
    }

    let cacheKey = _genUpdateKey() + ':' + params.period + ':' + params.subject + ':' + params.region + ':region';
    try {
        let cacheData = await rediser.get(cacheKey);
        if (cacheData) {
            return responseWrapper.succ(cacheData);
        }
        let proj = { _id: 1, name: 1, vague_name: 1, view_times: 1, download_times: 1, ctime: 1, to_year: 1, from: 1, use_type: 1, province: 1, city: 1, grade: 1, subject: 1, type: 1, from_year: 1 };
        let list = await db.collection('exampaper').find(cond).project(proj).skip(params.offset).limit(params.limit).sort({ ctime: -1 }).toArray();
        if (!lodash.size(list)) return responseWrapper.succ([]);
        // 排序
        list.sort((x, y) => y.to_year - x.to_year);
        let resList = [];
        for (const d of list) {
            d.vague_name = filterExampaperName(d);
            d.name = mapExampaperName(d, req.query);
            if (d.vague_name) {
                d.vague_name = d.vague_name.replace('学科网', '')
            }
            resList.push({
                id: d._id,
                name: d.name,
                type: d.type,
                view_times: d.view_times,
                ctime: d.ctime
            });
        }
        // 排序
        resList.sort((a, b) => {
            const date1 = b.ctime;
            date1.setHours(0, 0, 0, 0);
            const date2 = a.ctime;
            date2.setHours(0, 0, 0, 0);
            if (date1.getTime() === date2.getTime()) {
                return b.view_times - a.view_times;
            } else {
                return date1.getTime() - date2.getTime();
            }
        });

        resList = resList.map(it => {
            it.ctime = utils.format_date(it.ctime);
            return it;
        });
        await rediser.set(cacheKey, resList, 60 * 60 * 2);
        return responseWrapper.succ(resList);
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR', err.message)
    }
}

/**
 * 根据不同的资源组装 筛选条件 和 更新项
 *
 * @param {string} resource_filter_id
 */
function _createFilterUpdateObject(req) {
    if (req.params.resource_filter_id === 'exampaper') {
        let period = req.body.period,
            grades = req.body.grades,
            dbs = req.body.dbs;
        if (period && _.isArray(grades) && grades.length) {
            // 验证 grades 数据格式: [ {grade: string, type: []} ]
            let invalid = _.find(grades, grade => (
                !_.isString(grade.grade)
                || !_.isArray(grade.type)
                || !grade.type.length
                || !!_.find(grade.type, type => !_.isString(type) || !type.trim())
            ));

            if (invalid) return;

            return {
                resource: 'exampaper',
                cond: {
                    resource: 'exampaper',
                    'filter.period': period,
                    dbs,
                },
                update: {
                    $set: {
                        'filter.$.grades': grades,
                        utime: req.body.utime || new Date()
                    }
                }
            };
        }
    }
}

/**
 * 验证是否修改或删除原有标签，返回被修改或删除的标签
 * @param {*} req
 */
async function _verifyExampaperTags(req) {
    let new_grades = req.body.grades, dbs = req.body.dbs;
    let old_data = await db.collection('resource_filter').findOne({ resource: 'exampaper', dbs });
    old_data = old_data.filter;
    let old_grades = [];
    for (let i = 0; i < old_data.length; i++) {
        if (old_data[i].period === req.body.period) {
            old_grades = old_data[i];
        }
    }
    // 提取出当前已存在的试卷标签
    old_grades = old_grades.grades;
    let diff_gradeType = [];
    // 提取出被修改或被删除的试卷标签
    lodash.transform(new_grades, (result, new_grade) => {
        let old_grade = _.find(old_grades, grade => grade.grade === new_grade.grade);
        let diff_types = _.difference(old_grade.type, new_grade.type)
        if (_.isArray(diff_types) && diff_types.length > 0) {
            result.push({
                grade: old_grade.grade,
                types: diff_types
            });
        }
    }, diff_gradeType)
    return diff_gradeType;
}

/**
 * 更新 resource_filter 数据
 * @param {string} req.params.resource_type
 * @param {*} res
 */
async function updateResourceFilter(req, res) {
    let responseWrapper = new ResponseWrapper(res);

    let dbs = req.body.dbs;
    if (!dbs) {
        dbs = 'basic';
    } else if (dbs instanceof Array) {
        dbs = dbs.length > 0 ? dbs[0] : 'basic';
    } else if (dbs instanceof String) {
        dbs = dbs.split(',')[0] || 'basic';
    }
    req.body.dbs = dbs;

    let diffGradeType = await _verifyExampaperTags(req);
    // 将结果数组转为字符串
    let strDiffGradeType = diffGradeType.map(e => `【${e.grade}:${e.types.toString()}】`).join('');

    if (diffGradeType.length != 0) {
        return responseWrapper.error('PARAMETERS_ERROR', `已有标签不能删除或修改: ${strDiffGradeType}`);
    }

    let { resource, cond, update } = _createFilterUpdateObject(req) || {};

    let key = `kb_api:v2:${resource}:filters:${dbs}` + process.env.NODE_PORT;

    try {
        if (cond && update) {
            let result = await db.collection('resource_filter').updateOne(cond, update);
            if (result.result.n === 1) {
                await rediser.del(key);
                return responseWrapper.succ({});
            }
            return responseWrapper.error('NULL_ERROR');
        }
        return responseWrapper.error('PARAMETERS_ERROR');
    } catch (err) {
        Logger.error(err);
        responseWrapper.error('HANDLE_ERROR', err);
    }
}

const incDownloads = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let body = req.body;
        await inscreaceDownTimes(body.resource_ids, body.resource_type, body.album_id);
        return responseWrapper.succ({});
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR', err.message)
    }
}

async function getNewNum(req, res) {

    let responseWrapper = new ResponseWrapper(res);
    try {
        const { day } = req.query;
        const date = new Date(Date.now() - day * 24 * 60 * 60 * 1000);
        const cond = {
            ctime: {
                $gte: date
            }
        }
        const result = {
        }
        // 试卷
        result.exampaper = await db.collection('exampaper').count(cond);
        // 试题
        result.question = await db.collection('question').count(cond);

        return responseWrapper.succ(result);
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR', err.message)
    }
}


function _getCurrSemester(grade) {
    const semesterStartDate = new Date();
    semesterStartDate.setMonth(1, 15);
    semesterStartDate.setHours(0, 0, 0, 0);

    const semesterEndDate = new Date();
    semesterEndDate.setMonth(7, 15);
    semesterEndDate.setHours(0, 0, 0, 0);
    let semester = "上";//学期
    const nowDate = new Date().getTime();
    if (nowDate >= semesterStartDate.getTime() && nowDate < semesterEndDate.getTime()) {
        semester = '下';
    }
    return grade + semester;
}

function _getCurrSemesterAll(grade) {
    return [grade + '上', grade + '下'];
}

module.exports = {
    profile,
    profileNew,
    profile_single,
    filters,
    filters2,
    update,
    exampaperUpdateSubject,
    getSeqIds,
    newHot: getNewHotExampaper,
    getVideoStatistics: cacheWrapper(getVideoStatistics, { validTime: 60 * 10 }),
    getExamUpdateStatistics: cacheWrapper(getExamUpdateStatistics, { validTime: 60 * 5 }),
    getExampaperRegionStatistic: cacheWrapper(getExampaperRegionStatistic, { validTime: 60 * 5 }),
    updateResourceFilter,
    incDownloads,
    region: getRegionExampaper,
    newNum: getNewNum,
};
