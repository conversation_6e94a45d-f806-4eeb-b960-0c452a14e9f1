/* eslint-disable no-console */
const Express = require('express');
const Body_parser = require('body-parser');
const Config = require('config');
const Thenjs = require('thenjs');

const Logger = require('./modules/common/utils/logger');
const mongodber = require('./modules/common/utils/mongodber');
const rediser = require('./modules/common/utils/rediser');
const responseProperty = require('./modules/common/middleware/response_property');
const ResponseWrapper = require('./modules/common/utils/response_wrapper');

const isDevelopment = /^development/.test(Config.util.getEnv('NODE_ENV') || '');

/**
 * 根据机器 hostname 获取部署 port 和参数配置文件
 */
const app = Express();

/**
 * 启动server
 */
Thenjs(function (cont) {
    // 初始化基础资源数据库
    let databases = Config.get('DATABASES');
    mongodber.init(databases, function (err) {
        if (err) {
            Logger.error(err);
            process.exit(-1);
        }
        cont(null, null);
    });
}).then(function (cont) {
    // 连接redis
    let redis_conf = Config.get('redis');
    rediser.init(redis_conf, function (err) {
        if (err) {
            Logger.error('Connect Cache Error: ' + err);
            process.exit(-1);
        }
        cont(null, null);
    });
}).then(function (cont) {
    // favicon
    app.use('/favicon.ico', Express.static('public/favicon.ico'));
    // log
    app.use(Logger.init(Config.get('log4js')));
    // 设置 post 上传格式和最大传输数据量
    app.use(Body_parser.json({type: 'application/json', limit: '20480kb'}));
    app.use(Body_parser.urlencoded({extended: true, limit: '20480kb'}));
    app.use(responseProperty);
    // Development : Http Request Log
    if (isDevelopment) {
        app.use((req, res, next) => {
            console.log(req.method, req.originalUrl);
            ~['POST', 'PUT', 'PATCH'].indexOf(req.method) && console.log('RequestBody: ', req.body);
            next();
        });
    }
    // 权限校验
    app.all('*', function (req, res, next) {
        res.timestamps = [];
        res.setTimeout(500, () => {
            res.timeout = true;
        });
        res.header('Access-Control-Allow-Origin', req.headers.origin);
        res.header('Access-Control-Allow-Credentials', true);
        res.header('Access-Control-Allow-Headers', 'X-Requested-With');
        res.header('Access-Control-Allow-Methods', 'PUT,POST,GET,DELETE,OPTIONS');

        let auth_validator = require('./modules/common/middleware/auth').auth_validator;
        if (req.query.freq === 'true') {
            // 设置超频标识
            req.out_of_limits = true;
            res.out_of_limits = true;
            return next();
        }
        auth_validator(req, function (err) {
            if (err) {
                let responseWrapper = new ResponseWrapper(res);
                return responseWrapper.error(err);
            }
            next();
        });
    });

    let route = require('./routes/index');
    route(app);

    // Error Status 404
    app.use(function (req, res) {
        let responseWrapper = new ResponseWrapper(res);
        try {
            Logger.warn({
				who: req.query.api_key,
				where: `${__filename}`,
				what: '404 Not Found',
                why: `${req.originalUrl}`,
                how: req.query,
			});
            return responseWrapper.error('URL_ERROR');
        } catch (err) {
            return responseWrapper.error('HANDLE_ERROR');
        }
    });
    cont(null, null);

}).then(function (cont) {
    // 启动 Server
    let port = Config.get('app').port;
    let server = app.listen(port, cont);
    app.set('port', port);

    // Event listener for HTTP server "error" event.
    function on_error(error) {
        if (error.syscall !== 'listen') {
            throw error;
        }
        let bind = typeof port === 'string' ? 'Pipe ' + port : 'Port ' + port;
        // handle specific listen errors with friendly messages
        switch (error.code) {
            case 'EACCES':
                Logger.error(bind + ' requires elevated privileges');
                process.exit(1);
                break;
            case 'EADDRINUSE':
                Logger.error(bind + ' is already in use');
                process.exit(1);
                break;
            default:
                throw error;
        }
    }

    server.on('error', on_error);

}).then(function () {
    // 服务启动成功
    let app_name = Config.get('app.name');
    Logger.info(app_name + ' : Express Listen ' + app.get('port'));
}).fail(function (cont, err) {
    // 服务启动失败
    if (err.stack) Logger.error('Error: ' + err.stack);
    else Logger.error('Error: ' + err);
    process.exit(-1);
});

if (process.env.NODE_HEAPDUMP_OPTIONS === 'signal') {
    process.on('SIGUSR2', function(){
        let heapdump = require('heapdump');
        heapdump.writeSnapshot(function(err, filename){
            if (err) {
                console.error(err.stack);
                console.log('dump written to', filename);
            }
        });
    });
}


process.on('uncaughtException', function (err) {
    Logger.error('Caught exception: ' + err.stack);
});
