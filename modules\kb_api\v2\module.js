const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const _ = require('lodash');
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const counter = require('../../common/utils/counter');
const db = mongodber.use('KB');
const schemaModule = require('./schemas/module');

// 数据库集合名
const moduleColl = 'module'; // todo 根据‘学科学段’建立唯一索引
const knowColl = 'knowledge';

const getDataTemplate = (body) => ({
    period: body.period,
    subject: body.subject,
    children: body.children,
});

const getKnowledgeIds = (module, knowIds) => {
    let children = module.children;
    let knowledges = module.knowledges;
    if (Array.isArray(children)) {
        for (let i = 0; i < children.length; i++) {
            getKnowledgeIds(children[i], knowIds);
        }
    } else if (Array.isArray(knowledges)) {
        for (let j = 0; j < knowledges.length; j++) {
            if (knowledges[j] && knowledges[j].id) {
                knowIds.push(knowledges[j].id);
            }
        }
    }
};

// 获取做了修改的知识点
const getUpdatedKnowledges = (module, know) => {
    let children = module.children;
    let knowledges = module.knowledges;
    if (Array.isArray(children)) {
        for (let i = 0; i < children.length; i++) {
            getUpdatedKnowledges(children[i], know);
        }
    } else if (Array.isArray(knowledges)) {
        for (let j = 0; j < knowledges.length; j++) {
            if (knowledges[j] && knowledges[j].id && knowledges[j].targets) {
                know.push(knowledges[j]);
                knowledges[j] = {
                    id: knowledges[j].id,
                    name: knowledges[j].name,
                };
            }
        }
    }
};

// 更新到 knowledge 表
const updateKnowledge = async (know, moduleId) => {
    for (let i = 0; i < know.length; i++) {
        let k = know[i];
        let targets = [];
        let knowMethods = [];
        if (Array.isArray(k.targets)) {
            targets = k.targets.map(item => ({ id: item.id, name: item.name,coefficient:item.coefficient }));
        }
        if (Array.isArray(k.know_methods)) {
            knowMethods = k.know_methods.map(item => ({ id: item.id, name: item.name ,coefficient:item.coefficient}));
        }
        let d = {
            targets: targets,
            know_methods: knowMethods,
            module_id: moduleId,
            utime: new Date()
        };
        await db.collection(knowColl).updateOne({ _id: k.id }, { $set: d });
    }
};

// 添加模块id
const addModuleId = async (module) => {
    let children = module.children;
    if (Array.isArray(children)) {
        for (let i = 0; i < children.length; i++) {
            if (!children[i].id) {
                const seq = await counter.nextSeq('module', 1);
                children[i].id = seq[0];
            }
            await addModuleId(children[i]);
        }
    }
};

// kbp审核通过，推送过来，添加
const postModule = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    let now = new Date();
    try {
        let data = getDataTemplate(body);
        // 格式校验
        if (!ajv.validate(schemaModule.putModule, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '格式错误');
        }
        await addModuleId(data);

        const seq = await counter.nextSeq('module', 1);
        data._id = seq[0];
        data.ctime = now;
        data.utime = now;
        let know = [];
        getUpdatedKnowledges(data, know);

        let result = await db.collection(moduleColl).insertOne(data);
        if (result.result.ok === 1) {
            await updateKnowledge(know, seq[0]);
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '新增出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};


// kbp审核通过，推送过来，更新
const updateModule = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let moduleId = Number(req.params.id);
    let body = req.body;
    try {
        let data = getDataTemplate(body);
        // 格式校验
        if (!ajv.validate(schemaModule.putModule, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '格式错误');
        }
        await addModuleId(data);

        let know = [];
        getUpdatedKnowledges(data, know);

        // 删除知识点上的模块id
        let cond = { _id: moduleId };
        let newKnowIds = [];
        let oldKnowIds = [];
        let oldData = await db.collection(moduleColl).findOne(cond);
        getKnowledgeIds(data, newKnowIds);
        getKnowledgeIds(oldData, oldKnowIds);
        let diffIds = _.difference(oldKnowIds, newKnowIds);
        if (diffIds.length > 0) {
            let c = { _id: { $in: diffIds } };
            let s = { $unset: { module_id: '' } };
            await db.collection(knowColl).updateMany(c, s);
        }

        let setData = { $set: data };
        let result = await db.collection(moduleColl).updateOne(cond, setData);
        if (result.result.ok === 1) {
            await updateKnowledge(know, moduleId);
            responseWrapper.succ({});
        } else {
            responseWrapper.error('HANDLE_ERROR', '修改出错了');
        }
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};


const getJsonKnow = (allKnow) => {
    let jsonKnow = {};
    for (let i = 0; i < allKnow.length; i++) {
        allKnow[i].id = allKnow[i]._id;
        delete allKnow[i]._id;
        jsonKnow[allKnow[i].id] = allKnow[i];
    }
    return jsonKnow;
};

const setKnowledges = (module, jsonKnow) => {
    let children = module.children;
    let knowledges = module.knowledges;
    if (Array.isArray(children)) {
        for (let i = 0; i < children.length; i++) {
            setKnowledges(children[i], jsonKnow);
        }
    } else if (Array.isArray(knowledges)) {
        for (let j = 0; j < knowledges.length; j++) {
            if (knowledges[j] && knowledges[j].id) {
                knowledges[j] = jsonKnow[knowledges[j].id];
            }
        }
    }
};

const handleAttach = async (result, attach) => {
    if ('category' === attach) {
        let knowIds = [];
        getKnowledgeIds(result, knowIds);
        let project = { name: 1, targets: 1, know_methods: 1 };
        let cond = { _id: { $in: knowIds } };
        let allKnow = await db.collection(knowColl).find(cond).project(project).toArray();
        let jsonKnow = getJsonKnow(allKnow);
        setKnowledges(result, jsonKnow);
    }
};

// 获取模块详情，根据id
const getModuleById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let id = req.params.id;
    let attach = req.query.attach;
    let cond = { _id: Number(id) };
    try {
        let result = await db.collection(moduleColl).findOne(cond);
        if (result) {
            result.id = result._id;
            delete result._id;
            await handleAttach(result, attach);
        }
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 获取模块详情，根据学段学科
const getModulesList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let { period, subject, attach } = req.query;
    let cond = {};
    if (period) {
        cond.period = period;
    }
    if (subject) {
        cond.subject = subject;
    }
    try {
        let resultList = await db.collection(moduleColl).find(cond).toArray();
        if (resultList && resultList.length > 0) {
            for (let i = 0; i < resultList.length; i++) {
                let result = resultList[i];
                result.id = result._id;
                delete result._id;
                await handleAttach(result, attach);
            }
        }
        responseWrapper.succ(resultList);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};


module.exports = {
    postModule, // kbp审核通过，推送过来，添加
    updateModule, // kbp审核通过，推送过来，更新
    getModulesList, // 获取模块详情，根据学段学科
    getModuleById, // 获取模块详情，根据id
};
