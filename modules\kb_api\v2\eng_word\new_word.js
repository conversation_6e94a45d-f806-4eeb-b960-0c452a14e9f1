let ResponseWrapper = require('../../../common/utils/response_wrapper');
let mongodber = require('../../../common/utils/mongodber');
const fs = require('fs');
const promisify = require('util').promisify;
const unlinkFile = promisify(fs.unlink);
const xlsx = require('node-xlsx');
const logger = require('../../../common/utils/logger');
const { search, batchSearch } = require('node-ecdict');
const _ = require('lodash');
const { response } = require('express');
const ObjectID = require('mongodb').ObjectID;

let db = mongodber.use('KB');
let wordColl = 'eng_word_level';

const levels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

const checkData = async(req, res) => {
    // let responseWrapper = new ResponseWrapper(res);
    let file = req.body;
    const excel = req.body.excel;
    let excelData = [];
    let data = [];
    let nullDataFlag = true;
    let scene;
    for (let i = 0, len = excel.length; i < len; i++) {
        const item = excel[0];
        scene = item.name;
        excelData = item.data;
    }
    if (excelData.length > 0) {
        let danci = excelData[0][0];
        let level = excelData[0][1];
        if (danci && level) {
            danci = danci.trim();
            level = level.trim();
        } else {
            // await unlinkFile(file.path);
            let msg = '请使用正确的模版';
            return res.json({
                data: msg
            });
        }
        let count = 0;
        if (danci === '单词' && level === 'level') {
            for (let i = 1; i < excelData.length; i++) {
                let name = excelData[i][0];
                let level = excelData[i][1];
                if (!name && !level) {
                    count++;
                    if (count > 100) {
                        break;
                    }
                    continue;
                }
                if (!(typeof name === 'string') || !(typeof level === 'number')) {
                    // await unlinkFile(file.path);
                    let msg = `第${i + 1}行格式错误`;
                    return res.json({
                        data: msg
                    });
                }
                name = name.toLowerCase();
                let danci = await search(name);
                if (danci[0]) {
                    let exchange = danci[0].exchange;
                    for (let j = 0; j < exchange.length; j++) {
                        let flag = exchange[j].split(':');
                        if (flag[0] === '0') {
                            name = flag[1];
                        }
                        continue;
                    }
                }
                let dd = {
                    name: name.toLowerCase(),
                    level: level,
                    scene: scene,
                    ctime: new Date()
                };
                nullDataFlag = false;
                let num = await db.collection('eng_word_level').find({ name: dd.name, scene: scene, level: level }).count();
                if (num > 0) {
                    continue;
                }
                data.push(dd);
            }
        } else {
            // await unlinkFile(file.path);
            let msg = '请使用正确的模版';
            return res.json({
                data: msg
            });
        }
    }
    if (data.length > 0) {
        let insertData = Array.from(new Set(data));
        // await unlinkFile(file.path);
        return res.json({
            data: insertData
        });
    } else {
        // await unlinkFile(file.path);
        let msg = '';
        if (nullDataFlag) {
            msg = '当前模版为空，请修改后再导入';
        } else {
            msg = '单词、单词级别与系统中重复，请修改后再导入';
        }
        return res.json({
            data: msg
        });
    }
}

const getNewWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let { words, level = 1 } = req.body;
    let scene = req.body.scene;
    let levelsArr = levels.slice(0, level);
    let wordsArrLen = words.match(/[a-z]+[-']?[a-z]*/ig, '') || [];
    let charArr = words.replace(/[A-Za-z]+\(+[\u4e00-\u9fa5]+\)/ig, '');
    let wordsArr = charArr.match(/[a-z]+[-']?[a-z]*/ig, '') || [];

    try {
        if (wordsArr.length === 0) {
            return responseWrapper.succ({ new_word_rate: 0, new_word: [] });
        }
        for (let i = 0; i < wordsArr.length; i++) {
            if (_.endsWith(wordsArr[i],'\'s')) {
                wordsArr[i] = wordsArr[i].replace('\'s','');
            }
            if (_.endsWith(wordsArr[i],'s\'')) {
                wordsArr[i] = wordsArr[i].replace('s\'','');
            }
            wordsArr[i] = wordsArr[i].toLowerCase();

            let danci = await db.collection('eng_word_origin').findOne({name: wordsArr[i]});
            if (danci && danci.origin) {
                wordsArr[i] = danci.origin;
            }

            // let danci = await search(wordsArr[i]);
            // if (danci[0]) {
            //     let exchange = danci[0].exchange;
            //     for (let j = 0; j < exchange.length; j++) {
            //         let flag = exchange[j].split(':');
            //         if (flag[0] === '0') {
            //             wordsArr[i] = flag[1];
            //         }
            //         continue;
            //     }
            // }
        }
        let wordsUnique = Array.from(new Set(wordsArr));
        let cond = { name: { $in: wordsArr }, level: { $in: levelsArr }, scene: scene };
        let data = await db.collection(wordColl).find(cond).toArray();
        if (!data) {
            return responseWrapper.error('NULL_ERROR');
        }
        for (let r of data) {
            let index = wordsUnique.indexOf(r.name);
            if (index > -1) {
                wordsUnique.splice(index, 1);
            }
        }
        let new_word_rate = ((wordsUnique.length / wordsArrLen.length) * 100).toFixed(2) + '%';

        let corecond = {
            level: level,
            scene: scene
        };
        let coredata = [];
        let coredataArray = await db.collection(wordColl).find(corecond, { name: 1, _id: 0 }).toArray();
        for (let r of coredataArray) {
            coredata.push(r.name);
        }
        let coreWords = _.intersection(wordsArr, coredata);
        let nolearnWords = _.without(coredata, ...coreWords);
        //前三长的句子
        let wordsStr = words.split('.');
        let strArray = [];
        for (let s of wordsStr) {
            let str = {};
            str.words = s;
            let strword = s.split(' ');
            str.length = strword.length;
            strArray.push(str);
        }
        for (let i = 0; i < strArray.length - 1; i++) {
            for (let j = 0; j < strArray.length - i - 1; j++) {   // 这里说明为什么需要-1
                if (strArray[j].length < strArray[j + 1].length) {
                    let temp = strArray[j];
                    strArray[j] = strArray[j + 1];
                    strArray[j + 1] = temp;
                }
            }
        }
        if (strArray.length > 3) {
            strArray = strArray.slice(0, 3);
        }
        logger.info('done');
        return responseWrapper.succ({
            total_num: wordsArrLen.length,
            new_word_rate,
            new_word: wordsUnique,
            coreWords: coreWords,
            nolearnWords: nolearnWords,
            longwords: strArray
        });
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const addNewWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let insertData = req.body;
        await db.collection('eng_word_level').insertMany(insertData);
        logger.info(new Date + '上传数据' + insertData.length);
        return responseWrapper.succ();
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const getScene = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let data = await db.collection(wordColl).distinct('scene');
        return responseWrapper.succ(data);
    } catch (err) {
        logger.err(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const updateScene = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let type = req.body.type;
        let name = req.body.name;
        let newname = req.body.newname;
        if (type === 'update') {
            let scenedata = await db.collection(wordColl).findOne({ scene: newname });
            if (scenedata) {
                return res.json({
                    code: 5,
                    msg: '场景重复'
                });
            }
            await db.collection(wordColl).updateMany({ scene: name }, { $set: { scene: newname, utime: new Date() } });
        } else if (type === 'delete') {
            await db.collection(wordColl).remove({ scene: name });
        }
        return responseWrapper.succ();
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const wordsList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let scene = req.query.scene;
        let offset = parseInt(req.query.offset) || 0;
        let limit = parseInt(req.query.limit) || 10;

        let data = await db.collection(wordColl).find({scene: scene}).skip(offset).limit(limit).toArray();
        for (let word of data) {
            word.id = word._id;
            delete word._id;
        }
        let total_num = await db.collection(wordColl).find({scene: scene}).count();
        return responseWrapper.succ({
            total_num,
            list: data
        });
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const updateWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let id = req.params.id;
        let name = req.body.name;
        await db.collection(wordColl).updateOne({_id: ObjectID(id)},{$set:{name: name}});
        return responseWrapper.succ({});
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const searchWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let scene = req.query.scene;
        let name = req.query.name;
        let cond = {scene: scene,name: name};
        let word = await db.collection(wordColl).findOne(cond);
        if (!word) {
            return responseWrapper.succ(null);
        }
        word.id = word._id;
        delete word._id;
        return responseWrapper.succ(word);
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
};
const deleteWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let id = req.params.id;
        await db.collection(wordColl).remove({_id: ObjectID(id)});
        return responseWrapper.succ({});
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

module.exports = {
    getNewWord,
    addNewWord,
    getScene,
    updateScene,
    wordsList,
    updateWord,
    searchWord,
    deleteWord,
    checkData
};
