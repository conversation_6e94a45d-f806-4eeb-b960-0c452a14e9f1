// kbp试卷分析
const exampaper_analysis = require('../../../modules/kb_api/v2/exam_analysis/exampaper_analysis');

module.exports = function (router) {
    // 单个试卷id分析（kbp调用）
    router.get('/exampaper_statistics/single/:exampaper_id', exampaper_analysis.getSingleExampaperStatistics);
    // 获取近N年试卷分析（kbp调用）
    router.get('/exampaper_statistics/many/:exampaper_ids', exampaper_analysis.getManyExampaperStatistics);
    // 获取试卷分析tabs（kbp调用）
    router.get('/exampaper_statistics/tabs', exampaper_analysis.getTabs);
    // 试卷分析文案查询（kbp调用）
    router.get('/exampaper_statistics/comment', exampaper_analysis.getComment);
    // 试卷分析文案保存（kbp调用）
    router.put('/exampaper_statistics/comment', exampaper_analysis.setComment);
};