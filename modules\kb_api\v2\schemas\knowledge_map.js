const Ajv = require('ajv');
const ajv = new Ajv({removeAdditional: 'all'});
const Logger = require('../../../common/utils/logger');

const mapKnow = {
    'type': 'object',
    'required': ['id', 'name'],
    'properties': {
        'id': {
            'type': 'number',
        },
        'name': {
            'type': 'string',
        }
    }
};

const putSchema = {
    type: 'array',
    additionalProperties: false,
    items: mapKnow
};

const putValidate = (data) => {
    if (!ajv.validate(putSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('映射格式错误');
    }
};

module.exports = {
    putValidate
};
