const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const params_utils = require('../../common/utils/params.js');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../common/utils/rediser');
const kb_api = require('../config.js').kb_api['v2'];
const _ = require('underscore');
const logger = require('../../common/utils/logger');
const { ObjectID } = require('mongodb');

/**
 * 获取区域教材统计数据
 * @param {*} req 
 * @param {*} res 
 */
async function get_region_book_statistics(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        params = params_utils.create_params(req, kb_api['get_region_book_statistics']);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    let rediser_Key = params.cache_key;
    try {
        let item = await rediser.get(rediser_Key);
        if (item) {
            return responseWrapper.succ(item);
        }

        let cond = {
            'province': params.province,
            'city': params.city,
            'country': params.country,
        };
        let statistics_data = await db.collection('region_press_version').findOne(cond, { sort: { _id: -1 }, projection: { version_count: 1 } });
        if (!statistics_data) {
            return responseWrapper.error('HANDLE_ERROR', '没有查询到数据。');
        }
        if (statistics_data.version_count == null || !(statistics_data.version_count instanceof Array) || statistics_data.version_count.length === 0) {
            return responseWrapper.succ({ version_count: [] });
        }
        let returnobj = {
            version_count: _.sortBy(statistics_data.version_count, function (data) {
                if (data) {
                    if (data['name'] === '小学')
                        return 1;
                    if (data['name'] === '初中')
                        return 2;
                    if (data['name'] === '高中')
                        return 3;
                }
            }),
        };
        rediser.set(rediser_Key, returnobj, 60 * 30);
        return responseWrapper.succ(returnobj);
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }
}

/**
 * 获取区域教材数据
 * @param {*} req 
 * @param {*} res 
 */
async function get_region_book_regions(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        params = params_utils.create_params(req, kb_api['get_region_book_regions']);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    let cond = {
        'province': params.province,
        'city': params.city,
        'country': params.country,
    };
    let retrun_data = {
        region: {},
        books: []
    };
    try {
        let result_data = await db.collection('region_press_version').findOne(cond, { sort: { _id: -1 } });
        if (!result_data) {
            return responseWrapper.error('HANDLE_ERROR', '没有查询到数据.');
        }
        retrun_data.region.id = result_data._id;
        retrun_data.region.province = result_data.province;
        retrun_data.region.city = result_data.city;
        retrun_data.region.country = result_data.country;
        if (result_data.periods == null || !(result_data.periods instanceof Array) || result_data.periods.length == 0) {
            return responseWrapper.succ(retrun_data);
        }
        retrun_data.books = _.sortBy(result_data.periods, function (data) {
            if (data) {
                if (data['name'] === '小学')
                    return 1;
                if (data['name'] === '初中')
                    return 2;
                if (data['name'] === '高中')
                    return 3;
            }
        });
        return responseWrapper.succ(retrun_data);
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }
}

/**
 * 设置区域教材信息
 * @param {*} req 
 * @param {*} res 
 */
async function put_region_book_regions(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        params = params_utils.create_params(req, kb_api['put_region_book_regions']);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    let cond = { '_id': params.rb_id };
    try {
        let result_data = await db.collection('region_press_version').findOne(cond, { sort: { _id: -1 } });
        if (!result_data) {
            return responseWrapper.error('HANDLE_ERROR', '没有查询到数据');
        }
        let periods = params.periods;
        if (!check_periods_grades(result_data.periods, periods) || !check_periods_grades(periods, result_data.periods)) {
            let logconent = '数据结构错误,学段、年级与原始数据不符';
            Logger.error(logconent);
            return responseWrapper.error('HANDLE_ERROR', logconent);
        }
        //更新数据
        let filter = { '_id': params.rb_id };
        let update = { $set: { periods: periods } };
        let options = { upsert: true };
        let result = await db.collection('region_press_version').updateOne(filter, update, options);
        if (!result || !result.result || result.result.ok !== 1) {
            Logger.error(result);
            return responseWrapper.error('HANDLE_ERROR', result);
        }
        return responseWrapper.succ({});
    } catch (error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR', error);
    }
}
/**
 * 验证更新的数据在原始数据中是否存在学段年级结构数据。
 * @param {Array} periods_s *原始数据
 * @param {Array} periods_t *更新数据
 * @returns {boolean} 
 */
function check_periods_grades(periods_s, periods_t) {
    let a_periods = periods_s;
    let b_periods = periods_t;
    for (let a_period of a_periods) {
        let p_index = _.indexOf(_.map(b_periods, 'name'), a_period.name);
        if (p_index === -1) {
            return false;
        }
        let b_grades = b_periods[p_index].grades;
        let a_grades = a_period.grades;
        for (let a_grade of a_grades) {
            let g_index = _.indexOf(_.map(b_grades, 'name'), a_grade.name);
            if (g_index === -1) {
                return false;
            }
        }
    }
    return true;
}

/**
 * 根据id获取最新区域教材版本数据
 * @param {*} req 
 * @param {*} res 
 */
async function get_region_book_by_id(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let rbId = parseInt(req.params.rb_id);
    if (!rbId) {
        throw new Error('缺少必要参数');
    }
    try {
        let result = await db.collection('region_press_version').findOne({ _id: rbId });
        if (result) {
            return responseWrapper.succ(result);
        } else {
            throw Error('无数据');
        }
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', error);
    }
}

async function put_region_book_by_id(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let id = Number(req.params.id);
    let grades = req.body.data.grades;
    let name = req.body.data.name;
    if (!id || !grades || !name) {
        return responseWrapper.error('PARAMETERS_ERROR', 'id，data,name不能为空');
    }

    try {
        let result_data = await db.collection('region_press_version').findOne({ _id: id });
        if (!result_data) {
            return responseWrapper.error('HANDLE_ERROR', '没有查询到数据');
        }
        await db.collection('region_press_version').updateOne({ _id: id, "periods.name": name }, { $set: { "periods.$.grades": grades, utime: new Date() } });
        return responseWrapper.succ({});
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', error);
    }
}

module.exports = {
    get_region_book_statistics: get_region_book_statistics,
    get_region_book_regions: get_region_book_regions,
    put_region_book_regions: put_region_book_regions,
    get_region_book_by_id: get_region_book_by_id,
    put_region_book_by_id: put_region_book_by_id
}