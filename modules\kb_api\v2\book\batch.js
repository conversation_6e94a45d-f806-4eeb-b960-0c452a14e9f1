const Logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const loosen_tree = require('../utils.js').loosen_tree;
const _getChapterIdsInBook = require('../book')._getChapterIdsInBook;
const _assignChaptersToBook = require('../book')._assignChaptersToBook;
const cacheWrapper = require('../../../common/utils/cache_wrapper');
const _ = require('lodash');

/**
 * 填充知识点
 * @param {*} chapters      - 章节数据
 * @return {Promise<any>}
 * @private
 */
function _fillKnowledgeStage(chapters) {
    return new Promise(function (resolve, reject) {
        let kIds = [];
        chapters.forEach(function (chapter) {
            if (chapter.hasOwnProperty('knowledges')) {
                chapter.knowledges.forEach(function (knowledge) {
                    kIds.push(knowledge.id);
                });
            }
        });
        let proj = { _id: 1, stages: 1, pre_knowledges: 1, related_knowledges: 1 };
        db.collection('knowledge').find({ _id: { $in: kIds } }).project(proj).toArray(function (err, stageInfos) {
            if (err) return reject(err);
            try {
                chapters.forEach(function (chapter) {
                    if (chapter.hasOwnProperty('knowledges')) {
                        chapter.knowledges.forEach(function (knowledge) {
                            let findInfo = _.find(stageInfos, function (info) {
                                return info._id === knowledge.id;
                            });
                            if (findInfo) {
                                delete findInfo['_id'];
                                if (findInfo['stages']) {
                                    knowledge['stages'] = findInfo['stages'];
                                }
                                if (findInfo['pre_knowledges']) {
                                    knowledge['pre_knowledges'] = findInfo['pre_knowledges'];
                                }
                                if (findInfo['related_knowledges']) {
                                    knowledge['related_knowledges'] = findInfo['related_knowledges'];
                                }
                            }
                        });
                    }
                });
            } catch (err) {
                return reject(err);
            }
            return resolve(chapters);
        });
    });
}

/**
 * 根据 ID 批量请求教材详情
 * @param {*} req
 * @param {*} res
 * @return {Promise<*>}
 */
async function batchGetBookDetail(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let ids = (req.query.ids || '').split(',').map(e => +e).filter(e => e);
        let params = req.query;
        // 判断 params 中的 fields_type 是否为 'eng_book' 然后选择相应的数据库进行查询 book 的详细数据
        let dbName = {
            'book': 'book',
            'chapter': 'book_chapter'
        };
        if (params['fields_type'] === 'eng_book') {
            dbName['book'] = 'eng_book';
            dbName['chapter'] = 'eng_book_chapter';
        }
        let coll = db.collection('book');
        // 查询条件
        let cond = { _id: { $in: ids } };
        let books = await coll.find(cond, { has_modified: 0 }).toArray();
        // 过滤无权限的教材
        const queryDbs = new Set((req.query.dbs || 'basic').split(','));
        books = books.filter(book => {
            return (book.dbs || ['basic']).some(item => queryDbs.has(item));
        });
        for (let book of books) {
            let chapterIds = [];
            _getChapterIdsInBook('chapters', book.chapters, chapterIds);
            let chapters = await db.collection('book_chapter').find({ _id: { $in: chapterIds } }).project({
                ctime: 0,
                utime: 0,
                path: 0
            }).toArray();
            if (params['type'] === 'plan') {
                await _fillKnowledgeStage(chapters);
            } else {
                book = _assignChaptersToBook('book', book, chapters);
                book['id'] = book['_id'];
                delete book['_id'];
                book['book'] = {
                    'chapters': book.chapters,
                };
                delete book['chapters'];
            }
            let keys = {
                'chapters': 'chapter',
                'knowledges': 'knowledge',
                'eng_words': 'eng_word',
                'eng_sentences': 'eng_sentence',
                'eng_dialogs': 'eng_dialog',
            };
            let excludes = [];
            if (params.fields_type === 'knowledge') {
                excludes = ['eng_words', 'eng_sentences', 'eng_dialogs'];
            } else if (params.fields_type === 'chapter') {
                excludes = ['knowledges', 'eng_words', 'eng_sentences', 'eng_dialogs'];
            }
            loosen_tree(book['book'], keys, excludes);
        }
        // 排序
        const result = [];
        if (books) {
            for (const id of ids) {
                const book = books.find(e => e.id === +id);
                result.push(book);
            }
        }
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e.toString());
    }
}

module.exports = {
    batchGetBookDetail: cacheWrapper(batchGetBookDetail, { validTime: 60 * 30 }),
};
