const { validateQuestion, flattenKnowledges, flattenElements } = require('./validate_question');
const cheerio = require('cheerio');

// const Logger = require('../../../../common/utils/logger');

// 将css属性变成对象
function _getCssList(css) {
    // 将转义的引号换成非转义的引号
    css = css.replace(/&quot;/g, '"');

    var list = {},
        // 匹配css属性（例如： color: white; text-align: center ）[]中的-是特殊字符，需要转义
        reg = /\s*([\w\\-]+)\s*:([^;]*)(;|$)/g,
        match;
    while ((match = reg.exec(css))) {
        var key = match[1].toLowerCase().trim(),
            val = match[2].trim();
        list[key] = val.replace('"', '');
    }
    return list;
}
// 过滤Style属性
function _getCanCssStr(str, isPaste = false) {
    // 提取style字符串
    return str.replace(/style\s*=\s*('[^']*'|"[^"]*")/igm, function ($0, $1, $2) {
        let cssJson = _getCssList($1);
        let styleStr = '';

        const map = new Map([
            ['text-indent', '0px'],
            ['text-decoration', ''],
            ['text-decoration-line', ''],
            ['font-style', 'normal'],
            ['text-align', 'left']
        ]);

        if (!isPaste) {
            map.set('float', '');
        }

        for (const key in cssJson) {
            if (map.has(key)) {
                let value = cssJson[key];
                if (value === '' || value === map.get(key)) {
                    continue;
                }

                styleStr += key + ':' + value + ';';
            }
        }

        return styleStr !== '' ? `style="${styleStr}"` : '';
    });
}

// 将Style属性转换成u、s、b、i标签
function _styleToTag(style) {
    let styleJson = _getCssList(style);
    const tagList = {
        'text-decoration-line': {
            'underline': 'u',
            'line-through': 's'
        },
        'text-decoration': {
            'underline': 'u',
            'line-through': 's'
        },
        'font-weight': {
            'bold': 'b'
        },
        'font-style': {
            'italic': 'i'
        }
    };

    let callStr = '';
    let tagArray = [];

    for (let key in styleJson) {
        let value = styleJson[key];
        if (tagList[key] && tagList[key][value]) {
            tagArray.push(tagList[key][value]);
        } else {
            callStr += `${key}: ${value};`;
        }
    }
    return {
        tagArray,
        callStr
    };
}

// 过滤
function _htmlRegParser(html) {
    try {
        // 匹配所有span标签 注意：match与replace方法的正则匹配需要保持一致，因为是以index匹配替换的,必须对应
        let spans = html.match(/<\/?span[^>]*>/gi) || [];
        let eSpanNum = spans.filter(span => span === '</span>').length; // 结束标签应该span标签总数的一半，否则直接跳过，保留原数据
        if (Array.isArray(spans) && spans.length > 0 && eSpanNum * 2 === spans.length) {
            spans = spans.map((tag, index) => ({
                content: tag,
                type: tag === '</span>' ? 'eTag' : 'sTag',
                matched: false,
                index
            }));
            spans.forEach((item, index) => {
                if (item.type === 'eTag' && !item.matched) { // 是结束标签并且没有配对过，找最近的开始标签
                    for (let i = index - 1; i >= 0; i--) {
                        if (spans[i].type === 'sTag' && !spans[i].matched) {
                            let result = {};
                            // 可转换为标签形式的style属性转换为标签形式
                            spans[i].content = spans[i].content.replace(/style\s*=\s*('[^']*'|"[^"]*")/ig, (current, style) => {
                                result = _styleToTag(style);
                                if (result.callStr) {
                                    return `style="${result.callStr}"`;
                                }
                                return style;
                            });
                            if (result.tagArray && result.tagArray.length > 0) {
                                // 将u标签等追加到开始标签的前面（比如<span><u>）
                                spans[i].content += result.tagArray.map(tag => `<${tag}>`).join('');
                                // 将u标签等追加到结束标签的后面（比如</u></span>）
                                item.content = result.tagArray.map(tag => `</${tag}>`).reverse().join('') + item.content;
                            }
                            spans[i].matched = true;
                            break; // 匹配到到最近的一个开始标签就终止
                        }
                    }
                    item.matched = true;
                }
            });

            // 处理span标签中的特定样式转特定标签
            let i = 0;
            html = html.replace(/<\/?span[^>]*>/gi, function (str, $1) {
                let span = spans[i];
                if (!span) {
                    return str;
                }
                i++;
                return span.content;
            });
        }
        // 去除所有span标签
        html = html.replace(/<\/?span.*?>/gi, '');
        // 处理所有标签的style样式
        html = _getCanCssStr(html);
        // 删除所有class/id
        html = html.replace(/\s?(class|id)=('|").*?('|")/igm, '');
    } catch (err) {
        // catch到错误直接返回出现错误前的html (比如说原数据标签对应关系错乱的情况可能会报错，这时最好是去编辑平台处理)
        return html;
    }
    return html;
}
function _htmlParser(html) {
    // 不是字符串或者为空，或者不含有标签直接返回
    if (!html || (typeof html) !== 'string' || !html.match(/<\/?.+?\/?>/g)) {
        return html;
    }
    // if (!html.match(/style="[^=>]*"([(\s+\w+=)|>])/g)) {

    // }
    // 需要保留的css属性
    const keepCss = ['text-indent', 'text-decoration', 'text-decoration-line', 'float', 'font-style', 'text-align', 'vertical-align'];
    // 需要转换为标签的css属性
    const tagCss = {
        'text-decoration-line': {
            'underline': 'u',
            'line-through': 's'
        },
        'text-decoration': {
            'underline': 'u',
            'line-through': 's'
        },
        'font-weight': {
            'bold': 'b'
        },
        'font-style': {
            'italic': 'i'
        }
    };
    const $ = cheerio.load(html, {
        decodeEntities: false,
        _useHtmlParser2: true,
        // xmlMode: true
    });
    // 将span中部分css转为标签，并去除span标签
    // 为什么要用while？（使用replaceWith会丢失子节点的引用，假如被替换的子节点中存在span标签，此时需要重新遍历）
    while ($('span').length > 0) {
        $('span').each((index, span) => {
            let spanDom = $(span);
            let sTag = '';
            let eTag = '';
            Object.keys(tagCss).forEach(attr => {
                const attrValue = spanDom.css(attr);
                Object.keys(tagCss[attr]).forEach(key => {
                    if (key === attrValue) {
                        sTag += `<${tagCss[attr][key]}>`;
                        eTag = `</${tagCss[attr][key]}>` + eTag;
                    }
                });
            });
            spanDom.replaceWith(sTag + spanDom.html() + eTag);
        });
    }
    $('*').each((index, e) => {
         const node = $(e);
        // 转换img和td style中的width和height
        if (e.name === 'img' || e.name === 'td') {
            let width = node.css('width');
            let height = node.css('height');
            if (width) {
                node.attr('width', width);
            }
            if (height) {
                node.attr('height', height);
            }
        }
        let style = node.css();
        if (style) {
            node.removeAttr('style');
            Object.keys(style).forEach(key => {
                if (keepCss.indexOf(key) !== -1) {
                    node.css(key, style[key]);
                }
            });
        }
        node.removeAttr('id');
        node.removeAttr('class');

    });

    html = $.html();
    return html;
}
const _filterBasic = (data) => {
    const length = data.length;
    for (let i = 0; i < length; i++) {
        data[i] = _htmlParser(data[i]);
    }
    return data;
};

const _filterStems = (types, data) => {
    const length = data.length;
    for (let i = 0; i < length; i++) {
        const type = types[i];
        const obj = data[i];
        obj.stem = _htmlParser(obj.stem);
        if (['选择题', '多选题'].includes(type)) {
            const options = obj.options;

            for (const key in options) {
                if (options.hasOwnProperty(key)) {
                    options[key] = _htmlParser(options[key]);
                }
            }
            obj.options = options;
        }
        data[i] = obj;
    }
    return data;
};

const _filterAnswer = (types, data) => {
    const length = data.length;

    for (let i = 0; i < length; i++) {
        const type = types[i];
        if (['填空题', '多选题'].includes(type)) {
            if (Array.isArray(data[i])) {
                data[i].forEach((item, index) => {
                    data[i][index] = _htmlParser(item);
                });
            }
            continue;
        }
        data[i] = _htmlParser(data[i]);
    }
    return data;
};

const filterQuestion = (question) => {
    question.description = _htmlParser(question.description);
    question.comment = _htmlParser(question.comment);

    const blocks = validateQuestion(question.blocks);
    blocks.stems = _filterStems(blocks.types, blocks.stems);
    blocks.answers = _filterAnswer(blocks.types, blocks.answers);
    blocks.explanations = _filterBasic(blocks.explanations);
    blocks.solutions = _filterBasic(blocks.solutions);
    question.blocks = blocks;

    // 处理外层知识点和叠加要素
    // 获取知识点及核心知识点
    question.knowledges = flattenKnowledges(question.blocks.knowledges);
    question.core_knowledges = flattenKnowledges(question.blocks.core_knowledges);
    // 小题叠加元素合并到答题
    question.elements = flattenElements(question.blocks.elements);
    return question;
};
const filterCategories = (categories) => {
    if (!Array.isArray(categories)) {
        return [];
    }
    categories.forEach(item => {
        item.content = item.content? _htmlParser(item.content) : '';
    });
    return categories;
};
const filterKnowledge = (knowledge) => {
    // 能量储备、基础提升、易错混淆、考前攻略、蓄势待发、完胜关卡
    if (knowledge.card_contents) {
        for (let key in knowledge.card_contents) {
            if (knowledge.card_contents.hasOwnProperty(key)) {
                knowledge.card_contents[key].categories = filterCategories(knowledge.card_contents[key].categories);
            }
        }
    }
    if (knowledge.card_contents_b) {
        for (let key in knowledge.card_contents_b) {
            if (knowledge.card_contents_b.hasOwnProperty(key)) {
                knowledge.card_contents_b[key].categories = filterCategories(knowledge.card_contents_b[key].categories);
            }
        }
    }
    // 认知
    if (knowledge.dimensions && Array.isArray(knowledge.dimensions.categories)) {
        knowledge.dimensions.categories = filterCategories(knowledge.dimensions.categories);
    }
    if (knowledge.dimensions_b && Array.isArray(knowledge.dimensions_b.categories)) {
        knowledge.dimensions_b.categories = filterCategories(knowledge.dimensions_b.categories);
    }
    // 素养
    if (knowledge.cognitions && Array.isArray(knowledge.cognitions.categories)) {
        knowledge.cognitions.categories = filterCategories(knowledge.cognitions.categories);
    }
    if (knowledge.cognitions_b && Array.isArray(knowledge.cognitions_b.categories)) {
        knowledge.cognitions_b.categories = filterCategories(knowledge.cognitions_b.categories);
    }
    // 知识点塑危
    if (knowledge.make_crises) {
        for (let key in knowledge.make_crises) {
            if (knowledge.make_crises.hasOwnProperty(key)) {
                knowledge.make_crises[key].categories = filterCategories(knowledge.make_crises[key].categories);
            }
        }
    }
    if (knowledge.make_crises_b) {
        for (let key in knowledge.make_crises_b) {
            if (knowledge.make_crises_b.hasOwnProperty(key)) {
                knowledge.make_crises_b[key].categories = filterCategories(knowledge.make_crises_b[key].categories);
            }
        }
    }
    return knowledge;
};
module.exports = {
    filterQuestion,
    filterKnowledge
};
