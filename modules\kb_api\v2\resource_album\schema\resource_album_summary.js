const ajv = require('ajv');
const ajvInstance = new ajv({
    removeAdditional: true,
});



const createResourceAlbumSummarySchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        name: { type: 'string' },
        title: { type: 'string' },
        subtitle: { type: 'string' },
        description: { type: 'string' },
        view_times: { type: 'integer' },
        template: { type: 'string' },
        template_style: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    key: {
                        type: 'string'
                    }
                }
            }
        },
        template_data: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    value: { type: 'string' },
                }
            }
        },
        albums: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    title: { type: 'string' },
                    subtitle: { type: 'string' },
                    albums: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                album_id: {
                                    type: 'string'
                                },
                                album_style: {
                                    type: 'object',
                                    properties: {
                                        key: {
                                            type: 'string'
                                        }
                                    }
                                },
                                album_data: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            name: { type: 'string' },
                                            value: { type: 'string' },
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        dbs: {
            type: 'array',
            items: {
                type: 'string'
            }
        },
    }
};

const updateResourceAlbumSummarySchema = {
    type: 'object',
    additionalProperties: false,
    properties: {
        name: { type: 'string' },
        title: { type: 'string' },
        subtitle: { type: 'string' },
        description: { type: 'string' },
        view_times: { type: 'integer' },
        template: { type: 'string' },
        template_style: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    key: {
                        type: 'string'
                    }
                }
            }
        },
        template_data: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    name: { type: 'string' },
                    value: { type: 'string' },
                }
            }
        },
        albums: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    title: { type: 'string' },
                    subtitle: { type: 'string' },
                    albums: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                album_id: {
                                    type: 'string'
                                },
                                album_style: {
                                    type: 'object',
                                    properties: {
                                        key: {
                                            type: 'string'
                                        }
                                    }
                                },
                                album_data: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            name: { type: 'string' },
                                            value: { type: 'string' },
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        dbs: {
            type: 'array',
            items: {
                type: 'string'
            }
        },
        valid: {
            type: 'integer',
            enum: [0, 1]
        }
    }
};




const createResourceAlbumSummaryValidator = ajvInstance.compile(createResourceAlbumSummarySchema);
const updateResourceAlbumSummaryValidator = ajvInstance.compile(updateResourceAlbumSummarySchema);

module.exports = {
    createResourceAlbumSummaryValidator,
    updateResourceAlbumSummaryValidator
};