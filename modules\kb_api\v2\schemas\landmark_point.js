const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const Logger = require('../../../common/utils/logger');

const point = {
    type: 'object',
    additionalProperties: false,
    required: ['id', 'name', 'period', 'subject', 'knowledge_id', 'question_ids'],
    properties: {
        id: {
            type: 'number'
        },
        name: {
            type: 'string'
        },
        period: {
            type: 'string'
        },
        subject: {
            type: 'string'
        },
        knowledge_id: {
            type: 'number',
            default: 0
        },
        question_ids: {
            type: 'array',
            items: {
                type: 'object',
                required: ['original_ques_id', 'similar_ques_ids'],
                properties: {
                    original_ques_id: {
                        type: 'number',
                    },
                    similar_ques_ids: {
                        type: 'array',
                        items: {
                            type: 'number'
                        }
                    }
                }
            },
            default: []
        }
    }
};

const schema = {
    type: 'object',
    additionalProperties: false,
    required: ['landmark_points'],
    properties: {
        landmark_points: {
            type: 'array',
            minItems: 1,
            items: point
        }
    }
};

const checkLandmarkQuesSchema = {
    type: 'object',
    additionalProperties: false,
    required: ['original_questions', 'similar_questions'],
    properties: {
        original_questions: {
            type: 'array'
        },
        similar_questions: {
            type: 'array'
        }
    }
};


const postValidate = (data) => {
    if (!ajv.validate(schema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('标签格式错误');
    }
};

const putValidate = (data) => {
    if (!ajv.validate(schema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('标签格式错误');
    }
};

const checkLandmarkQuesValidate = (data) => {
    if (!ajv.validate(checkLandmarkQuesSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('标签格式错误');
    }
};


module.exports = {
    postValidate,
    putValidate,
    checkLandmarkQuesValidate,
};