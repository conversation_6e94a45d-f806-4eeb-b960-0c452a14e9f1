const exampaper = require('../../modules/kb_api/v2/exampaper');
const appkey = require('../../modules/common/middleware/appkey');
const Router = require('../router');

module.exports = function (rts) {
    const router = new Router(rts, appkey);

    // 根据全局试卷id g_paper_id 获取试卷内容
    router.get('/exampapers/digital_exampaper/:g_paper_id', exampaper.getExampaperByGPaperId);

    // 创建电子试卷，为 kbp 电子试卷服务，其他场景下并不适用
    router.post('/exampapers/digital_exampaper/instances', exampaper.createExampaper);
    router.post('/exampapers/digital_exampaper/instances2', exampaper.createExampaper2);
    router.put('/exampapers/digital_exampaper/instances/:exampaperId', exampaper.updateExampaper);
    router.put('/exampapers/mt_exampaper/instances/:exampaperId', exampaper.updateMtExampaper);
};
