const MongoClient = require("mongodb").MongoClient;
const _ = require('underscore');

const dbUrl = "****************************************************************";

const syncHotTables = async (db) => {
	try {
		let cond = {
			permission: 'public'
        };
        let data = await db.collection('tw_specification').find(cond).sort({view_times: -1}).toArray();
        let obj = {};
        obj = _.groupBy(data, (x) => `${x.period}-${x.subject}-${x.type}-${x.grade}`);
        for (let i in obj) {
            if (obj.hasOwnProperty(i)) {
                obj[i] = obj[i].slice(0, 2);
            }
        }
        obj = _.flatten(_.values(obj));
        await db.collection('hot_table').insertMany(obj);
    } catch (err) {
        console.log(err.stack);
    }
};

const run = async (dbUrl) => {
    try {
      const client = await MongoClient.connect(dbUrl);
      const db = client.db("kb");
      await syncHotTables(db);
      client.close();
    } catch (err) {
        console.log(err.stack);
    }
};

run(dbUrl);