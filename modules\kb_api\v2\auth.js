const Config = require('config');
const crypto = require('crypto');

const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const utils = require('../../common/utils/utils.js');
const params_utils = require('../../common/utils/params.js');
const rediser = require('../../common/utils/rediser');
const cache = rediser.getCache();
const kb_api = require('../config.js').kb_api['v2'];

const SALT = 'cfcca941090922c8482814e49e4e16dc';
const SMOOT_WIN = Config.get('visit_freq.TOKEN_SMOOTH_WINDOW');
const KEY_EXP = Config.get('visit_freq.KEY_EXP');

/*
 * 函数描述:
 * 		认证接口
 * URL: 
 * 		http://kboe.yunxiao.com/kb_api/v2/api_key/
 * Method: 
 * 		GET
 * Author: 
 * 		zhangjun
 * Date:
 * 		2016-08-29
 */
async function api_key(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let params = null;
	let user_tag_key = '';
	let token = ''; // 生成TOKEN
	try {
		params = params_utils.create_params(req, kb_api['api_key']);
		let app_name = params['app_key'].split('_')[1];
		let user_tag = params['user_tag'];
		let t = new Date();
		let content = app_name + '____++++' + user_tag + t.toISOString() + SALT;
		let m = crypto.createHash('md5');
		m.update(content);
		token = m.digest('hex');
		user_tag_key = 'app:' + app_name + ':user:' + user_tag;
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} api_key`,
			what: 'PARAMETERS_ERROR',
			why: typeof e === 'object' && e !== null ? e.message : e,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	
	// update cache
	// 1.
	//    key     : app:appname:user:usertag
	//    value   : token
	try {
		let data = await cache.hgetall(user_tag_key);
		let old_token = data['token'];
		let tokenT = await cache.ttl(old_token);
		if (tokenT > SMOOT_WIN) {
			let result = { 'api_key': old_token, 'duration': tokenT - SMOOT_WIN, };
			return responseWrapper.succ(result);
		}
		rediser.set(token, user_tag_key, KEY_EXP);
		let userT = await cache.ttl(user_tag_key);

		if (userT < KEY_EXP + SMOOT_WIN) {
			// 2.
			//  key     : token
			//  value   : {'m_{0-59}':*, 'h_{0-23}':*, 'd_{1-31}':*}
			let d = {
				'user_tag': user_tag_key,
				'token': token,
				'last_visit_time': utils.get_time_seq(new Date()),
				'day': 0,
				'hour': 0,
				'min': 0,
				'out_of_limits': '',
			};
			cache.hmset(user_tag_key, d);
			cache.expire(user_tag_key, 24 * 3600 + SMOOT_WIN);
		} else {
			cache.hmset(user_tag_key, 'token', token);
		}
		let result = { 'api_key': token, 'duration': KEY_EXP };
		return responseWrapper.succ(result);
	} catch (err) {
		Logger.error(err);
		return responseWrapper.error('HANDLE_ERROR');
	}
}

module.exports = {
	api_key: api_key,
};
