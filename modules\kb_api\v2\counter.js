let ResponseWrapper = require('../../common/utils/response_wrapper');
let mongodber = require('../../common/utils/mongodber');
let db = mongodber.use('KB');
let _ = require('underscore');

const getCounter = async (req, res) => {
    try {
        let responseWrapper = new ResponseWrapper(res);
        let collectName = req.query.collection;
        let retval = await db.collection('counter').findOne({
            _id: collectName
        });
        if (!retval) {
            let data = {
                _id: collectName,
                utime: new Date(),
                seq: 0,
            };
            await db.collection('counter').insertOne(data);
            return responseWrapper.succ(data);
        }
        retval.id = retval._id;
        delete retval._id;
        return responseWrapper.succ(retval);
    } catch (err) {
        return responseWrapper.error('NULL_ERROR');
    }
}

const setCounter = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let collectName = req.query.collection;
        let seq = req.body.count;
        let retval = await db.collection('counter').findAndModify({
            _id: collectName
        }, [], {
            $inc: {
                seq: seq
            },
            $set: {
                utime: new Date()
            }
        }, {
            new: true
        });
        
        if (!retval || !retval.value) {
            throw new Error();
        }
        retval = retval.value;
        retval.id = retval._id;
        delete retval._id;
        return responseWrapper.succ(retval);
    } catch (err) {
        return responseWrapper.error('NULL_ERROR');
    }
}

module.exports = {
    getCounter,
    setCounter,
}