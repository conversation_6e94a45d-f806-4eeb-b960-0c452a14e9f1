const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const params_utils = require('../../common/utils/params.js');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../common/utils/rediser');
const kb_api = require('../config.js').kb_api['v2'];
const _ = require('underscore');
const schoolBookSchema = require('./schemas/school_book');
const Config = require('config');
const axios = require('axios');
const url = require('url');
// 学校教材表
const school_press_version = 'school_press_version';
// 缓存时间
const CACHE_TIME = 60 * 2; //单位 秒

/**
 * 验证更新的数据在原始数据中是否存在学段年级结构数据。
 * @param {*} periods_s  原始数据
 * @param {*} periods_t 更新数据
 * @returns {boolean}
 */
const check_periods_grades = (periods_s, periods_t) => {
    let a_periods = periods_s;
    let b_periods = periods_t;
    if (!Array.isArray(a_periods) || !Array.isArray(b_periods)) {
        return true;
    }
    for (let a_period of a_periods) {
        let p_index = _.indexOf(_.map(b_periods, 'name'), a_period.name);
        if (p_index === -1) {
            return false;
        }
        let b_grades = b_periods[p_index].grades;
        let a_grades = a_period.grades;
        for (let a_grade of a_grades) {
            let g_index = _.indexOf(_.map(b_grades, 'name'), a_grade.name);
            if (g_index === -1) {
                return false;
            }
        }
    }
    return true;
};

/**
 * 查询学校列表数据
 * @param {*} req 
 * @param {*} res 
 */
const get_school_books_schools = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const fun_name = 'get_school_books_schools';
        let params = params_utils.create_params(req, kb_api[fun_name]);
        let cond = {
            invalid: { $in: [null, 0] }
        };
        if (params.query) {
            if (/^[0-9]*$/.test(params.query)) {
                cond['_id'] = parseInt(params.query);
            } else {
                cond['name'] = { '$regex': new RegExp(params.query) };
            }
        }

        let retrun_data = { total_num: 0, schools: [] };

        let result_count = await db.collection(school_press_version).count(cond, { sort: { _id: -1 } });
        retrun_data.total_num = result_count;

        let r_array_data = await db.collection(school_press_version).find(cond)
            .sort({ _id: -1 })
            .limit(params.limit)
            .skip(params.offset)
            .project({ _id: 1, name: 1 })
            .toArray();
        for (let school of r_array_data) {
            retrun_data.schools.push({
                sch_id: school._id,
                sch_name: school.name,
            });
        }
        responseWrapper.succ(retrun_data);
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
};

/**
 * 查询某个学校地区和教材信息
 * @param {*} req 
 * @param {*} res 
 */
const get_school_books_school_books = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let fun_name = 'get_school_books_school_books';
        let params = params_utils.create_params(req, kb_api[fun_name]);
        let hasCache = (params.cache !== 'no');
        // 从缓存获取
        let key = 'kb_api:v2:get_school_books_school_books' + params.sch_id + ':' + process.env.NODE_PORT;
        if (hasCache) {
            let cache = await rediser.get(key);
            if (cache) {
                return responseWrapper.succ(cache);
            }
        }
        let cond = { _id: params.sch_id, invalid: { $in: [null, 0] } };
        let return_data = { school: {}, books: [] };
        let result_data = await db.collection(school_press_version).findOne(cond);
        if (!result_data) {
            return responseWrapper.error('HANDLE_ERROR', '没有查询到数据');
        }
        return_data.school = {
            sch_id: result_data._id,
            sch_name: result_data.name,
            province: result_data.province,
            city: result_data.city,
            country: result_data.country,
        };
        if (result_data.periods == null || !(result_data.periods instanceof Array) || result_data.periods.length === 0) {
            //保存到缓存
            await rediser.set(key, return_data, CACHE_TIME); //单位 秒
            return responseWrapper.succ(return_data);
        }

        return_data.books = _.sortBy(result_data.periods, function (data) {
            if (data) {
                if (data['name'] === '小学')
                    return 1;
                if (data['name'] === '初中')
                    return 2;
                if (data['name'] === '高中')
                    return 3;
            }
        });
        //保存到缓存
        await rediser.set(key, return_data, CACHE_TIME); //单位 秒
        responseWrapper.succ(return_data);
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

/**
 * 设置学校教材区域信息
 * @param {*} req 
 * @param {*} res 
 */
const put_school_books_region = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    try {
        schoolBookSchema.putRegionValidate(body);
        let cond = { _id: parseInt(req.params.sch_id) };
        let data = {
            province: body.province,
            city: body.city,
            country: body.country,
            utime: new Date()
        };
        let update = { $set: data };
        let result = await db.collection(school_press_version).updateOne(cond, update);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            Logger.error(result);
            responseWrapper.error('HANDLE_ERROR', '更新出错');
        }
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * 设置学校教材信息
 * @param {*} req 
 * @param {*} res 
 */
const put_school_books_book = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        const fun_name = 'put_school_books_book';
        let params = params_utils.create_params(req, kb_api[fun_name]);
        let cond = { _id: params.sch_id };
        let r_school_book = await db.collection(school_press_version).findOne(cond);
        if (!r_school_book) {
            return responseWrapper.error('HANDLE_ERROR', '没有查询到数据');
        }
        let periods = params.periods;
        if (!check_periods_grades(r_school_book.periods, periods) || !check_periods_grades(periods, r_school_book.periods)) {
            let logconent = '数据结构错误,学段、年级与原始数据不符';
            Logger.error(logconent);
            return responseWrapper.error('HANDLE_ERROR', logconent);
        }
        //更新数据
        let filter = { _id: params.sch_id };
        let update = { $set: { periods: periods } };
        let result = await db.collection(school_press_version).updateOne(filter, update);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            Logger.error(result);
            responseWrapper.error('更新出错');
        }
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
};
const getBossSchoolInfos = async (id) => {
    try {
        const boss = Config.get('BOSS_SERV');
        let uri = url.format({
            protocol: boss.protocol,
            hostname: boss.hostname,
            port: boss.port,
            pathname: '/external/api/customer/get_by_school_ids',
        });
        let data = {
            'schoolIds': [id],
            'fields': ['schoolSize', 'mingyouSchoolType', 'zoudujisu', 'eduSystem']
        };
        let headers = {
            'Content-Type': 'application/json',
            'apikey': boss.apikey
        };
        return await axios.post(uri, data, { timeout: 10000, headers: headers });
    } catch (error) {
        Logger.error(error);
        return {};
    }
};
/**
 * 添加学校
 * @param {*} req 
 * @param {*} res 
 */
const add_school_books = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    try {
        schoolBookSchema.postValidate(body);
        let cond = { _id: parseInt(body.id) };
        let boss = await getBossSchoolInfos(parseInt(body.id));
        let bossinfo = {};
        if (boss && boss.data && boss.data.data && boss.data.data[String(body.id)]) {
            bossinfo = boss.data.data[String(body.id)];
        }
        let school_level = {
            '全国百强校': 100,
            '省级top10': 80,
            '省级示范型高中': 60,
            '市级示范型高中': 50,
            '初中名优校': 40,
            '普通': 20
        };
        let data = {
            name: body.name,
            province: body.province,
            city: body.city,
            country: body.country,
            periods: body.periods,
            invalid: 0,
            edu_system: bossinfo.eduSystem || '',
            school_size: bossinfo.schoolSize || '',
            famous_type: bossinfo.mingyouSchoolType || '',
            day_boarding: bossinfo.zoudujisu || '',
            famous_type_level: school_level[bossinfo.mingyouSchoolType || ''] || 0,
            utime: new Date(),
            ctime: new Date()
        };
        let update = { $set: data };
        let options = { upsert: true };
        let result = await db.collection(school_press_version).updateOne(cond, update, options);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            Logger.error(result);
            responseWrapper.error('HANDLE_ERROR', '更新出错');
        }
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

// 提交的学段对比旧学段，把已经存在的复制给提交的数据
const getPeriods = (oldPeriods, newPeriods) => {
    if (Array.isArray(oldPeriods) && oldPeriods.length > 0) {
        let periodKeyValue = {};
        let periodArr = [];
        oldPeriods.forEach((item) => {
            periodKeyValue[item.name] = item.grades;
            periodArr.push(item.name);
        });
        newPeriods.forEach((item) => {
            if (periodArr.includes(item.name)) {
                item.grades = periodKeyValue[item.name];
            }
        });
    }
    return newPeriods;
};

/**
 * 更新学校
 * @param {*} req 
 * @param {*} res 
 */
const put_school_books = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    try {
        schoolBookSchema.putValidate(body);
        let cond = { _id: parseInt(req.params.sch_id) };
        let school = await db.collection(school_press_version).findOne(cond);
        let oldPeriods = school.periods;
        let newPeriods = body.periods;
        let periods = getPeriods(oldPeriods, newPeriods);
        let boss = await getBossSchoolInfos(parseInt(req.params.sch_id));
        let bossinfo = {};
        if (boss && boss.data && boss.data.data && boss.data.data[String(req.params.sch_id)]) {
            bossinfo = boss.data.data[String(req.params.sch_id)];
        }
        let school_level = {
            '全国百强校': 100,
            '省级top10': 80,
            '省级示范型高中': 60,
            '市级示范型高中': 50,
            '初中名优校': 40,
            '普通': 20
        };
        let data = {
            name: body.name,
            province: body.province,
            city: body.city,
            country: body.country,
            periods: periods,
            edu_system: bossinfo.eduSystem || '',
            school_size: bossinfo.schoolSize || '',
            famous_type: bossinfo.mingyouSchoolType || '',
            day_boarding: bossinfo.zoudujisu || '',
            famous_type_level: school_level[bossinfo.mingyouSchoolType || ''] || 0,
            utime: new Date()
        };
        let update = { $set: data };
        let options = { upsert: true };
        let result = await db.collection(school_press_version).updateOne(cond, update, options);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            Logger.error(result);
            responseWrapper.error('HANDLE_ERROR', '更新出错');
        }
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * 删除学校
 * @param {*} req 
 * @param {*} res 
 */
const delete_school_books = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let sch_id = req.params.sch_id;
    try {
        let cond = { _id: parseInt(sch_id) };
        let update = { $set: { invalid: 1, utime: new Date() } };
        let result = await db.collection(school_press_version).updateOne(cond, update);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            Logger.error(result);
            responseWrapper.error('HANDLE_ERROR', '删除出错');
        }
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getRegionSchoolsCond = (query) => {
    let { province, city, country, sch_name } = query;
    let cond = {
        invalid: { $in: [null, 0] }
    };
    if (province) {
        cond.province = province;
    }
    if (city) {
        cond.city = city;
    }
    if (country) {
        cond.country = country;
    }
    if (sch_name) {
        cond.name = { '$regex': new RegExp(sch_name) };
    }
    return cond;
};
/**
 * 查询区域学校列表接口, 通过区域，学校名称
 * @param {*} req 
 * @param {*} res 
 */
const getRegionSchools = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let limit = parseInt(req.query.limit) || 10;
    let offset = parseInt(req.query.offset) || 0;
    try {
        let cond = getRegionSchoolsCond(req.query);
        let project = {
            name: 1, province: 1, city: 1, country: 1, day_boarding: 1,
            edu_system: 1, school_size: 1, famous_type: 1
        };

        let total = await db.collection(school_press_version).count(cond);
        let countryTotal = total; // 按照全部省市县条件查询
        if (total === 0) { // 按照省市县查询无数据，则按照省市查
            delete cond.country;
            total = await db.collection(school_press_version).count(cond);
        }
        let result = await db.collection(school_press_version).find(cond)
            .sort({ 'famous_type_level': -1 })
            .limit(limit)
            .skip(offset)
            .project(project)
            .toArray();
        for (let school of result) {
            school.id = school._id;
            delete school._id;
        }
        responseWrapper.succ({ country_total: countryTotal, total: total, list: result });
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
};

/**
 * 查询学校简介
 * @param {*} req 
 * @param {*} res 
 */
const getSchoolBrief = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let cond = { _id: parseInt(req.params.sch_id) };
        let options = { fields: { sch_brief: 1 } };
        let result = await db.collection(school_press_version).findOne(cond, options);
        if (result) {
            responseWrapper.succ({ sch_brief: result.sch_brief });
        } else {
            Logger.error(result);
            responseWrapper.error('HANDLE_ERROR', '查询出错');
        }
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * 更新学校简介
 * @param {*} req 
 * @param {*} res 
 */
const updateSchoolBrief = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    try {
        let cond = { _id: parseInt(req.params.sch_id) };
        let data = {
            sch_brief: body.sch_brief,
            utime: new Date()
        };
        let update = { $set: data };
        let result = await db.collection(school_press_version).updateOne(cond, update);
        if (result.result.ok === 1) {
            responseWrapper.succ({});
        } else {
            Logger.error(result);
            responseWrapper.error('HANDLE_ERROR', '更新出错');
        }
    } catch (err) {
        Logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * 获取学校信息
 * @param {object} req 
 * @param {object} res 
 */
const getSchoolsByIdAndName = async (req, res) => {
    let resWrapper = new ResponseWrapper(res);
    let limit = Number(req.query.limit || 10);
    let offset = Number(req.query.offset || 0);
    let school = req.query.school;
    let cond = {};
    if (school) {
        school = parseInt(req.query.school);
        if (_.isNaN(school)) {
            cond.name = new RegExp(req.query.school);
        } else {
            cond._id = school;
        }
    }
    try {
        let result = await db.collection('school_press_version').find(cond).sort({_id: -1}).limit(limit).skip(offset).project({ _id: 1, name: 1, ctime: 1, utime: 1 }).toArray();
        for (const value of result) {
            if (value) {
                value.id = value._id;
                delete value._id;
            }
        }
        let total_num = await db.collection('school_press_version').find(cond).count();
        resWrapper.succ({ total_num: total_num, list: result });
    } catch (err) {
        Logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    get_school_books_schools,
    get_school_books_school_books,
    put_school_books_region,
    put_school_books_book,

    add_school_books,
    put_school_books,
    delete_school_books,

    getRegionSchools,
    getSchoolBrief,
    updateSchoolBrief,

    getSchoolsByIdAndName: getSchoolsByIdAndName,
};