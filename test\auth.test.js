/**
 * 接口测试
 */
/* eslint-disable */
const URL = require('url');
const qs = require('querystring');
const assert = require('assert');
const request = require('superagent');

const localEnv = {
    host: '127.0.0.1',
    port: 9500,
    app_key: 'iyunxiao_kbplat180305',
};

describe('认证测试', function () {
    this.timeout(90000);
    it('/kb_api/v2/api_key', async function () {
        let pathname = `/kb_api/v2/api_key`;
        let url = URL.format({
            protocol: 'http',
            hostname: localEnv.host,
            port: localEnv.port,
            pathname: pathname,
            search: qs.stringify({
                api_key: localEnv.app_key
            })
        });
        let data = {
            app_key: 'app_tiku',
            user_tag: 'wujinfeng'
        }
        let result = await request.get(url).query(data);
        console.log(result.body);
        assert.ok(result.body.api_key);
        assert.ok(result.body.duration > 0);
    });
});