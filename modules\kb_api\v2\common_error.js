const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const mongodber = require('../../common/utils/mongodber');
const logger = require('../../common/utils/logger');
const db = mongodber.use('KB');
const defsColl = db.collection('common_error_def');
const recommendColl = db.collection('common_error_recommend');
const ObjectID = require('mongodb').ObjectID;
const PERIODS = ['高中', '初中'];
const SUBJECTS = ['语文', '数学', '英语', '物理', '化学', '生物', '政治', '历史', '地理'];
const TYPES = ['同步', '中考', '高考'];

const mapRate = function (data) {
    let rateObj = {
        a_standard: 0,
        b_upper: 0,
        b_standard: 0,
        c_upper: 0,
        c_standard: 0,
        d_upper: 0,
        d_standard: 0,
        e_upper: 0,
        e_standard: 0
    };
    if (Array.isArray(data)) {
        for (let i = 0, l = data.length; i < l; i++) {
            if (!isNaN(Number(rateObj[data[i].name]))) {
                rateObj[data[i].name] = data[i].rate;
            }
        }
    }
    return rateObj;
};

// 对外提供-共性错题定义
const getDefsConfig = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let period = req.query.period;
    let type = req.query.type;
    let subject = req.query.subject;
    let cond = {};
    if (period) {
        if (!PERIODS.includes(period)) {
            return responseWrapper.error('PARAMETERS_ERROR', '学段不存在');
        }
        cond['period'] = period;
    }
    if (subject) {
        if (!SUBJECTS.includes(subject)) {
            return responseWrapper.error('PARAMETERS_ERROR', '学科不存在');
        }
        cond['subject'] = subject;
    }
    if (type) {
        if (!TYPES.includes(type)) {
            return responseWrapper.error('PARAMETERS_ERROR', '类型不存在');
        }
        cond['type'] = type;
    }
    try {
        let results = await defsColl.find(cond).toArray();
        for (let result of results) {
            result['id'] = result._id.toString();
            result['rates'] = mapRate(result['rates']);
            delete result._id;
            delete result.ctime;
            delete result.utime;
        }
        res.resMode = 'normal';
        return responseWrapper.succ(results);
    } catch (e) {
        Logger.error(e);
		return responseWrapper.error('HANDLE_ERROR', e);
    }
};

// 对外提供-共性错题配置
const getRecommendConfig = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let period = req.query.period;
    let type = req.query.type;
    let subject = req.query.subject;
    let cond = {};
    if (period) {
        if (!PERIODS.includes(period)) {
            return responseWrapper.error('PARAMETERS_ERROR', '学段不存在');
        }
        cond['period'] = period;
    }
    if (subject) {
        if (!SUBJECTS.includes(subject)) {
            return responseWrapper.error('PARAMETERS_ERROR', '学科不存在');
        }
        cond['subject'] = subject;
    }
    if (type) {
        if (!TYPES.includes(type)) {
            return responseWrapper.error('PARAMETERS_ERROR', '类型不存在');
        }
        cond['type'] = type;
    }
    try {
        let results = await recommendColl.find(cond).toArray();
        for (let result of results) {
            result['id'] = result._id.toString();
            delete result._id;
        }
        res.resMode = 'normal';
        return responseWrapper.succ(results);
    } catch (e) {
        Logger.error(e);
		return responseWrapper.error('HANDLE_ERROR', e);
    } 
};


// 查询共性错题定义
const getDefs = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let period = req.query.period;
    let type = req.query.type;
    let subject = req.query.subject;
    if (!period || !type) {
        return responseWrapper.error('PARAMETERS_ERROR', 'period和type必传');
    }
    let cond = {period: period, type: type};
    if (subject) {
        if (!SUBJECTS.includes(subject)) {
            return responseWrapper.error('PARAMETERS_ERROR', '学科不存在');
        }
        cond['subject'] = subject;
    }
    try {
        let results = await defsColl.find(cond).toArray();
        for (let result of results) {
            result['id'] = result._id.toString();
            delete result._id;
            delete result.ctime;
            delete result.utime;
        }
        return responseWrapper.succ(results);
    } catch (e) {
        Logger.error(e);
		return responseWrapper.error('HANDLE_ERROR', e);
    }
};


// 根据id查询共性错题定义
const getDefById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res); 
    let defId = req.params.def_id;
    if (!defId) {
        return responseWrapper.error('PARAMETERS_ERROR', '缺少参数');
    }
    try {
        let result = await defsColl.findOne({_id: ObjectID(defId)});
        result['id'] = result._id.toString();
        delete result._id;
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e);
    }
};


// 共性错题定义更新kb库
const updateDef = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let defId = req.params.def_id;
    if (!defId) return responseWrapper.error('PARAMETERS_ERROR', '缺少参数');
    let data = req.body;
    if (!data) throw Error('请传入内容');
    data['utime'] = new Date();
    try {
        await defsColl.update({_id: ObjectID(defId)}, {$set: data});
        return responseWrapper.succ({});
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e);
    }
};


// 查询共性错题推题
const getRecommend = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let period = req.query.period;
    let type = req.query.type;
    let subject = req.query.subject;
    if (!period || !type) {
        return responseWrapper.error('PARAMETERS_ERROR', 'period和type必传');
    }
    let cond = {period: period, type: type};
    if (subject) {
        if (!SUBJECTS.includes(subject)) {
            return responseWrapper.error('PARAMETERS_ERROR', '学科不存在');
        }
        cond['subject'] = subject;
    }
    try {
        let results = await recommendColl.find(cond).toArray();
        for (let result of results) {
            result['id'] = result._id.toString();
            delete result._id;
        }
        return responseWrapper.succ(results);
    } catch (e) {
        Logger.error(e);
		return responseWrapper.error('HANDLE_ERROR', e);
    } 
};


// 根据id查询共性错题推题
const getRecommendById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res); 
    let recommendId = req.params.recommend_id;
    if (!recommendId) {
        return responseWrapper.error('PARAMETERS_ERROR', '缺少参数');
    }
    try {
        let result = await recommendColl.findOne({_id: ObjectID(recommendId)});
        result['id'] = result._id.toString();
        delete result._id;
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e);
    }
};

// 共性错题推题更新kb库
const updateRecommend = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let recommendId = req.params.recommend_id;
    if (!recommendId) return responseWrapper.error('PARAMETERS_ERROR', '缺少参数');
    let data = req.body;
    if (!data) throw Error('请传入内容');
    data['utime'] = new Date();
    try {
        await recommendColl.update({_id: ObjectID(recommendId)}, {$set: data});
        return responseWrapper.succ({});
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e);
    }
};


module.exports = {
    getDefsConfig,
    getRecommendConfig,
    getDefs,
    getDefById,
    updateDef,
    getRecommend,
    getRecommendById,
    updateRecommend
};