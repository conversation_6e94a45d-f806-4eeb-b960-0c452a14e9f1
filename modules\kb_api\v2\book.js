const Thenjs = require('thenjs');
const assert = require('assert');
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const params_utils = require('../../common/utils/params.js');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../common/utils/rediser');
let kb_api = require('../config.js').kb_api['v2'];
let config = require('config');
let loosen_tree = require('./utils.js').loosen_tree;
let homework = require('./homework.js');
let _ = require('underscore');
const counter = require('../../common/utils/counter');
let EngBook = require('./eng_book');
//const createExampaperCategory = require('./exampaper').createCategory;
/*
 * 函数描述:
 * 		在 catalog 中过滤掉指定的 period 和 subject 以外的数据。
 * return * 		过滤之后的 catalog 对象
 */
function filterCatalog(catalog, period, subject) {
	if (period) {
		catalog.periods = _.filter(catalog.periods, function (periodCatalog) {
			return periodCatalog.name === period;
		});
	}
	if (subject) {
		_.each(catalog.periods, function (periodCatalog) {
			periodCatalog.subjects = _.filter(periodCatalog.subjects, function (subjectCatalog) {
				return subjectCatalog.name === subject;
			});
		});
	}
	return catalog;
}

function filterInvalidEnglishBook(catalog, englistBookList) {
	if (!catalog.hasOwnProperty('book')) return;
	catalog.book.children.forEach(function (period) {
		if (!period.hasOwnProperty('children')) return;
		period.children.forEach(function (subject) {
			if (!subject.hasOwnProperty('children')) return;
			subject.children.forEach(function (press_version) {
				if (!press_version.hasOwnProperty('children')) return;
				let gradeArray = [];
				press_version.children.forEach(function (grade) {
					englistBookList.forEach(function (englishBook) {
						if (englishBook._id === grade.id) {
							gradeArray.push(grade);
						}
					});
				});
				press_version.children = gradeArray;
			});
		});
	});
}
/*
 * 函数描述:
 * 		请求教材信息（打包信息）
 * URL:
 * 		http://kboe.gotiku.com/kb_api/v2/books/
 * Method:
 * 		GET
 * Author:
 * 		zhangjun
 * Date:
 * 		2016-08-18
 */
function books(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	if (req.query.type === 'answer')
		return homework.getBooksCategory(req, res);
	let params = null;
	try {
		let fun_name = arguments.callee.name;
		params = params_utils.create_params(req, kb_api[fun_name]);
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} books`,
			what: 'PARAMETERS_ERROR',
			why: e,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	let cache_key = params['cache_key'] + ':' + params['device'] + process.env.NODE_PORT;
	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		// 查询 Redis 中是否有相应的数据
		try {
			if (config.get('kb_plat.appkey').indexOf(req.query.api_key) >= 0) {
				cache_key = cache_key + ':' + req.query.api_key;
			}
		} catch (err) {

		}
		rediser.get(cache_key, function (err, items) {
			if (items) {
				return cont(null, items);
			}
			cont(null, null);
		});
	}).then(function (cont, result) {
		// 若 result 中已经有数据，则直接跳过
		if (result) {
			return cont(null, result);
		}
		// 查询 catalog 数据库
		let coll = db.collection('catalog');
		let cond = { _id: 'filtered_book' };

		// 查询
		coll.findOne(cond, function (err, catalog) {
			if (err) {
				return cont(err);
			}
			if (!catalog) {
				return cont(null, null);
			}
			try {
				try {
					// 过滤 unique_subjects 中的学科
					if (config.get('kb_plat.appkey').indexOf(req.query.api_key) < 0) {
						let subjects = config.get('kb_plat.unique_subjects');
						for (let ix in catalog.periods) {
							let period = catalog.periods[ix];
							period.subjects = _.filter(period.subjects, function (x) {
								return subjects.indexOf(x.name) < 0;
							});
						}
					}
				} catch (err) { }
				// 过滤 period 和 subject 之外的数据
				catalog = filterCatalog(catalog, params['period'], params['subject']);
				let keys = {
					'periods': 'period',
					'subjects': 'subject',
					'press_versions': 'press_version',
					'grades': 'grade',
				};
				// 把 periods 字段转换成 book 字段
				catalog['book'] = { 'periods': catalog.periods };
				delete catalog['periods'];
				// 处理 book 字段中的数据
				loosen_tree(catalog['book'], keys);
			} catch (e) {
				return cont(e);
			}
			// 如果是获取英语口语教材，则在此处理 books
			if (params['device'] === 'mobile' && params['subject'] === '英语') {
				EngBook.getEnglishBooksToCatalog(function (err, engBook) {
					if (!err && engBook.length > 0) {
						filterInvalidEnglishBook(catalog, engBook);
					}
					return cont(null, catalog);
				});
			} else {
				return cont(null, catalog);
			}
		});
	}).then(function (cont, result) {
		if (result) {
			//过滤掉“初中”-“科学”
			// if (result.book && result.book.children) {
			// 	let childrens = result.book.children;
			// 	for (let child of childrens) {
			// 		if (child.name === '初中') {
			// 			for (let i = 0; i < child.children.length; i++)
			// 				if (child.children[i].name === '科学') {
			// 					child.children.splice(i, 1);
			// 					break;
			// 				}
			// 		}
			// 	}
			// }
			// 更新 Redis 数据
			rediser.set(cache_key, result, 60 * 30);


			// 过滤掉 不应该展示的 教材
			const queryDbs = new Set((req.query.dbs || 'basic').split(','));
			result.book.children = (result.book.children || []).filter(period => {
				period.children = (period.children || []).filter(subject => {
					subject.children = (subject.children || []).filter(pressVersion => {
						pressVersion.children = (pressVersion.children || []).filter(grade => {
							return (grade.dbs || ['basic']).some(item => queryDbs.has(item));
						});
						return pressVersion.children.length > 0;
					});
					return subject.children.length > 0;
				});
				return period.children.length > 0;
			})
			if (result.book.children.length > 0) {
				return responseWrapper.succ(result);
			}
		}
		return responseWrapper.error('NULL_ERROR');

	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
// 提取出持久化结构中章节的id,支持知识树（key:nodes）和教材(key:chapters)
function _getChapterIdsInBook(key, bookChapters, ids) {
	_.each(bookChapters, function (bookChapter) {
		ids.push(Number(bookChapter.id));
		if (!bookChapter.hasOwnProperty(key)) {
			return;
		}
		_getChapterIdsInBook(key, bookChapter[key], ids);
	});
}
function _assignChapter(type, bookLevelChapter, chapterContents) {
	for (let index in bookLevelChapter) {
		var curLevelChapterItem = bookLevelChapter[index];
		let findItem = _.find(chapterContents, function (chapter) {
			return chapter._id === curLevelChapterItem.id;
		});
		// delete curLevelChapterItem.id;
		if (type === 'book') {
			if (curLevelChapterItem.hasOwnProperty('chapters')) {
				if (findItem) {
					curLevelChapterItem['class'] = findItem['class'];
					curLevelChapterItem['duration'] = findItem['duration'];
					curLevelChapterItem['id'] = findItem['_id'];
				}
				_assignChapter(type, curLevelChapterItem.chapters, chapterContents);
			} else {
				if (findItem) {
					bookLevelChapter[index] = findItem;
					bookLevelChapter[index].id = findItem['_id'];
				}
			}
		} else if (type === 'knowledge_tree') {
			if (curLevelChapterItem.hasOwnProperty('nodes')) {
				_assignChapter(type, curLevelChapterItem.nodes, chapterContents);
			} else {
				if (findItem) {
					bookLevelChapter[index] = findItem;
					bookLevelChapter[index].id = findItem['_id'];
				}
			}
		}
	}
}
//将章节信息填充到教材 type 可选值 book knowledge_tree
function _assignChaptersToBook(type, book, chapters) {
	if (chapters.length === 0) {
		return book;
	}
	if (type === 'book') {
		_assignChapter(type, book.chapters, chapters);
	} else if (type === 'knowledge_tree') {
		_assignChapter(type, book.nodes, chapters);
	}
	_.each(chapters, function (chapter) {
		delete chapter._id;
	});
	return book;
}
function _fillKnowledgeStage(chapters, callback) {
	let kIds = [];
	chapters.forEach(function (chapter) {
		if (chapter.hasOwnProperty('knowledges')) {
			chapter.knowledges.forEach(function (knowledge) {
				kIds.push(knowledge.id);
			});
		}
	});
	let proj = { _id: 1, stages: 1, pre_knowledges: 1, related_knowledges: 1 };
	db.collection('knowledge').find({ _id: { $in: kIds } }).project(proj).toArray(function (err, stageInfos) {
		if (err) {
			return callback(err);
		}
		try {
			chapters.forEach(function (chapter) {
				if (chapter.hasOwnProperty('knowledges')) {
					chapter.knowledges.forEach(function (knowledge) {
						let findInfo = _.find(stageInfos, function (info) {
							return info._id === knowledge.id;
						});
						if (findInfo) {
							delete findInfo['_id'];
							if (findInfo['stages']) {
								knowledge['stages'] = findInfo['stages'];
							}
							if (findInfo['pre_knowledges']) {
								knowledge['pre_knowledges'] = findInfo['pre_knowledges'];
							}
							if (findInfo['related_knowledges']) {
								knowledge['related_knowledges'] = findInfo['related_knowledges'];
							}
						}
					});
				}
			});
		} catch (err) {
			return callback(err);
		}
		return callback(null, chapters);
	});
}
/*
 * 根据book_id获取教材结构以及知识章节、知识点信息
 *
 */
function _get_book(params, callback) {
	// 判断 params 中的 fields_type 是否为 'eng_book' 然后选择相应的数据库进行查询 book 的详细数据
	let dbName = {
		'book': 'book',
		'chapter': 'book_chapter'
	};
	if (params['fields_type'] === 'eng_book') {
		dbName['book'] = 'eng_book';
		dbName['chapter'] = 'eng_book_chapter';
	}
	Thenjs(function (cont) {
		let coll = db.collection(dbName['book']);
		let cond = { _id: params['book_id'] };
		let proj = { has_modified: 0 };
		coll.findOne(cond, proj, function (err, book) {
			if (err) {
				return cont(err);
			}
			if (!book) {
				return cont('没有此教材' + JSON.stringify(params));
			}
			cont(null, book);
		});
	}).then(function (cont, book) {
		let chapterIds = [];
		_getChapterIdsInBook('chapters', book.chapters, chapterIds);
		let proj = { ctime: 0, utime: 0, path: 0 };
		db.collection(dbName['chapter']).find({ _id: { $in: chapterIds } }).project(proj).toArray(function (err, chapters) {
			if (err) {
				return cont(err);
			}
			return cont(null, book, chapters);
		});
	}).then(function (cont, book, chapters) {
		if (params['type'] === 'plan') {
			_fillKnowledgeStage(chapters, function (err, chapters) {
				if (err) {
					return cont(err);
				}
				return cont(null, book, chapters);
			});
		} else {
			return cont(null, book, chapters);
		}
	}).then(function (cont, book, chapters) {
		book = _assignChaptersToBook('book', book, chapters);
		book['id'] = book['_id'];
		delete book['_id'];
		book['book'] = {
			'chapters': book.chapters,
		};
		delete book['chapters'];
		let cache_key = params['cache_key'];
		rediser.set(cache_key, book, 60 * 30);
		return callback(null, book);
	}).fail(function (cont, error) {
		return callback(error);
	}).finally(function (cont, error) {
		return callback(error);
	});
}
/*
 * Desc:
 *      请求某教材下所有章节挂载的资源
 * URL:
 *      http://kboe.yunxiao.com/kb_api/v2/books/{book_id}/
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-18
*/
function book(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let params = null, cache_key = null;
	try {
		let fun_name = arguments.callee.name;
		params = params_utils.create_params(req, kb_api[fun_name]);
		if (req.query.fields_type === 'eng_book') {
			params['cache_key'] = [params['cache_key'], 'eng_book'].join(':');
		}
		cache_key = params['cache_key'];
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} book`,
			what: 'PARAMETERS_ERROR',
			why: e,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	Thenjs(function (cont) {
		rediser.get(cache_key, function (err, item) {
			if (item) {
				cont(null, item);
			} else {
				_get_book(params, function (err, book) {
					if (err) {
						return cont(err);
					}
					return cont(null, book);
				});
			}
		});
	}).then((cont, book) => {
		const queryDbs = new Set((req.query.dbs || 'basic').split(','));
		const bookDbs = book.dbs || ['basic'];
		// 按照权限进行过滤
		// 1. 如果没有权限，则返回空
		// 2. 如果未传参，则只返回基本数据，即book中dbs为空或者包含basic
		if (bookDbs.some(item => queryDbs.has(item))) {
			return cont(null, book);
		} else {
			cont('没有此教材' + JSON.stringify(params));
		}
	}).then(function (cont, book) {
		let keys = {
			'chapters': 'chapter',
			'knowledges': 'knowledge',
			'eng_words': 'eng_word',
			'eng_sentences': 'eng_sentence',
			'eng_dialogs': 'eng_dialog',
		}, excludes = [];
		if (params.fields_type === 'knowledge') {
			excludes = ['eng_words', 'eng_sentences', 'eng_dialogs'];
		}
		loosen_tree(book['book'], keys, excludes);
		cont(null, book);
	}).then(function (cont, result) {
		if (result) {
			return responseWrapper.succ(result);
		}
		return responseWrapper.error('NULL_ERROR');

	}).fail(function (cont, error) {
		Logger.error(error);
		if (typeof error === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
/*
 * 函数描述:
 * 		修改教材内容
 * URL:
 * 		http://kboe.yunxiao.com/kb_api/v2/books/{book_id}/
 * Method:
 * 		PUT
 * Author:
 * 		zhangjun
 * Date:
 * 		2016-11-14
 */
// 检测最底层的key，将带有knowledge的章节 key由 chapter 变成 chapter_knowledge
function changeKeyInBookStruct(bookChapters, chapterInfos) {
	_.each(bookChapters, function (bookChapter) {
		if (!bookChapter.hasOwnProperty('children')) {
			let findChapterInfo = _.find(chapterInfos, function (chapterInfo) {
				return chapterInfo.id === bookChapter.id;
			});
			if (findChapterInfo && findChapterInfo.hasOwnProperty('knowledges')) {
				bookChapter.key = 'chapter_knowledge';
			}
			return;
		}
		changeKeyInBookStruct(bookChapter.children, chapterInfos);
	});
}
//获取book的结构
function book_struct(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	try {
		var book_id = parseInt(req.params.book_id);
		var origin = req.query.origin;
		assert(book_id);
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} book_struct`,
			what: 'PARAMETERS_ERROR',
			why: e.message,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		let coll = db.collection('book');
		// 查询条件
		let cond = { '_id': book_id };
		coll.findOne(cond, function (err, book) {
			if (err) {
				return cont(err);
			}
			if (!book) {
				return responseWrapper.error('NULL_ERROR', '查不到该条记录');
			}
			if (origin) {
				return responseWrapper.succ(book);
			}
			book['id'] = book['_id'];
			delete book['_id'];
			let chapterIds = [];
			_getChapterIdsInBook('chapters', book.chapters, chapterIds);
			book['book'] = {
				'chapters': book.chapters,
			};
			delete book['chapters'];
			let keys = {
				'chapters': 'chapter'
			};
			loosen_tree(book['book'], keys);
			//return responseWrapper.succ(book);
			return cont(null, book, chapterIds);
		});
	}).then(function (cont, book, chapterIds) {
		let coll = db.collection('book_chapter');
		// 查询条件
		let cond = { '_id': { $in: chapterIds } };
		coll.find(cond).toArray(function (err, chapterInfos) {
			if (err) {
				return cont(err);
			}
			chapterInfos.forEach(function (chapterInfo) {
				chapterInfo['id'] = chapterInfo['_id'];
				delete chapterInfo['_id'];
			});
			return cont(null, book, chapterInfos);
		});
	}).then(function (cont, book, chapterInfos) {
		changeKeyInBookStruct(book.book.children, chapterInfos);
		return responseWrapper.succ(book);
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
const bookStructKeys = ['key', 'name', 'id', 'children'];
// 将前端传回的book struct还原成shema 结构
function convert2DBSchema(bookLevel, chapterArray, newChapterArray, path, isLegal) {
	_.each(bookLevel, function (elem) {
		let elemKeys = Object.keys(elem);
		let diff = _.difference(elemKeys, bookStructKeys);
		if (diff.length) {
			for (let index in diff) {
				delete elem[diff[index]];
			}
		}
		delete elem['key'];
		if (elem.hasOwnProperty('name') && elem.hasOwnProperty('id')) {
			elem.name = elem.name.trim();
			var curPath = path ? path + '_' + elem.name : elem.name + '';
			var cur = { 'id': elem.id, 'name': elem.name, 'path': curPath };
			chapterArray.push(cur);
		} else {
			elem.name = elem.name.trim();
			let elemPath = path + '_' + elem.name;
			var cur = { 'name': elem.name, 'path': elemPath };
			newChapterArray.push(cur);
		}
		if (elem.hasOwnProperty('children')) {
			if (elem.children.length) {
				elem['chapters'] = elem.children;
				delete elem.children;
				var curPath = path ? path + '_' + elem.name : elem.name + '';
				convert2DBSchema(elem.chapters, chapterArray, newChapterArray, curPath, isLegal);
			} else {
				delete elem.children;
			}
		}
	});
}
// 将新建的chapterid 填充到教材结构
function fillNewChapterIds(bookLevel, id2NameArray) {
	_.each(bookLevel, function (elem) {
		if (elem.hasOwnProperty('name') && !elem.hasOwnProperty('id')) {

			let id = 0;
			for (let i = 0; i < id2NameArray.length; ++i) {
				if (id2NameArray[i] && (id2NameArray[i].name === elem.name)) {
					id = id2NameArray[i].id;
					delete id2NameArray[i];
					break;
				}
			}
			if (id) {
				elem.id = id;
			}
		}
		if (elem.hasOwnProperty('chapters')) {
			fillNewChapterIds(elem.chapters, id2NameArray);
		}
	});
}

function updateBookChapters(changeChapterArray, callback) {
	Thenjs.each(changeChapterArray, function (cont, chapter) {
		let coll = db.collection('book_chapter');
		// 查询条件
		let cond = { '_id': chapter.id };
		let update_fields = { $set: { 'name': chapter.name, 'path': chapter.path } };
		update_fields['$set']['utime'] = new Date();
		// 更新相关book_chapter
		coll.updateOne(cond, update_fields, function (err, result) {
			if (err) {
				Logger.error(err);
				return cont(null, null);
			}
			return cont(null, 'succ');
		});
	}).then(function (cont, queue) {
		queue = _.filter(queue, function (item) {
			return item === 'succ';
		});
		if (queue.length === changeChapterArray.length) {
			return callback(null);
		}
		return callback('部分失败');

	}).fail(function (cont, error) {
		Logger.error(error);
		return callback(error);
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback(error);
	});
}
function insertBookChapters(newChapterArray, callback) {
	let newChapterIds = [];
	Thenjs.each(newChapterArray, function (cont, chapter) {
		counter.getNextSequenceValue('book_chapter', 1, function (err, seq) {
			if (err) {
				Logger.error(err);
				return cont(null, null);
			}
			let fields = {};
			fields['_id'] = seq[0];
			fields['name'] = chapter.name;
			fields['path'] = chapter.path;
			fields['class'] = 0;
			fields['duration'] = 0.0;
			fields['utime'] = new Date();
			fields['ctime'] = new Date();
			// 更新
			let coll = db.collection('book_chapter');
			// book_chapter
			coll.insertOne(fields, function (err, result) {

				if (err) {
					Logger.error(err);
					return cont(null, null);
				}
				return cont(null, { 'id': fields['_id'], 'name': fields['name'] });
			});
		});

	}).then(function (cont, queue) {
		queue = _.filter(queue, function (item) {
			return item !== null;
		});
		if (queue.length === newChapterArray.length) {
			return callback(null, queue);
		}
		return callback('部分失败');

	}).fail(function (cont, error) {
		Logger.error(error);
		return callback(error);
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback(error);
	});
}
//修改book的结构
// function put_book_struct(req, res) {
// 	var responseWrapper = new ResponseWrapper(res);
// 	var rootPath = '';
// 	var cacheKeyPre = '';
// 	var period;
// 	var subject;
// 	var press_version;
// 	var grade;
// 	try {
// 		var book_id = parseInt(req.params.book_id);
// 		assert(book_id);
// 		cacheKeyPre = 'kb_api:v2:books:' + book_id + '*';
// 		period = req.body.period;
// 		subject = req.body.subject;
// 		press_version = req.body.press_version;
// 		grade = req.body.grade;
// 	} catch (e) {
// 		return responseWrapper.error('PARAMETERS_ERROR', e.message);
// 	}
// 	// 数据查询 ----------------------------
// 	Thenjs(function (cont) {
// 		var update_fields = {};
// 		if (period) {
// 			update_fields['period'] = period;
// 		}
// 		if (subject) {
// 			update_fields['subject'] = subject;
// 		}
// 		if (press_version) {
// 			update_fields['press_version'] = press_version;
// 		}
// 		if (grade) {
// 			update_fields['grade'] = grade;
// 		}
// 		if (JSON.stringify(update_fields) !== '{}') {

// 			var cond = { '_id': book_id };
// 			// 检测id 是否合法
// 			db.collection('book').findOne(cond, function (err, book) {
// 				if (err) {
// 					return cont(err);
// 				}
// 				let toPeriod = period ? period : book.period;
// 				let toSubject = subject ? subject : book.subject;
// 				let toPress_version = press_version ? press_version : book.press_version;
// 				let toGrade = grade ? grade : book.grade;
// 				checkBookConflict(book._id, toPeriod, toSubject, toPress_version, toGrade, function(err){
// 					if (err) {
// 						return cont(err);
// 					}
// 					//通过检查可以更新
// 					update_fields['has_modified'] = 1;
// 					update_fields['utime'] = new Date();
// 					var coll = db.collection('book');
// 					// 查询条件
// 					var cond = { '_id': book_id };
// 					// 检测id 是否合法
// 					coll.updateOne(cond, { $set: update_fields }, function (err, result) {
// 						if (err) {
// 							return cont(err);
// 						}
// 						let cacheKeyPre = 'kb_api:v2:books:' + book_id + '*';
// 						rediser.getCache().keys(cacheKeyPre, function(err, curkeys){
// 							if (err) {
// 								Logger.error(err);
// 							}
// 							rediser.del( curkeys);
// 						});
// 						let booksKey = 'kb_api:v2:books:undefined:undefined:' + process.env.NODE_PORT;
// 						rediser.del(booksKey);

// 						return cont(null)
// 					});
// 				});
// 			});
// 		} else {
// 			return cont(null);
// 		}
// 	}).then(function (cont) {
// 		var coll = db.collection('book');
// 		// 查询条件
// 		var cond = { '_id': book_id };
// 		// 检测id 是否合法
// 		coll.findOne(cond, function (err, book) {
// 			if (err) {
// 				return cont(err);
// 			}
// 			if (!book) {
// 				return responseWrapper.error('PARAMETERS_ERROR', '查不到该条记录');
// 			}
// 			rootPath = [book.period, book.subject, book.press_version, book.grade].join('_');
// 			return cont(null)
// 		});
// 	}).then(function (cont) {
// 		var book = req.body.book;
// 		book['chapters'] = book['children'];
// 		delete book['children'];
// 		var isLegal = true;
// 		var chapterArray = [];
// 		var newChapterArray = [];
// 		// chapter 为空情况
// 		if (book.chapters.length === 0) {
// 			let coll = db.collection('book');
// 			// 查询条件
// 			let cond = { '_id': book_id };
// 			//
// 			let update_fields = {};
// 			update_fields['chapters']  = [];
// 			update_fields['utime'] = new Date();
// 			// 检测id 是否合法
// 			coll.updateOne(cond,{$set : update_fields}, function (err, result) {
// 				if (err) {
// 					return cont(err);
// 				}
// 				rediser.getCache().keys(cacheKeyPre, function(err, curkeys){
// 					if (err) {
// 						Logger.error(err);
// 					}
// 					rediser.del( curkeys);
// 				});
// 				return responseWrapper.succ({});
// 			});
// 		} else {

// 			convert2DBSchema(book.chapters, chapterArray, newChapterArray, rootPath, isLegal);
// 			if (!isLegal) {
// 				return cont('传入结构不合法');
// 			}
// 			return cont(null, book.chapters, chapterArray, newChapterArray);
// 		}
// 	}).then(function (cont, book_chapter, chapterArray, newChapterArray) {
// 		// 查询chapterArray name path是否变更
// 		var chapterIds = _.map(chapterArray, 'id');
// 		var coll = db.collection('book_chapter');
// 		// 查询条件
// 		var cond = { '_id': {$in: chapterIds} };
// 		var proj = { 'name': 1, 'path': 1 };
// 		// 检测id 是否合法
// 		coll.find(cond).project(proj).toArray( function (err, bookChapters) {
// 			if (err) {
// 				return cont(err);
// 			}
// 			if (!bookChapters.length) {
// 				return responseWrapper.error('PARAMETERS_ERROR', '查询不到bookChapter');
// 			}
// 			var isLegal = true;
// 			var changeChapterArray = _.filter(chapterArray, function(chapter){
// 				var findChapter = _.find(bookChapters, function(bookChapter){
// 					return chapter.id === bookChapter._id;
// 				});
// 				if (findChapter) {
// 					return (findChapter.name !== chapter.name) || (findChapter.path !== chapter.path);
// 				} else {
// 					console.log('不合法:' + JSON.stringify(chapter));
// 					isLegal = false;
// 				}
// 			});
// 			if (!isLegal) {
// 				return cont ('发现异常id')
// 			}
// 			return cont(null, book_chapter, changeChapterArray, newChapterArray);
// 		});
// 	}).then(function (cont, book_chapter, changeChapterArray, newChapterArray) {
// 		if (changeChapterArray.length === 0){
// 			return cont(null, book_chapter, newChapterArray);
// 		}
// 		// 更新changeChapterArray
// 		updateBookChapters(changeChapterArray, function(err){
// 			if (err) {
// 				return cont(err);
// 			}
// 			return cont(null, book_chapter, newChapterArray)
// 		});
// 	}).then(function (cont, book_chapter,  newChapterArray) {
// 		if (newChapterArray.length === 0) {
// 			return cont(null, book_chapter);
// 		}
// 		// 新建newChapterArray
// 		insertBookChapters(newChapterArray, function(err, queue){
// 			if (err) {
// 				return cont(err);
// 			}
// 			fillNewChapterIds(book_chapter, queue);
// 			return cont(null, book_chapter)
// 		});
// 	}).then(function (cont, book_chapter) {
// 		// 更新 当前教材chapter字段
// 		var coll = db.collection('book');
// 		// 查询条件
// 		var cond = { '_id': book_id };
// 		//
// 		var update_fields = {};
// 		update_fields['chapters']  = book_chapter;
// 		update_fields['utime'] = new Date();
// 		// 检测id 是否合法
// 		coll.updateOne(cond,{$set : update_fields}, function (err, result) {
// 			if (err) {
// 				return cont(err);
// 			}
// 			rediser.getCache().keys(cacheKeyPre, function(err, curkeys){
// 				if (err) {
// 					Logger.error(err);
// 				}
// 				rediser.del( curkeys);
// 			});
// 			return responseWrapper.succ({});
// 		});
// 	}).fail(function (cont, error) {
// 		Logger.error(error);
// 		if (typeof error === 'string') {
// 			return responseWrapper.error('HANDLE_ERROR', error);
// 		}
// 		return responseWrapper.error('HANDLE_ERROR');
// 	}).finally(function (cont, error) {
// 		Logger.error(error);
// 		return responseWrapper.error('HANDLE_ERROR');
// 	});
// }
function unsetKnowledgeInChapter(book_id, book_chapter_id, callback) {
	let coll = db.collection('book_chapter');
	// 查询条件
	let cond = { '_id': book_chapter_id };
	//
	let update_fields = {
		'$set': {
			utime: new Date()
		},
		'$unset': {
			knowledges: null,
		}
	};

	coll.updateOne(cond, update_fields, function (err, result) {
		if (err) {
			return callback(err);
		}
		if (book_id) {
			let cacheKeyPre = 'kb_api:v2:books:' + book_id + '*';
			rediser.getCache().keys(cacheKeyPre, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			return callback(null);
		}
		return callback(null);

	});
}
//修改book_chapter的结构

const put_book_chapter = async (req, res) => {
	let responseWrapper = new ResponseWrapper(res);
	try {
		let book_chapter_id = Number(req.params.book_chapter_id);
		let data = {
			topic_knowledge_map: req.body.topic_knowledge_map ? req.body.topic_knowledge_map : '',
			topic_learning_goal: req.body.topic_learning_goal ? req.body.topic_learning_goal : ''
		};
		if (book_chapter_id) {
			let bookChapter = db.collection('book_chapter');
			let result = await bookChapter.findOne({ _id: book_chapter_id });
			if (!result) {
				return responseWrapper.succ({});
			}
			await bookChapter.updateOne({ _id: book_chapter_id }, {
				'$set': data
			});
			return responseWrapper.succ({ id: result._id });
		}
		return responseWrapper.error('PARAMETERS_ERROR', 'id 为必传字段');
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} put_book_chapter`,
			what: 'PARAMETERS_ERROR',
			why: e.message,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
};

// function put_book_chapter(req, res) {
// 	let responseWrapper = new ResponseWrapper(res);
// 	try {
// 		var book_chapter_id = parseInt(req.params.book_chapter_id);
// 		var book_id = req.query.book_id;
// 		assert(book_chapter_id);
// 		var type = req.query.type;
// 	} catch (e) {
// 		Logger.warn({
// 			who: req.query.api_key,
// 			where: `${__filename} put_book_chapter`,
// 			what: 'PARAMETERS_ERROR',
// 			why: e.message,
// 			how: req.originalUrl
// 		});
// 		return responseWrapper.error('PARAMETERS_ERROR', e.message);
// 	}
// 	// 数据查询 ----------------------------
// 	Thenjs(function (cont) {
// 		if (type === 'remove') {
// 			unsetKnowledgeInChapter(book_id, book_chapter_id, function (err) {
// 				if (err) {
// 					return cont(err);
// 				}
// 				return responseWrapper.succ({});
// 			});
// 		} else {
// 			let knowledgeIds = _.map(req.body.knowledges, 'id');
// 			let cond = { '_id': { $in: knowledgeIds } };
// 			let proj = { 'name': 1, 'importance': 1, 'chance': 1, 'score': 1, 'ques_num': { '$size': '$questions' } };//"ques_num": {"$size": "$questions"}}
// 			let pipeLine = [
// 				{
// 					'$match': cond
// 				},
// 				{
// 					'$project': proj
// 				}
// 			];
// 			db.collection('knowledge').aggregate(pipeLine).toArray(function (err, knowledges) {
// 				if (err) {
// 					Logger.error(err.message);
// 					return responseWrapper.error('HANDLE_ERROR', err.message);
// 				}
// 				knowledges.forEach(function (knowledge) {
// 					knowledge['duration'] = 0.0;
// 					knowledge['id'] = knowledge._id;
// 					delete knowledge._id;
// 				});
// 				knowledges.sort(function (a, b) {
// 					let indexA = knowledgeIds.indexOf(a.id);
// 					let indexB = knowledgeIds.indexOf(b.id);
// 					return indexA - indexB;
// 				});
// 				return cont(null, knowledges);
// 			});
// 		}
// 	}).then(function (cont, knowledges) {
// 		let coll = db.collection('book_chapter');
// 		// 查询条件
// 		let cond = { '_id': book_chapter_id };
// 		//
// 		let update_fields = {};
// 		update_fields['knowledges'] = knowledges;
// 		update_fields['utime'] = new Date();
// 		coll.updateOne(cond, { $set: update_fields }, function (err, result) {
// 			if (err) {
// 				return cont(err);
// 			}
// 			if (book_id) {
// 				let cacheKeyPre = 'kb_api:v2:books:' + book_id + '*';
// 				rediser.getCache().keys(cacheKeyPre, function (err, curkeys) {
// 					if (err) {
// 						Logger.error(err);
// 					}
// 					rediser.del(curkeys);
// 				});
// 			}
// 			return responseWrapper.succ({});
// 		});
// 	}).fail(function (cont, error) {
// 		Logger.error(error);
// 		return responseWrapper.error('HANDLE_ERROR');
// 	}).finally(function (cont, error) {
// 		Logger.error(error);
// 		return responseWrapper.error('HANDLE_ERROR');
// 	});
// }
// 根据章节id获取章节 ， type 可以为book ,knowledge_tree
function _getChaptersInfo(chapterIds, type, callback) {
	let dbName;
	if (type === 'book') {
		dbName = 'book_chapter';
	} else if (type === 'knowledge_tree') {
		dbName = 'knowledge_tree_chapter';
	} else {
		return callback('type 字段非法');
	}
	Thenjs(function (cont) {
		let coll = db.collection(dbName);
		// 查询条件
		let cond = { '_id': { $in: chapterIds } };
		coll.find(cond).toArray(function (err, bookChapters) {
			if (err) {
				return cont(err);
			}
			if (bookChapters.length === 0) {
				return callback('查不到该条记录');
			}
			_.each(bookChapters, function (chapter) {
				chapter['id'] = chapter['_id'];
				delete chapter['_id'];
			});
			if (chapterIds.length === 1) {
				bookChapters = bookChapters[0];
			}

			return callback(null, bookChapters);
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return callback(error);
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback(error);
	});
}
//查询book_chapter
function book_chapter(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let chapterIds;
	try {
		let idStr = req.params.book_chapter_id;
		//多个id拼接
		chapterIds = idStr.split(',');
		chapterIds = _.map(chapterIds, function (x) { return parseInt(x); });
		assert(chapterIds);
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} book_chapter`,
			what: 'PARAMETERS_ERROR',
			why: e.message,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		_getChaptersInfo(chapterIds, 'book', function (err, bookChapters) {
			if (err) {
				return cont(err);
			}

			const queryDbs = new Set((req.query.dbs || 'basic').split(','));
			if (_.isArray(bookChapters)) {
				for (const value of bookChapters) {
					if (value) {
						value.topic_knowledge_map = value.topic_knowledge_map ? value.topic_knowledge_map : '';
						value.topic_learning_goal = value.topic_learning_goal ? value.topic_learning_goal : '';
					}
				}
				// 过滤无权限的章节
				bookChapters = bookChapters.filter(bookChapter => (bookChapter.dbs || ['basic']).some(item => queryDbs.has(item)));
				if (bookChapters.length === 0) {
					return cont('查不到该条记录');
				}
			} else {
				bookChapters.topic_knowledge_map = bookChapters.topic_knowledge_map ? bookChapters.topic_knowledge_map : '';
				bookChapters.topic_learning_goal = bookChapters.topic_learning_goal ? bookChapters.topic_learning_goal : '';
				// 过滤无权限的章节
				if (!(bookChapters.dbs || ['basic']).some(item => queryDbs.has(item))) {
					cont('查不到该条记录');
				}
			}
			return responseWrapper.succ(bookChapters);
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		if (typeof (error) === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
// 新增一个章节
// function post_book_chapter(req ,res) {
// 	var responseWrapper = new ResponseWrapper(res);
// 	try {
// 		var name = req.body.name;
// 		assert(name, 'name 字段');
// 	} catch (e) {
// 		return responseWrapper.error('PARAMETERS_ERROR', e.message);
// 	}
// 	// 数据查询 ----------------------------
// 	Thenjs(function (cont) {
// 		counter.getNextSequenceValue('book_chapter', 1, function (err, seq) {
// 			if (err) {
// 				Logger.error(err);
// 				return cont(null, null);
// 			}
// 			return cont(null, seq[0])
// 		});
// 	}).then(function (cont, book_chapter_id) {
// 		let fields = {};
// 		fields['_id'] = book_chapter_id;
// 		fields['name'] = name;
// 		fields['path'] = name;
// 		fields['class'] = 0;
// 		fields['duration'] = 0.0;
// 		fields['utime'] = new Date();
// 		fields['ctime'] = new Date();
// 		// 更新
// 		let coll = db.collection('book_chapter');
// 		// book_chapter
// 		coll.insertOne(fields, function (err, result) {
// 			if (err) {
// 				Logger.error(error);
// 				return cont(null, null);
// 			}
// 			return responseWrapper.succ({id: book_chapter_id});
// 		});
// 	}).fail(function (cont, error) {
// 		Logger.error(error);
// 		return responseWrapper.error('HANDLE_ERROR');
// 	}).finally(function (cont, error) {
// 		Logger.error(error);
// 		return responseWrapper.error('HANDLE_ERROR');
// 	});
// }
//生成根chapter
function genRootChapter(rootPath, callback) {
	Thenjs(function (cont) {
		counter.getNextSequenceValue('book_chapter', 1, function (err, seq) {
			if (err) {
				Logger.error(err);
				return cont(null, null);
			}
			return cont(null, seq[0]);
		});
	}).then(function (cont, book_chapter_id) {
		let fields = {};
		fields['_id'] = book_chapter_id;
		fields['name'] = rootPath;
		fields['path'] = rootPath;
		fields['utime'] = new Date();
		fields['ctime'] = new Date();
		// 更新
		let coll = db.collection('book_chapter');
		// book_chapter
		coll.insertOne(fields, function (err, result) {
			if (err) {
				Logger.error(err);
				return cont(null, null);
			}
			return callback(null, book_chapter_id);
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return callback('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback('HANDLE_ERROR');
	});
}
// 新增一本教材
// function post_book(req, res) {
// 	var responseWrapper = new ResponseWrapper(res);
// 	try {
// 		var period = req.body.period;
// 		assert(period, 'period 字段');
// 		var subject = req.body.subject;
// 		assert(subject, 'subject 字段');
// 		var press_version = req.body.press_version;
// 		assert(press_version, 'press_version 字段');
// 		var grade = req.body.grade;
// 		assert(grade, 'grade 字段');
// 		var rootPath = [period, subject, press_version, grade].join('_');
// 	} catch (e) {
// 		return responseWrapper.error('PARAMETERS_ERROR', e.message);
// 	}
// 	// 数据查询 ----------------------------
// 	Thenjs(function (cont) {
// 		checkBookConflict(0 ,period, subject, press_version, grade, function(err){
// 			if (err) {
// 				return cont(err);
// 			}
// 			return cont(null);
// 		});
// 	}).then(function(cont){
// 		genRootChapter(rootPath, function(err, book_chapter_id){
// 			if (err) {
// 				return cont(err);
// 			}
// 			return cont(null, book_chapter_id);
// 		});
// 	}).then(function(cont, book_chapter_id){
// 		counter.getNextSequenceValue('book', 1, function(err, seq) {
// 			if (err) {
// 				Logger.error(err);
// 				return cont(null, null);
// 			}
// 			return cont(null, seq[0], book_chapter_id);
// 		});
// 	}).then(function(cont, book_id, book_chapter_id){
// 		let fields = {};
// 		fields['_id'] = book_id;
// 		fields['period'] = period;
// 		fields['subject'] = subject;
// 		fields['press_version'] = press_version;
// 		fields['grade'] = grade;
// 		fields['order_no'] = 1000;
// 		fields['book_id'] = book_chapter_id;
// 		fields['chapters'] = [];
// 		fields['has_modified'] = 1;
// 		fields['utime'] = new Date();
// 		fields['ctime'] = new Date();
// 		var coll = db.collection('book');
// 		coll.insertOne(fields, function(err, result){
// 			if (err) {
// 				return cont(err);
// 			}
// 			let booksKey = 'kb_api:v2:books:undefined:undefined:' + process.env.NODE_PORT;
// 			rediser.del(booksKey);
// 			return responseWrapper.succ({id: book_id});
// 		});
// 	}).fail(function (cont, error) {
// 		Logger.error(error);
// 		if (typeof error === 'string') {
// 			return responseWrapper.error('HANDLE_ERROR', error);
// 		}
// 		return responseWrapper.error('HANDLE_ERROR');
// 	}).finally(function (cont, error) {
// 		Logger.error(error);
// 		return responseWrapper.error('HANDLE_ERROR');
// 	});
// }
// 获取db中教材的 章节信息
function getBookAndChapterInDB(bookId, callback) {
	Thenjs(function (cont) {
		// 获取待复制教材
		let cond = { _id: bookId };
		db.collection('book').findOne(cond, function (err, book) {
			if (err) {
				return cont(err);
			}
			return cont(null, book);
		});
	}).then(function (cont, book) {
		let chapterIds = [];
		_getChapterIdsInBook('chapters', book.chapters, chapterIds);
		let cond = { _id: { $in: chapterIds } };
		db.collection('book_chapter').find(cond).toArray(function (err, chapterInfos) {
			if (err) {
				return cont(err);
			}
			return callback(null, book, chapterInfos);
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return callback('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback('HANDLE_ERROR');
	});
}
//教材复制 将新生成的chapterid 赋值给要创建的教材book
function assignNewChapterIdsToBook(bookChapters, bookChapterInfos) {
	_.each(bookChapters, function (bookChapter) {
		let findInfo = _.find(bookChapterInfos, function (info) {
			return info.originId === bookChapter.id;
		});
		if (findInfo) {
			bookChapter.id = findInfo._id;
		}
		if (bookChapter.hasOwnProperty('chapters')) {
			assignNewChapterIdsToBook(bookChapter.chapters, bookChapterInfos);
		}
	});
}
//教材复制 生成新的book 以chapters
function genNewBookAndChapter(rootPath, originBook, originBookChapterInfos, callback) {
	let chapterLen = originBookChapterInfos.length;
	let originRootPath = [originBook.period, originBook.subject, originBook.press_version, originBook.grade].join('_');
	Thenjs(function (cont) {
		counter.getNextSequenceValue('book_chapter', chapterLen, function (err, chapterSeqs) {
			if (err) {
				Logger.error(err);
				return callback(err, null);
			}
			return cont(null, chapterSeqs);
		});
	}).then(function (cont, chapterSeqs) {
		let index = 0;
		originBookChapterInfos.forEach(function (chapterInfo) {
			chapterInfo.originId = chapterInfo._id;
			chapterInfo._id = chapterSeqs[index];
			chapterInfo.path = chapterInfo.path.replace(originRootPath, rootPath);
			chapterInfo['ctime'] = new Date();
			chapterInfo['utime'] = new Date();
			index++;
		});
		return cont(null);
	}).then(function (cont) {
		counter.getNextSequenceValue('book', 1, function (err, seq) {
			if (err) {
				Logger.error(err);
				return callback(err, null);
			}
			return cont(null, seq[0]);
		});
	}).then(function (cont, newBookId) {
		originBook['_id'] = newBookId;
		originBook['ctime'] = new Date();
		originBook['utime'] = new Date();
		originBook['order_no'] = 1000;
		assignNewChapterIdsToBook(originBook.chapters, originBookChapterInfos);
		return callback(null, originBook, originBookChapterInfos);
	}).fail(function (cont, error) {
		Logger.error(error);
		return callback('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback('HANDLE_ERROR');
	});
}
// 判断是否有重复的教材
function checkBookConflict(originBookId, period, subject, press_version, grade, callback) {
	let cond = {
		'period': period,
		'subject': subject,
		'press_version': press_version,
		'grade': grade,
		'invalid': { $ne: 1 }
	};
	db.collection('book').findOne(cond, function (err, book) {
		if (err) {
			return callback(err);
		}
		if (book) {
			//新添加教材
			if (originBookId === 0) {
				return callback('版本/教材名称重复，请重新输入');
			}
			if (book._id === originBookId) {
				return callback(null);
			}
			return callback('版本/教材名称重复，请重新输入');

		}
		return callback(null);
	});
}
// 复制教材
function dup_book(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let rootChapterId;
	let originBook;
	let originBookChapterInfos;
	try {
		var originBookId = parseInt(req.params.book_id);
		assert(originBookId, 'id error');
		var period = req.body.period;
		assert(period, 'period 字段');
		var subject = req.body.subject;
		assert(subject, 'subject 字段');
		var press_version = req.body.press_version;
		assert(press_version, 'press_version 字段');
		var grade = req.body.grade;
		assert(grade, 'grade 字段');
		var rootPath = [period, subject, press_version, grade].join('_');
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} dup_book`,
			what: 'PARAMETERS_ERROR',
			why: e.message,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	Thenjs(function (cont) {
		checkBookConflict(0, period, subject, press_version, grade, function (err) {
			if (err) {
				return cont('版本/教材名称重复，请重新输入');
			}
			return cont(null);
		});
	}).then(function (cont) {
		genRootChapter(rootPath, function (err, retId) {
			if (err) {
				return cont(err);
			}
			rootChapterId = retId;
			return cont(null);
		});
	}).then(function (cont) {
		getBookAndChapterInDB(originBookId, function (err, book, bookChapterInfos) {
			if (err) {
				return cont(err);
			}
			originBook = book;
			originBookChapterInfos = bookChapterInfos;
			return cont(null);
		});
	}).then(function (cont) {
		genNewBookAndChapter(rootPath, originBook, originBookChapterInfos, function (err, newBook, newChapterInfos) {
			if (err) {
				return cont(err);
			}
			newBook['period'] = period;
			newBook['subject'] = subject;
			newBook['press_version'] = press_version;
			newBook['grade'] = grade;
			newBook['book_id'] = rootChapterId;
			return cont(null, newBook, newChapterInfos);
		});
	}).then(function (cont, book, chapters) {
		db.collection('book_chapter').insertMany(chapters, function (err) {
			if (err) {
				return cont(err);
			}
			return cont(null, book);
		});
	}).then(function (cont, book) {
		db.collection('book').insertOne(book, function (err) {
			if (err) {
				return cont(err);
			}
			let booksKey = 'kb_api:v2:books:undefined:undefined:' + process.env.NODE_PORT;
			rediser.del(booksKey);
			return responseWrapper.succ({ id: book._id });
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		if (typeof error === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
// 对每一个章节判断是否需要更新和插入
function updateOrInsertChapter(chapter, type, callback) {
	let coll;
	if (type === 'book') {
		coll = db.collection('book_chapter');
	} else {
		coll = db.collection('knowledge_tree_chapter');
	}
	Thenjs(function (cont) {
		if (!chapter.hasOwnProperty('knowledges')) {
			return cont(null, null);
		}
		// 将knowledge字段填充
		let knowledgeIds = _.pluck(chapter.knowledges, 'id');
		let cond = { '_id': { $in: knowledgeIds } };
		let proj = { 'name': 1, 'importance': 1, 'chance': 1, 'score': 1, 'ques_num': { '$size': '$questions' } };//"ques_num": {"$size": "$questions"}}
		let pipeLine = [
			{
				'$match': cond
			},
			{
				'$project': proj
			}
		];

		// let proj = { 'name': 1, 'importance': 1, 'chance': 1, 'score': 1, 'questions': 1 };//"ques_num": {"$size": "$questions"}}
		db.collection('knowledge').aggregate(pipeLine).toArray(function (err, knowledges) {
			if (err) {
				Logger.error(err.message);
				return callback(err);
			}
			knowledges.forEach(function (knowledge) {
				knowledge['duration'] = 0.0;
				knowledge['id'] = knowledge._id;
				delete knowledge._id;

			});
			knowledges.sort(function (a, b) {
				let indexA = knowledgeIds.indexOf(a.id);
				let indexB = knowledgeIds.indexOf(b.id);
				return indexA - indexB;
			});
			chapter.knowledges = knowledges;
			return cont(null, null);
		});

	}).then(function (cont) {

		coll.findOne({ '_id': chapter._id }, function (err, result) {
			if (err) {
				return cont(err);
			}
			return cont(null, result);
		});
	}).then(function (cont, result) {
		// 插入chapter
		if (!result) {
			chapter['ctime'] = new Date();
			chapter['utime'] = new Date();
		} else {
			chapter['utime'] = new Date();
		}
		let cond = { _id: chapter['_id'] };
		let opts = { upsert: true };
		// console.log('----------')
		// console.log(JSON.stringify(chapter))
		delete chapter._id
		let update_fields = { $set: chapter };
		if (!chapter.hasOwnProperty('knowledges')) {
			update_fields['$unset'] = { knowledges: null };
		}
		coll.findAndModify(cond, [], update_fields, opts, function (err, item) {
			if (err) {
				return callback(err);
			}
			return callback(null);
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return callback(error);
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback(error);
	});
}
function insertChapters(params, callback) {
	let type = params['type'];
	let coll;
	if (type === 'book') {
		coll = db.collection('book_chapter');
	} else {
		coll = db.collection('knowledge_tree_chapter');
	}
	Thenjs.each(params['chapters'], function (cont, chapter) {

		chapter['utime'] = new Date();
		if (type === 'book') {
			chapter.class = 0;
			chapter.duration = 0.0;
		} else if (type === 'knowledge_tree') {
		}
		// 填充knowledge字段
		updateOrInsertChapter(chapter, type, function (err) {
			if (err) {
				return cont(null, null);
			}
			return cont(null, {});
		});


	}).then(function (cont, resArray) {
		resArray = _.filter(resArray, function (res) {
			return res !== null;
		});
		if (resArray.length !== params['chapters'].length) {
			return callback('部分失败');
		}
		return callback(null);

	}).fail(function (cont, error) {
		Logger.error(error);
		return callback('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback('HANDLE_ERROR');
	});
}
// 批量插入更新章节
function post_book_chapters(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let params = {};
	try {
		assert(req.body.chapters);
		params['chapters'] = req.body.chapters;
		params['type'] = 'book';
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} post_book_chapters`,
			what: 'PARAMETERS_ERROR',
			why: e.message,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		insertChapters(params, function (err) {
			if (err) {
				return cont(err);
			}
			return responseWrapper.succ({});
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
// books/check
// 对外提供接口检查是否冲突
function check_book_conflict(req, res) {
	//checkBookConflict
	let responseWrapper = new ResponseWrapper(res);
	try {
		var book_id = parseInt(req.query.book_id);
		//assert(!isNaN(book_id));
		var toPeriod = req.query.period;
		assert(toPeriod !== '');
		var toSubject = req.query.subject;
		assert(toSubject != '');
		var toPress_version = req.query.press_version;
		assert(toPress_version != '');
		var toGrade = req.query.grade;
		assert(toGrade != '');
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} check_book_conflict`,
			what: 'PARAMETERS_ERROR',
			why: e.message,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		checkBookConflict(book_id, toPeriod, toSubject, toPress_version, toGrade, function (err) {
			if (err) {
				return cont(err);
			}
			return cont(null);
		});
	}).then(function (cont) {
		return responseWrapper.succ({});
	}).fail(function (cont, error) {
		Logger.error(error);
		if (typeof error === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
// 同步教材结构信息
function post_book_struct(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	try {

		var body = req.body;
		var book_id = body._id;
		var curRootPath = [body.period, body.subject, body.press_version, body.grade].join('_');
		var originBook;
		assert(body.period);
		assert(body.subject);
		assert(body.press_version);
		assert(body.grade);
		assert(body.hasOwnProperty('chapters'));
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} post_book_struct`,
			what: 'PARAMETERS_ERROR',
			why: e.message,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		checkBookConflict(book_id, body.period, body.subject, body.press_version, body.grade, function (err) {
			if (err) {
				return cont(err);
			}
			return cont(null);
		});
	}).then(function (cont) {
		// 获取原教材结构
		db.collection('book').findOne({ _id: book_id }, function (err, book) {
			if (err) {
				return cont(err);
			}
			if (book) {
				originBook = book;
			}

			return cont(null);
		});
	}).then(function (cont) {
		if (originBook) {
			// 判断是否需要修改编目
			let originRootPath = [originBook.period, originBook.subject, originBook.press_version, originBook.grade].join('_');
			if (originRootPath !== curRootPath) {
				let originBookInfo = {
					'period': originBook.period, 'subject': originBook.subject,
					'press_version': originBook.press_version, 'grade': originBook.grade
				};

				// createExampaperCategory({
				// 	'period': body.period,
				// 	'subject': body.subject,
				// 	'press_version': body.press_version,
				// 	'grade': body.grade
				// });
				changeBookCatalog(body, originBookInfo, 'edit', function (err) {
					if (err) {
						return cont(err);
					}
					return cont(null);
				});
			} else {
				return cont(null);
			}
		} else {
			// 新的教材，直接走创建逻辑
			//createExampaperCategory(body);
			changeBookCatalog(body, null, 'new', function (err) {
				if (err) {
					return cont(err);
				}
				return cont(null);
			});
		}
	}).then(function (cont) {
		// 插入book表
		if (!originBook) {
			body['ctime'] = new Date();
		}
		body['utime'] = new Date();
		body['has_modified'] = 1;
		let cond = { _id: book_id };
		let opts = { upsert: true };

		db.collection('book').findAndModify(cond, [], { $set: body }, opts, function (err, item) {
			if (err) {
				return cont(err);
			}
			// 下面这套逻辑是用来删除
			/* async */
			/* try {
				db.collection('book').findOne({
					'period': originBook.period,
					'subject': originBook.subject,
					'press_version': originBook.press_version,
					'grade': originBook.grade
				}, function (err, doc) {
					if (err || doc) {
						return;
					}
					db.collection('exampaper_category').removeOne({
						'period': originBook.period,
						'subject': originBook.subject,
						'press_version': originBook.press_version,
						'grade': originBook.grade
					});
				});
			} catch (err) { } */
			let cacheKeyPkg = 'kb_api:v2:books_pkg:*';
			let cacheKeyBook = 'kb_api:v2:books:' + book_id + '*';
			rediser.getCache().keys(cacheKeyPkg, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			rediser.getCache().keys(cacheKeyBook, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			return responseWrapper.succ({});
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
//
function _getLevelPath(key, bookLevel, curPath, chapterArray) {
	_.each(bookLevel, function (elem) {
		if (elem.hasOwnProperty('name') && elem.hasOwnProperty('id')) {
			let curPath = curPath + '_' + elem.name;
			let cur = { 'id': elem.id, 'name': elem.name, 'path': curPath };
			chapterArray.push(cur);
		}
		if (key === 'book') {
			if (elem.hasOwnProperty('chapters')) {
				if (elem.chapters.length) {
					let curPath = curPath + '_' + elem.name;
					convert2DBSchema(key, elem.chapters, curPath, chapterArray);
				} else {
					delete elem.chapters;
				}
			}
		}
	});
}
//将db结构中的book 获取chapter path信息
function _getChapterPathInDB(key, bookLevelChapter) {
	let chapterArray = [];
	_getLevelPath(key, bookLevelChapter, chapterArray);
	return chapterArray;
}
// 删除catalog 某一项
function deleteCatalogItem(catalog, id, period, subject, press_version, grade) {
	let selectPeriodCatalog = _.find(catalog.periods, function (periodCatalog) {
		return periodCatalog.name === period;
	});
	let selectSubjectCatalog = _.find(selectPeriodCatalog.subjects, function (subjectCatalog) {
		return subjectCatalog.name === subject;
	});
	let selectPressCatalog = _.find(selectSubjectCatalog.press_versions, function (press) {
		return press.name === press_version;
	});
	selectPressCatalog.grades = _.filter(selectPressCatalog.grades, function (gradeCatalog) {
		return gradeCatalog.name !== grade;
	});
	// 当前 版本下面年级为空 需要删除此版本
	if (!selectPressCatalog.grades.length) {
		selectSubjectCatalog.press_versions = _.filter(selectSubjectCatalog.press_versions, function (pressCatalog) {
			return pressCatalog.name !== press_version;
		});
	}
}
// 增加catalog 某一项
function addCatalogItem(catalog, id, period, subject, press_version, grade) {
	let selectPeriodCatalog = _.find(catalog.periods, function (periodCatalog) {
		return periodCatalog.name === period;
	});
	if (!selectPeriodCatalog) {
		selectPeriodCatalog = { 'name': period, 'subjects': [] };
		catalog.periods.push(selectPeriodCatalog);
	}
	let selectSubjectCatalog = _.find(selectPeriodCatalog.subjects, function (subjectCatalog) {
		return subjectCatalog.name === subject;
	});
	if (!selectSubjectCatalog) {
		selectSubjectCatalog = { 'name': subject, 'press_versions': [] };
		selectPeriodCatalog.subjects.push(selectSubjectCatalog);
	}
	let selectPressCatalog = _.find(selectSubjectCatalog.press_versions, function (pressCatalog) {
		return pressCatalog.name === press_version;
	});
	if (!selectPressCatalog) {
		selectPressCatalog = { 'name': press_version, 'grades': [] };
		selectSubjectCatalog.press_versions.push(selectPressCatalog);
	}
	selectPressCatalog.grades.push({ 'id': id, 'name': grade });
}
// curInfo body
// originInfo (press_version, grade)
// opType  edit, new , delete
function changeBookCatalog(curInfo, originInfo, opType, callback) {
	Thenjs(function (cont) {
		db.collection('catalog').findOne({ _id: 'book' }, function (err, catalog) {
			if (err) {
				return callback(err);
			}
			return cont(null, catalog);
		});
	}).then(function (cont, catalog) {
		let id = curInfo._id;
		let period = curInfo.period;
		let subject = curInfo.subject;
		let press_version = curInfo.press_version;
		let grade = curInfo.grade;
		if (opType === 'edit') {
			deleteCatalogItem(catalog, id, originInfo.period, originInfo.subject, originInfo.press_version, originInfo.grade);
			addCatalogItem(catalog, id, period, subject, press_version, grade);
		} else if (opType === 'new') {
			addCatalogItem(catalog, id, period, subject, press_version, grade);
		} else if (opType === 'delete') {
			deleteCatalogItem(catalog, id, originInfo.period, originInfo.subject, originInfo.press_version, originInfo.grade);
		}
		return cont(null, catalog);
	}).then(function (cont, catalog) {
		catalog['utime'] = new Date();
		db.collection('catalog').updateOne({ _id: 'book' }, { $set: catalog }, function (err, catalog) {
			if (err) {
				return callback(err);
			}
			return callback(null);
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return callback(error);
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback(error);
	});
}
function delete_book(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	try {
		var book_id = parseInt(req.params.id);
		assert(!isNaN(book_id));
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} delete_book`,
			what: 'PARAMETERS_ERROR',
			why: e.message,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	// 数据查询 ----------------------------
	Thenjs(function (cont) {

		// 获取原教材结构
		db.collection('book').findOne({ _id: book_id }, function (err, book) {
			if (err) {
				return cont(err);
			}
			if (!book) {
				return responseWrapper.error('HANDLE_ERROR', '此id教材不存在');
			}
			return cont(null, book);
		});
	}).then(function (cont, book) {
		changeBookCatalog({ 'id': book_id }, book, 'delete', function (err) {
			if (err) {
				return cont(err);
			}
			return cont(null);
		});
	}).then(function (cont) {
		// 获取原教材结构
		db.collection('book').updateOne({ _id: book_id }, { $set: { 'invalid': 1 } }, function (err, book) {
			if (err) {
				return cont(err);
			}
			let cacheKeyPkg = 'kb_api:v2:books_pkg:*';
			let cacheKeyBook = 'kb_api:v2:books:' + book_id + '*';
			rediser.getCache().keys(cacheKeyPkg, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			rediser.getCache().keys(cacheKeyBook, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			return responseWrapper.succ({});
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
function put_book_catalog(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let selectPeriodCatalog;
	try {
		assert(req.body.period);
		assert(req.body.subject);
		assert(req.body.press_versions);
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} put_book_catalog`,
			what: 'PARAMETERS_ERROR',
			why: e.message,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	// 数据查询 ----------------------------
	Thenjs(function (cont) {

		db.collection('catalog').findOne({ _id: 'book' }, function (err, catalog) {
			if (err) {
				return callback(err);
			}
			if (!catalog) {
				return cont('catalog 为空');
			}
			return cont(null, catalog);
		});
	}).then(function (cont, catalog) {
		selectPeriodCatalog = _.find(catalog.periods, function (periodCatalog) {
			return periodCatalog.name === req.body.period;
		});
		if (!selectPeriodCatalog) {
			return cont('没有发现该学段');
		}
		let selectSubjectCatalog = _.find(selectPeriodCatalog.subjects, function (subjectCatalog) {
			return subjectCatalog.name === req.body.subject;
		});
		if (!selectSubjectCatalog) {
			return cont('没有发现该学科');
		}
		let localIdArray = [];
		_.each(selectSubjectCatalog.press_versions, function (pressCatalog) {
			let press = pressCatalog.name;
			_.each(pressCatalog.grades, function (item) {
				let curItem = { 'id': item.id, 'path': press + '_' + item.name };
				localIdArray.push(curItem);
			});
		});
		let postIdArray = [];
		_.each(req.body.press_versions, function (pressCatalog) {
			let press = pressCatalog.name;
			_.each(pressCatalog.grades, function (item) {
				let curItem = { 'id': item.id, 'path': press + '_' + item.name };
				postIdArray.push(curItem);
			});
		});
		return cont(null, localIdArray, postIdArray, catalog);
	}).then(function (cont, localIdArray, postIdArray, catalog) {
		if (localIdArray.length === postIdArray.length) {
			let isLegal = true;
			_.each(localIdArray, function (localInfo) {
				let findPostInfo = _.find(postIdArray, function (postInfo) {
					return postInfo.id === localInfo.id;
				});
				if (!findPostInfo) {
					isLegal = false;
				} else {
					if (findPostInfo.path !== localInfo.path) {
						isLegal = false;
					}
				}
			});
			if (!isLegal) {
				return cont('编目数据冲突');
			}
			return cont(null, catalog);

		}
		return cont('编目数据冲突');

	}).then(function (cont, catalog) {
		let i;
		for (i = 0; i < selectPeriodCatalog.subjects.length; ++i) {
			if (selectPeriodCatalog.subjects[i].name === req.body.subject) {
				break;
			}
		}
		selectPeriodCatalog.subjects[i].press_versions = req.body.press_versions;
		catalog['utime'] = new Date();
		db.collection('catalog').updateOne({ _id: 'book' }, catalog, function (err, result) {
			if (err) {
				return callback(err);
			}
			let cacheKeyPkg = 'kb_api:v2:books_pkg:*';
			rediser.getCache().keys(cacheKeyPkg, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			return responseWrapper.succ({});
		});

	}).fail(function (cont, error) {
		Logger.error(error);
		if (typeof error === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

async function postChapterBook(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	const body = req.body;
	const chapterBook = {
		_id: body.book_chapter_id,
		knowledge_analysis: body.knowledge_analysis,
		questions_a: body.questions_a,
		questions_b: body.questions_b,
		questions_c: body.questions_c,
		track_exam: body.track_exam,
	};
	if (!req.body) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} postChapterBook`,
			what: 'PARAMETERS_ERROR',
			why: '没有 chapterBook',
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', '没有 chapterBook');
	}
	try {
		const existChapterBook = await db.collection('chapter_book').findOne({ _id: chapterBook._id });
		if (existChapterBook) throw new Error('该章节已有此同步本:' + chapterBook._id);

		await db.collection('chapter_book').insertOne(chapterBook);

		res.resMode = 'normal';
		return responseWrapper.succ({ id: body.book_chapter_id });
	} catch (error) {
		Logger.error(error);
		if (typeof error === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}
}

async function putChapterBook(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	const body = req.body;
	const chapterBookId = parseInt(req.params.chapter_book_id);
	if (typeof chapterBookId !== 'number') {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} postChapterBook`,
			what: 'PARAMETERS_ERROR',
			why: 'ID不是数字',
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', 'ID不是数字');
	}
	try {
		const chapterBook = await db.collection('chapter_book').findOne({ _id: parseInt(chapterBookId) });
		if (!chapterBook) throw new Error('该章节无此同步本:' + chapterBookId);
		const cond = {
			_id: chapterBookId,
		}
		const updateObj = {
			$set: body,
		};
		await db.collection('chapter_book').updateOne(cond, updateObj);

		res.resMode = 'normal';
		return responseWrapper.succ({});
	} catch (error) {
		Logger.error(error);
		if (typeof error === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}
}

async function getChapterBook(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	const chapterBookId = parseInt(req.params.chapter_book_id);
	try {
		const cond = {
			_id: chapterBookId,
		}
		const chapterBook = await db.collection('chapter_book').findOne(cond);

		res.resMode = 'normal';
		return responseWrapper.succ(chapterBook);
	} catch (error) {
		Logger.error(error);
		if (typeof error === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}
}

//	获取某教材下所有知识点
function getKnowledgesByBook(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	let params = null, cache_key = null;
	try {
		let fun_name = arguments.callee.name;
		params = params_utils.create_params(req, kb_api[fun_name]);
		cache_key = params['cache_key'];
	} catch (e) {
		Logger.warn({
			who: req.query.api_key,
			where: `${__filename} book`,
			what: 'PARAMETERS_ERROR',
			why: e,
			how: req.originalUrl
		});
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}
	Thenjs(function (cont) {
		rediser.get(cache_key, function (err, item) {
			if (item) {
				cont(null, item);
			} else {
				_get_book(params, function (err, book) {
					if (err) {
						return cont(err);
					}
					return cont(null, book);
				});
			}
		});
	}).then((cont, book) => {
		const queryDbs = new Set((req.query.dbs || 'basic').split(','));
		const bookDbs = book.dbs || ['basic'];
		// 按照权限进行过滤
		// 1. 如果没有权限，则返回空
		// 2. 如果未传参，则只返回基本数据，即book中dbs为空或者包含basic
		if (bookDbs.some(item => queryDbs.has(item))) {
			return cont(null, book);
		} else {
			cont('没有此教材' + JSON.stringify(params));
		}
	}).then(function (cont, book) {
		let keys = {
			'chapters': 'chapter',
			'knowledges': 'knowledge',
		}, excludes = [];
		loosen_tree(book['book'], keys, excludes);
		cont(null, book);
	}).then(function (cont, result) {
		if (result) {
			const curKnowledges = _getKnowledge(result.book.children);
			return responseWrapper.succ({
				total: curKnowledges.length,
				list: curKnowledges,
			});
		}
		return responseWrapper.error('NULL_ERROR');

	}).fail(function (cont, error) {
		Logger.error(error);
		if (typeof error === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

function _getKnowledge(data) {
	let knowledges = [];
	for (let info of data) {
		if (info.key === 'knowledge') {
			knowledges.push(info);
		} else if (info.children && info.children.length > 0) {
			const curKnowledges = _getKnowledge(info.children);
			knowledges = knowledges.concat(curKnowledges);
		}
	}
	return knowledges;
}

module.exports = {
	books: books,
	book: book,
	book_struct: book_struct,				// 获取book 结构
	// put_book_struct: put_book_struct,		// 修改book 结构
	book_chapter: book_chapter,				// 获取book_chapter
	put_book_chapter: put_book_chapter,		// 修改book_chapter
	// post_book_chapter: post_book_chapter,	// 增加book_chapter
	// modify_book: modify_book,
	// post_book: post_book,					// 新增教材
	dup_book: dup_book,						// 新增教材,根据某本教材复制
	check_book_conflict: check_book_conflict, // 检查教材改名是否冲突
	post_book_struct: post_book_struct,
	post_book_chapters: post_book_chapters,
	delete_book: delete_book,
	put_book_catalog: put_book_catalog,		// 调整教材编目顺序
	// 导出函数
	_getChapterIdsInBook: _getChapterIdsInBook,
	_assignChaptersToBook: _assignChaptersToBook,
	changeKeyInBookStruct: changeKeyInBookStruct,
	filterCatalog: filterCatalog,
	_getChaptersInfo: _getChaptersInfo,
	insertChapters: insertChapters,
	postChapterBook: postChapterBook,
	putChapterBook: putChapterBook,
	getChapterBook: getChapterBook,
	getKnowledgesByBook,
};
