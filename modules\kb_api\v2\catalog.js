const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const ResponseWrapper = require('../../common/utils/response_wrapper');
let Logger = require('../../common/utils/logger');
const counter = require('../../common/utils/counter');

const textbook = 'text_book';
const catalog = 'catalog';

//新增版本，教材
const updateCatalogs = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let period = req.query.period;
    let subject = req.query.subject;
    let body = req.body;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    try {
        let data = await db
            .collection(catalog)
            .findOne({ _id: 'text_book' });
        for (let per of data.periods) {
            if (per.name === period) {
                for (let sub of per.subjects) {
                    if (sub.name === subject) {
                        sub.press_versions = body;
                    }
                }
            }
        }
        data.utime = new Date();
        await db.collection(catalog).updateOne({ _id: 'text_book' }, data);
        responseWrapper.succ();
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

//是否可以新增编目
const canUpdate = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let data = req.body;
    let period = data.period;
    let subject = data.subject;
    let press_versions = data.press_version;
    let grade = data.grade;
    if (!period || !subject) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    if (!press_versions && !grade) {
        return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    let cond = {
        period: period,
        subject: subject,
    };
    if (press_versions) {
        cond.press_version = press_versions;
    }
    if (grade) {
        cond.grade = grade;
    }
    try {
        let count = await db.collection(textbook).find(cond).count();
        let book = await db.collection(textbook).findOne(cond);
        let result = false;
        if ((count === 1 && book._id ===data.id) || count === 0) {
            result = true;
        }
        return result;
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('存在相同编目', e.message);
    }
};

const updateRes = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let data = req.body;
    let period = data.period;
    let subject = data.subject;
    let press_versions = data.press_version;
    let grade = data.grade;
    if (!period || !subject) {
        responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    if (!press_versions && !grade) {
        responseWrapper.error('PARAMETERS_ERROR', '参数错误');
    }
    let cond = {
        period: period,
        subject: subject,
    };
    if (press_versions) {
        cond.press_version = press_versions;
    }
    if (grade) {
        cond.grade = grade;
    }
    try {
        let count = await db.collection(textbook).find(cond).count();
        let book = await db.collection(textbook).findOne(cond);
        let result = false;
        if ((count === 1 && book._id ===data.id) || count === 0) {
            result = true;
        }
        responseWrapper.succ(result);
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('存在相同编目', e.message);
    }
};

module.exports = {
    updateCatalogs,
    canUpdate,
    updateRes
};
