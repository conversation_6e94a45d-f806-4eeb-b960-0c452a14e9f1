let Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const counter = require('../../common/utils/counter');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const schema = require('./schemas/english_word');
const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const _ = require('lodash');

// 数据库集合名
const colEnglish = 'english_word';

const createWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let data = req.body;
        if (!ajv.validate(schema.english, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '格式错误');
        }
        let word = await db.collection(colEnglish).findOne({ word: data.word, period: data.period, invalid: { $ne: 1 } });
        if (word) {
            return responseWrapper.error('HANDLE_ERROR', '单词已存在');
        }
        const seq = await counter.nextSeq('english_word', 1);
        data._id = seq[0];
        data.ctime = new Date();
        data.utime = new Date();
        let result = await db.collection(colEnglish).insertOne(data);
        if (result.result.ok === 1) {
            return responseWrapper.succ({});
        }
        return responseWrapper.error('HANDLE_ERROR', '新增出错了');
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 判断是否已存在该叠加要素类别或者叠加要素
const updateWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let data = req.body;
        let id = req.params.word_id;
        if (!id) { return responseWrapper.error('HANDLE_ERROR', 'ID未传'); }
        if (!ajv.validate(schema.english, data)) {
            Logger.error(ajv.errorsText());
            return responseWrapper.error('PARAMETERS_ERROR', '格式错误');
        }
        data.utime = new Date();
        let result = await db.collection(colEnglish).updateOne({ _id: parseInt(id) }, { $set: data });
        if (result.result.ok === 1) {
            return responseWrapper.succ({});
        }
        return responseWrapper.error('HANDLE_ERROR', '修改出错了');
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

// 
const getWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let id = req.params.word_id;
        if (!id) { return responseWrapper.error('HANDLE_ERROR', 'ID未传'); }
        let word = await db.collection(colEnglish).findOne({ _id: parseInt(id), invalid: { $ne: 1 } });
        if (word) {
            word.id = word._id;
            delete word._id;
            delete word.utime;
            delete word.ctime;
            return responseWrapper.succ(word);
        }
        return responseWrapper.error('HANDLE_ERROR', '数据不存在');
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

const removeWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let id = req.params.word_id;
        if (!id) { return responseWrapper.error('HANDLE_ERROR', 'ID未传'); }
        await db.collection(colEnglish).updateOne({ _id: parseInt(id) }, { $set: { 'invalid': 1, utime: new Date() } });
        return responseWrapper.succ({});
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

const getWordsList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let period = req.query.period;
        if (!period) {
            return responseWrapper.error('PARAMETERS_ERROR', 'period未传');
        }
        let offset = parseInt(req.query.offset) || 0;
        let limit = parseInt(req.query.limit) || 100;
        let result = await db.collection(colEnglish).find({ period: period }).skip(offset).limit(limit).sort({ word: 1 }).toArray();
        let totalNum = await db.collection(colEnglish).count({ period: period });
        for (let item of result) {
            item.id = item._id;
            delete item._id;
        }
        return responseWrapper.succ({ total_num: totalNum, list: result });
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

const existsWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    try {
        let period = req.query.period;
        let word = req.params.word;
        if (!period) {
            return responseWrapper.error('PARAMETERS_ERROR', 'period未传');
        }
        if (!word) {
            return responseWrapper.error('PARAMETERS_ERROR', 'word未传');
        }
        let cond = {
            period: period,
            word: word,
            invalid: { $ne: 1 },
        };
        let result = await db.collection(colEnglish).findOne(cond);
        return responseWrapper.succ(!result);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
};

module.exports = {
    createWord,
    updateWord,
    getWord,
    removeWord,
    getWordsList,
    existsWord
};
