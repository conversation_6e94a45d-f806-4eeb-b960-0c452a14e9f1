let Thenjs = require('thenjs');
let config = require('config');
let URL = require('url');
let util = require('util');
let request = require('request');
let qs = require('querystring');
let KBServerCfg = config.get('kb_api_server');
let Logger = require('../../../common/utils/logger');
let ResponseWrapper = require('../../../common/utils/response_wrapper');
let mongodber = require('../../../common/utils/mongodber');
let db = mongodber.use('KB');
let params_utils = require('../../../common/utils/params.js');
let rediser = require('../../../common/utils/rediser');
let imageUtils = require('../image_utils');
let kb_api = require('../../config.js').kb_api['v2'];
let dirty_string = require('../utils').dirty_string;
let dirty_id = require('../utils').dirty_id;
let resource = require('../resource');
let _ = require('underscore');
let getKnowledgesQuestions = require('../knowledge').getKnowledgesQuestions;
const utils = require('./utils');
const questionMobile = require('./question_mobile');
const counter = require('../../../common/utils/counter');
const { filter_fields } = require('./utils');
const { filterQuestion } = require('../../v2/question/utils/filterData');
const mapExampaperName = require('../exampaper/mapExampaperName');
const filterExampaperName = require('../exampaper/filterExampaperName');
const axios = require('axios');

// 获取试题的小题数和选项数
const getSubQuesCountAndOptionsCount = (question) => {
    try {
        if (question && ['英语', '地理', '政治'].includes(question.subject)) {
            if (question.blocks) {
                let stems = question.blocks.stems;
                if (Array.isArray(stems) && stems.length > 0) {
                    let countOptionsArr = [];
                    for (let stem of stems) {
                        if (stem.options && (typeof stem.options === 'object')) {
                            let len = Object.keys(stem.options).length;
                            if (len > 0 && !countOptionsArr.includes(len)) {
                                countOptionsArr.push(len);
                            }
                        }
                    }
                    if (!question.tags) {
                        question.tags = {};
                    }
                    question.tags.count_sub_ques = stems.length;
                    question.tags.count_options = countOptionsArr;
                }
            }
        }
    } catch (e) {
        Logger.error(e);
    }
};

// 大题小题的core_knowledges，id,name必填，'chance','score'没有为空
function compatible_core_knowledges(kns) {
    let keys = ['id', 'name'];
    let knowledgeKeys = ['id', 'name', 'chance', 'score', 'know_methods', 'targets'];
    for (let i in kns) {
        let kn = kns[i];
        kn.chance = kn.chance * 1 || 0;
        kn.score = kn.score * 1 || 0;
        for (let k in kn) {
            //去掉多余字段
            if (knowledgeKeys.indexOf(k) === -1) {
                delete kn[k];
                continue;
            }
            //必填字段
            if (keys.indexOf(k) === -1) {
                //throw ('core_knowledges.'+k+'必填字段');
            }
        }
    }
}

/**
 * 试题结构变化，兼容返回数据
 * 下面三个新增字段
 * blocks.knowledges
 * blocks.core_knowledges
 * blocks.comments
 * @param {*} ques 试题数组
 * @return {*}
 */
function compatible_question(ques) {
    if (!Array.isArray(ques)) {
        return new Error('compatible_question要求试题结构是数组');
    }
    //blocks 兼容 'knowledges', 'core_knowledges'
    let compatible = ['knowledges', 'core_knowledges'];
    let err_message = null;
    try {
        for (let fields of ques) {
            for (let ckey of compatible) {
                let blks = fields['blocks'];
                if (!blks[ckey]) {
                    blks[ckey] = _.map(blks.stems, () => fields[ckey] || []);
                    //大题小题的core_knowledges，id,name必填，'chance','score'没有为空

                } else {
                    if (!Array.isArray(blks[ckey])) {
                        Logger.error(fields._id + '试题结构blocks.' + ckey + '异常');
                        err_message = new Error('blocks.' + ckey + '应该是个数组');
                    }
                    for (let ks of blks[ckey]) {
                        compatible_core_knowledges(ks);
                    }
                    if (blks[ckey].length !== blks.stems.length) {
                        let kArr = [];
                        for (let i = 0; i < blks.stems.length; i++) {
                            let ks = blks[ckey][i] || fields[ckey] || [];
                            kArr.push(ks);
                        }
                        blks[ckey] = kArr;
                    }
                }

                blks[ckey] = blks[ckey].map(u => u && Array.isArray(u) ? u : []);
                delete blks.comments;
                compatible_question(fields[ckey] || []);
                fields[ckey] = fields[ckey] || [];
                //选择题：‘’，多选题：[''],填空题：[''],解答题：''
                for (let i = 0; i < blks.types.length; i++) {
                    let t_type = blks.types[i];
                    let ans = blks.answers[i] || '';
                    if (['选择题', '解答题'].indexOf(t_type) !== -1) {
                        if (Array.isArray(ans)) {
                            blks.answers[i] = ans.join(' ');
                        }
                    }
                    if (['多选题', '填空题'].indexOf(t_type) !== -1) {
                        if (typeof (ans) == 'string') {
                            blks.answers[i] = [ans];
                        }
                    }
                }
            }
        }
    } catch (e) {
        return e;
    }
    return err_message;
}

function _requestQuestions(qids, device, fields_type, callback) {
    let questionsUrl = URL.format({
        protocol: KBServerCfg.protocol,
        hostname: KBServerCfg.hostname,
        port: KBServerCfg.port,
        pathname: 'kb_api/v2/questions/',
        search: qs.stringify({
            api_key: KBServerCfg.api_key,
            fields_type: fields_type,
            device: device,
            question_ids: qids
        })
    });
    try {
        request.get({
            url: questionsUrl,
            headers: {
                'content-type': 'application/json'
            },
        }, function (error, response, _body) {
            if (response.statusCode !== 200 || error) {
                return callback('HANDLE_ERROR', null);
            }
            let retObj = JSON.parse(_body);
            return callback(null, retObj);
        });
    } catch (e) {
        Logger.error(e);
        return callback(e);
    }
}

function _requestQuestion(qid, device, fields_type, callback) {

    let questionsUrl = URL.format({
        protocol: KBServerCfg.protocol,
        hostname: KBServerCfg.hostname,
        port: KBServerCfg.port,
        pathname: `kb_api/v2/questions/${qid}`,
        search: qs.stringify({
            api_key: KBServerCfg.api_key,
            fields_type: fields_type,
            device: device,
        })
    });
    try {
        request.get({
            url: questionsUrl,
            headers: {
                'content-type': 'application/json'
            },
        }, function (error, response, _body) {

            if (response.statusCode !== 200 || error) {
                return callback('HANDLE_ERROR', null);
            }
            let retObj = JSON.parse(_body);
            return callback(null, retObj);
        });
    } catch (e) {
        Logger.error(e);
        return callback(e);
    }
}

/*
 * Desc:
 *      将元素分割为多组，每组元素个数为n
 * Params:
 *      ids - 待分割的id列表
 *      k_per_grp - 每组内元素个数
 * Return:
 *         分割后的id列表
 * Author:
 *      zhangjun
 * Date:
 *      2016-06-28
*/
function ids_split(ids, k_per_grp) {
    let ids_grp = new Array();
    let t = [];
    for (let i in ids) {
        if (i % k_per_grp == (k_per_grp - 1)) {
            ids_grp.push(t);
            t = [];
        }
        t.push(ids[i]);
    }
    if (t.length > 0) {
        ids_grp.push(t);
    }
    return ids_grp;
}

/**
 * 获取缓存中的知识点
 * @param {*} kids
 * @param {*} cache_key
 */
async function get_cache_knowledges(kids, cache_key) {
    let knowledges = null, uncache = true;
    if (kids.length === 1) {
        knowledges = await rediser.get(cache_key);
        if (knowledges) {
            uncache = false;
            knowledges = [knowledges];
        }
    }
    if (uncache) {
        let coll = db.collection('knowledge');
        // 查询条件
        let cond = { '_id': { $in: kids } };
        // 提取出的字段
        let proj = {
            questions: 1,
            single_assess: 1,
            associate_knowledges: 1,
            dbs: 1,
        };
        knowledges = await coll.find(cond).project(proj).toArray();
        if (!knowledges.length) {
            return [];
        }
    }
    if (uncache && kids.length === 1) {
        rediser.set(cache_key, knowledges[0], 60 * 60);
    }
    return knowledges;
}

/**
 * 过滤是否精品题
 * @param {*} knows
 * @param {*} ques_tag
 */
async function check_elite_ques(knows, ques_tag) {
    // 针对精品题
    if (['elite', 'unelite'].indexOf(ques_tag) === -1) {
        return;
    }
    // 如果取精品题则需要查询该知识点下的全量精品题id
    let coll = db.collection('knowledge_elite_question');
    let kids = _.pluck(knows, '_id');
    // 查询条件
    let cond = { '_id': { $in: kids } };
    // 查询
    let keqs = await coll.find(cond).toArray();

    let elite_qids = new Set();
    for (let item of keqs) {
        let elite_qs = item['questions'];
        for (let q of elite_qs) {
            elite_qids.add(q.id);
        }
    }
    for (let kno of knows) {
        let qs = kno.questions;
        for (let q of qs) {
            q['review_elite'] = elite_qids.has(q.id) ? 'elite' : 'unelite';
        }
    }
}

class KnoQuesIter {
    constructor(knows) {
        this.kindex = 0;
        this.knows = knows;
        this.traceLog = {};
    }

    nextQues() {
        let finished = true;
        //是否已经全部试题访问完毕
        for (let i = 0; i < this.knows.length; i++) {
            let _qs = this.knows[i].questions;
            let _q_max = this.traceLog[i] || 0;
            if (_q_max < _qs.length) {
                finished = false;
                break;
            }
        }
        if (finished) {
            return null;
        }
        let k_len = this.knows.length;
        let qs = this.knows[this.kindex].questions || [];
        let q_max = this.traceLog[this.kindex] || 0;
        while (q_max >= qs.length) {
            this.kindex = (this.kindex + 1) % k_len;
            q_max = this.traceLog[this.kindex] || 0;
            qs = this.knows[this.kindex].questions || [];
        }
        this.traceLog[this.kindex] = q_max + 1;
        this.kindex = (this.kindex + 1) % k_len;
        return qs[q_max];
    }
}

function union_ques(knows) {
    if (!knows.length) {
        return [];
    }
    if (knows.length === 1) {
        return knows[0].questions;
    }
    // 均匀合并知识点的试题
    let qs = [];
    let q_que = new KnoQuesIter(knows);
    let q = q_que.nextQues();
    let buf_ids = {};
    while (q) {
        if (!buf_ids[q.id]) {
            qs.push(q);
            buf_ids[q.id] = 1;
        }
        q = q_que.nextQues();
    }
    return qs;
}

/**
 * 过滤试题 id
 * @param {*} knows
 * @param {*} ques_tag
 * @param {*} diffs
 * @param {*} _type
 * @param {*} ass_kid
 * @param {*} province
 * @param {*} knowledge_ids
 * @param {*} city
 * @param {*} year
 * @param {*} grade
 * @param {*} elite
 * @param {*} device
 * @param {*} category
 * @param {*} mult_know_use
 * @param {*} total
 * @return {Promise<{q_pid, q_list: Array}>}
 */
async function filter_ques(knows, {
    ques_tag, diffs, _type, ass_kid, province, knowledge_ids,
    city, year, grade, elite, device, category, mult_know_use, total
}) {
    let q_list = [], q_pid = {};  // 记录试题对应的目标试卷id;
    let qs = total ? _.flatten(knows.map(e => e.questions)) : union_ques(knows, total);
    if (mult_know_use === 'intersection') {
        qs = _.filter(qs, (q) => {
            let inter_kns = _.intersection(knowledge_ids, q.kns);
            inter_kns = _.uniq(inter_kns);
            return inter_kns.length === knowledge_ids.length;
        });
    }
    // 计算试题平均引用数
    let thre = 0;
    for (let q of qs) {
        thre += q.papers.length;
    }
    if (qs && qs.length) {
        thre = thre / qs.length;
    }
    // 过滤
    for (let q of qs) {
        if (null != ques_tag) {
            if (('elite' === ques_tag || 'unelite' === ques_tag) &&
                (ques_tag !== q.review_elite)) { // 教研精品属性
                continue;
            } else if (('sch_elite' === ques_tag) &&
                (1 !== q.iselt)) { // 名校精品
                continue;
            } else if (('final' === ques_tag) &&
                (q.isfnl !== 1)) { // 压轴题
                continue;
            } else if (('common' === ques_tag) &&
                (q.papers.length < thre)) { // 常考题
                continue;
            }
        }
        if (diffs && !diffs.has(q.diff)) {
            continue;
        }
        if (_type && q.type !== _type) {
            continue;
        }
        if (ass_kid) {
            let kns = q.kns;
            if (!kns ||
                kns.length === 0 ||
                kns.indexOf(ass_kid) < 0) {
                continue;
            }
        }
        if (category || province || city || year || 'sch_elite' === ques_tag) {
            let chosen = false;
            let papers = q.papers;
            if (year) {
                if (!papers || 0 === papers.length) {
                    continue;
                }
                if (year !== papers[0].year) {
                    continue;
                }
            }
            for (let p of papers) {
                if ((!province || p.pr === province) &&
                    (!city || p.city === city) &&
                    (!category || p.cat === category) &&
                    ('sch_elite' !== ques_tag || p.sch_name)) {
                    q_pid[q.id] = p.id;
                    q.sco = p.sco;
                    q.year = p.year;
                    chosen = true;
                    break;
                }
            }
            if (!chosen) {
                continue;
            }
        }
        // 学科为英语的试题增加年级筛选 七年级上，高一上等
        if (grade && grade !== q.grade) {
            continue;
        }
        q_list.push(q);
    }
    // 筛选结果去重
    if (_.isArray(knows) && knows.length > 1) {
        let idMap = {};
        for (let i = 0; i < q_list.length; i++) {
            idMap[q_list[i].id] = q_list[i];
        }
        q_list = Object.keys(idMap).map(id => idMap[id]);
    }
    // 过滤精品题
    // '-1' - 未编题
    // '0'  - 非精品
    // '1'  - 精品
    // 试题 elite 字段可能为空
    if (elite) {
        // 如果试题不存在 elite 字段，知识点下试题 iselt = -1
        q_list = q_list.filter(e => ({
            '-1': -1,
            '0': 0,
            '1': 1,
        }[+elite] === e['iselt']));
    }
    return { q_list, q_pid };
}

async function sort_ques(q_list, knows, {
    offset, limit, sort_by,
    province, category, ques_tag, mult_know_use, total
}) {
    // sort data
    let _sort = function (t, reverse) {
        // reverse: 1-正序，2-倒序
        return function (x, y) {
            let result = x[t] - y[t];
            return result === 0 ? y.sco - x.sco : reverse * result;
        };
    };
    if (sort_by === 'use_times') {
        let q_ids = _.pluck(q_list, 'id');
        let cond = { _id: { $in: q_ids } };
        let proj = { use_times: 1 };
        let _ques = await db.collection('question').find(cond, proj).toArray();
        let _buf = {};
        _.each(_ques, q => _buf[q._id] = (q.use_times || 0));
        q_list.sort((x, y) => _buf[y.id] - _buf[x.id]);
    } else if (null != sort_by && sort_by !== 'year') {
        q_list.sort(_sort(sort_by, -1));
    } else if ((null != province || null != category || 'sch_elite' === ques_tag) && sort_by === 'year') {
        q_list.sort(_sort('year', -1));
    } else if ('union' === mult_know_use && _.isArray(knows) && knows.length > 1) {
        // 多知识点关联性排序
        let baseWeight = 100;
        let knowledgeIds = _.pluck(knows, '_id');
        let knowledgeMatchMap = knowledgeIds.sort((a, b) => a - b).reduce((obj, id) => {
            obj[id] = ++baseWeight;
            return obj;
        }, {});
        q_list.forEach(e => {
            e.knowledgeWeight = _.isArray(e.kns) ? e.kns.reduce((pv, id) => pv + (knowledgeMatchMap[id] || -0.1), 0) : 0;
        });
        q_list.sort((a, b) => b.knowledgeWeight - a.knowledgeWeight);
    }
    if (!total && q_list.length > 3000) {
        q_list.splice(3000);
    }
    let total_num = q_list.length;
    q_list = q_list.slice(offset, offset + limit);
    let qids = [];
    for (let q of q_list) {
        qids.push(q.id);
    }
    return { qids, total_num };
}

async function _query_ques_by_ids(
    qids,
    { fields_type, device, ques_tag, province, city, category, diffs },
    out_of_limits, q_pid) {

    let _query_questions = util.promisify(query_questions);
    let cond = { _id: { $in: qids } };
    let qs = await _query_questions(cond, fields_type, device, out_of_limits, null);
    // for (let ques of qs) {
    //     if (!ques.elite) {
    //         ques.elite = 0;
    //     }
    // }
    // 调整refer_exampapers
    if (category || province || city || 'sch_elite' === ques_tag) {
        for (let q of qs) {
            if (!q_pid.hasOwnProperty(q.id)) {
                continue;
            }
            let papers = q.refer_exampapers;
            let _papers = [0];
            let p_id = q_pid[q.id];
            for (let p of papers) {
                if (p.id === p_id) {
                    _papers[0] = p;
                } else {
                    _papers.push(p);
                }
            }
            if (0 === _papers[0]) {
                _papers.shift();
            }
            q.refer_exampapers = _papers;
        }
    }
    //重置试题难度
    if (diffs) {
        let diff = Array.from(diffs);
        if (diff && diff.length > 0) {
            for (let a = 0; a < qs.length; a++)
                qs[a].difficulty = diff[0];
        }
    }

    return qs;
}

async function update_mobile_ready(questions) {
    let questionsIds = _.pluck(questions, 'id');
    let cond = { _id: { $in: questionsIds } };
    let project = { _id: 1 };
    let _questions = await db.collection('question_mobile').find(cond, project).toArray();
    for (let question of questions) {
        question.mobile_ready = false;
    }
    let questionIds = _.pluck(_questions, '_id');

    for (let question of questions) {
        if (questionIds.indexOf(question.id) >= 0) {
            question.mobile_ready = true;
        } else {
            question.mobile_ready = false;
        }
    }
}

//对来源serv_range=public情况下，过滤来自标注平台的试卷
function filterPublicRefExampaper(params, questions, query) {
    if (params.serv_range === 'public') {
        for (let question of questions) {
            _filterReferExampapers(question, query);
        }
    }
}
async function knowledge_questions(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let fun_name = arguments.callee.name;
    let params = params_utils.create_params(req, kb_api[fun_name]);

    // 'all', 表示返回全部试题；
    // 'elite', 表示返回"教研标注的精品题"；
    // 'unelite', 表示返回"非教研标注的试题"；
    // 'sch_elite', 表示返回"名校精品题"；
    // 'common', 表示返回"常考试题"；
    // 'final', 表示返回"压轴题"；
    let ques_tag = params.category;          // 试题标签：
    let kids = params.knowledge_ids;         //知识点数组
    let category = params.exam_type;
    let diffs = params.difficulty;
    let _type = params.type;
    // 针对历史的部分字段进行修整
    params = { ...params, ques_tag, category, _type, diffs };

    //多知识点随机试题
    if (kids.length > 1 && ['union', 'intersection'].indexOf(params['mult_know_use']) === -1) {
        return getKnowledgesQuestions(req, res);
    }

    // 数据查询 ----------------------------
    let knowledges = await get_cache_knowledges(kids, params['cache_key']);
    if (!knowledges || !knowledges.length) {
        return responseWrapper.error('NULL_ERROR');
    }
    // 知识点 权限过滤
    const queryDbs = new Set((req.query.dbs || 'basic').split(','));
    knowledges = knowledges.filter(knowledge => (knowledge.dbs || ['basic']).some(item => queryDbs.has(item)));
    if (knowledges.length === 0) {
        return responseWrapper.error('NULL_ERROR');
    }

    // 获取精品题数据
    await check_elite_ques(knowledges, params['ques_tag']);

    // 限制tiku访问错题本的试卷（2020年10月16日，已放开限制）
    // getPublicQuestions(knowledges, req.query.serv_range === 'public');

    // 过滤试题
    let { q_list, q_pid } = await filter_ques(knowledges, params);

    // 试题排序
    let { qids, total_num } = await sort_ques(q_list, knowledges, params);

    // 通过试题ids获取试题相信信息
    let questions = await _query_ques_by_ids(qids, params, req.out_of_limits, q_pid);

    //对来源serv_range=public情况下，过滤来自标注平台的试卷
    filterPublicRefExampaper(params, questions, req.query);

    if (!questions.length) {
        return responseWrapper.error('NULL_ERROR');
    }
    let result = { total_num, questions };

    //原有单个知识点搜索返回关联知识点
    if (knowledges.length === 1) {
        result.associate_knowledges = knowledges[0].associate_knowledges;
        result.single_assess = knowledges[0].single_assess;
    }

    // if (params['mobile_ready']) {
    //     await update_mobile_ready(questions);
    // }
    responseWrapper.succ(result);
    // 试题移动化
    // questionMobile.updateQuestionMobileByQuesion(questions, params.device);
}

async function _knowledge_questions(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    try {
        await knowledge_questions(req, res);
    } catch (err) {
        responseWrapper.error('HANDLE_ERROR', err.message || err);
    }
}

const deleteQuestionBlocks = (question) => {
    if (question && question.blocks) {
        if (typeof question.blocks === 'object') {
            delete question.blocks.solutions;
            delete question.blocks.answers;
            delete question.blocks.explanations;
            question.freq = '今日浏览次数超限，无法查看此处内容';
        }
    }
};

function _filterReferExampapers(question, query) {
    try {
        question.refer_exampapers = _.filter(question.refer_exampapers, (x) => {
            let result = ['mt'].indexOf(x.from) < 0;
            if (result) {
                x.vague_name = filterExampaperName(x);
                x.name = mapExampaperName(x, query);
            }
            return result;
        });
    } catch (err) {

    }
}

/*
 * 函数描述:
 *         基于试题id，请求试题信息
 * URL:
 *         http://kboe.yunxiao.com/kb_api/v2/questions/{question_id}/
 * Method:
 *         GET
 * Author:
 *         zhangjun
 * Date:
 *         2016-08-18
 */
function question(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
    const queryDbs = new Set((req.query.dbs || 'basic').split(','));

    Thenjs(function (cont) {
        // 查询条件
        let qid = params['question_id'];
        let cond = { '_id': { '$in': [qid] } };
        let fields_type = params['fields_type'];
        // 查询
        let device = params['device'];
        query_questions(cond, fields_type, device, req.out_of_limits, queryDbs, async (err, result) => {
            if (err) {
                return cont(err);
            }
            if (result.length > 0) {
                // 2020年10月16日已放开限制
                /* if (req.query.serv_range === 'public' && ['mt'].indexOf(result[0].from) >= 0) {
                    return responseWrapper.error('NULL_ERROR');
                } */
                // 超限引发的不适
                if (req.query.freq && req.query.device !== 'mobile') {
                    deleteQuestionBlocks(result[0]);
                }
                if (req.query.serv_range === 'public') {
                    _filterReferExampapers(result[0], req.query);
                }
                if (req.query.request_landmark && result[0].landmark_point_ids && Array.isArray(result[0].landmark_point_ids)) {
                    await _filterLandmarkPoints(result[0]);
                }
                return responseWrapper.succ(result[0]);
            }
            if (res.timeout === true && Array.isArray(res.timestamp)) {
                Logger.warn({
                    who: req.query.api_key,
                    where: `${req.originalUrl}`,
                    what: 'TIMEOUT',
                    why: res.timestamp
                });
            }
            return responseWrapper.error('NULL_ERROR');
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

const _filterLandmarkPoints = async (resultData) => {
    let landmarkCond = { _id: { $in: resultData.landmark_point_ids } };
    let proj = { name: 1, invalid: 1 }
    let rawData = await db.collection('landmark_point').find(landmarkCond).project(proj).toArray();
    let landmarkPoints = [];
    for (let data of rawData) {
        if (data.invalid === 0) {
            data['id'] = data._id;
            delete data._id;
            delete data.invalid;
            landmarkPoints.push(data);
        }
    }
    resultData['landmark_points'] = landmarkPoints;
};


/*
 * 函数描述：
 *    获取试题的统计信息
 * URI:
 *    /kb_api/v2/questions/profile/
 * Method:
 *   GET
 */
function profile(req, res) {
    req.query.resource = 'question';
    return resource.profile_single(req, res);
}

/*
 * 函数描述：
 *    获取试题筛选项数据
 * URI:
 *     /kb_api/v2/questions/filters/
 * Method:
 *    GET
 */
function filters(req, res) {
    req.query.resource = 'question';
    return resource.filters(req, res);
}

/*
 * 函数描述:
 *         获取试题详细详情
 * URL:
 *         http://kboe.gotiku.com/kboe/v1/questions/
 * Method:
 *         GET
 * Author:
 *         zhangjun
 * Date:
 *         2016-06-21
 */
function get_questions(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    const queryDbs = new Set((req.query.dbs || 'basic').split(','));

    Thenjs(function (cont) {
        // 数据查询 ----------------------------
        // 查询条件
        let cond = { '_id': { '$in': params['question_ids'] } };
        let fields_type = params['fields_type'];
        // 查询
        let device = req.inner_visit ? params['device'] : 'pc';
        query_questions(cond, fields_type, device, req.out_of_limits, queryDbs, function (err, result) {
            if (err) {
                return cont(err);
            }
            if (result.length > 0) {
                return responseWrapper.succ(result);
            }
            return responseWrapper.error('NULL_ERROR');


        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

/**
 * 批量获取试题
 * @param {*} req
 * @param {*} res
 */
const post_questions = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    const isMobileReady = (req.query.mobile_ready === 'true');
    try {
        let params = params_utils.create_params(req, kb_api['post_questions']);
        let question_ids = params['question_ids'].slice(0, 200);
        let cond = { _id: { $in: question_ids } };
        let fields_type = params['fields_type'];
        let device = req.inner_visit ? params['device'] : 'pc';
        const queryDbs = new Set((req.body.dbs || ['basic']));
        // 试题移动化
        // process.nextTick(function () {
        //     questionMobile.updateQuestionMobile(cond, device);
        // });
        // pc端请求,要检查移动端数据是否准备ok
        if (isMobileReady && (device === 'pc')) {
            let questionFn = query_questions_lite(cond, fields_type, device, req.out_of_limits, queryDbs);
            let condQM = { _id: { $in: question_ids } };
            let proQM = { _id: 1 };
            // let questionMobilesFn = db.collection('question_mobile').find(condQM).project(proQM).toArray();

            let [result] = await Promise.all([questionFn]);

            if (result.length > 0) {
                // 试题附加 mobile_ready 字段
                // let quesMobileIds = _.pluck(questionMobiles, '_id');
                for (let q of result) {
                    // q.mobile_ready = quesMobileIds.includes(q.id);
                    if (Array.isArray(q.refer_exampapers) && q.refer_exampapers.length > 0) {
                        for (let exam of q.refer_exampapers) {
                            exam.vague_name = filterExampaperName(exam);
                            exam.name = mapExampaperName(exam, req.query);
                        }
                    }
                }
            }
            responseWrapper.succ(result);
        } else {
            let result = await query_questions_lite(cond, fields_type, device, req.out_of_limits, queryDbs);
            if (result.length > 0) {
                for (let q of result) {
                    if (Array.isArray(q.refer_exampapers) && q.refer_exampapers.length > 0) {
                        for (let exam of q.refer_exampapers) {
                            exam.vague_name = filterExampaperName(exam);
                            exam.name = mapExampaperName(exam, req.query);
                        }
                    }
                }
                if (req.body.request_landmark) {
                    for (let resData of result) {
                        if (resData.landmark_point_ids && resData.landmark_point_ids.length > 0) {
                            let landmarkCond = {
                                _id: {
                                    $in: resData.landmark_point_ids
                                }
                            };
                            let proj = { name: 1, invalid: 1 };
                            let rawData = await db.collection('landmark_point').find(landmarkCond).project(proj).toArray();
                            let landmarkPoints = [];
                            for (let data of rawData) {
                                if (data.invalid === 0) {
                                    delete data.invalid;
                                    data['id'] = data._id;
                                    delete data._id;
                                    landmarkPoints.push(data);
                                }
                            }
                            resData['landmark_points'] = landmarkPoints;
                        }
                    }
                }
                if (!isMobileReady) {
                    return responseWrapper.succ(result);
                }
                // if (isMobileReady && (device === 'mobile')) {
                //     for (let q of result) {
                //         q.mobile_ready = true;
                //     }
                //     return responseWrapper.succ(result);
                // }
                responseWrapper.succ(result);
            } else {
                responseWrapper.error('NULL_ERROR');
            }
        }
    } catch (e) {
        Logger.error(e);
        responseWrapper.error('PARAMETERS_ERROR', e.message);
    }
};

/**
 * Desc: desktop app 专用格式转换
 * Author: cuiyunfeng
 * @param {Object} questions
 */
function desktopQuestions(questions) {
    if (!questions) {
        return;
    }
    for (let ix in questions) {
        let question = questions[ix];
        if (question && question.blocks) {
            for (let i in question.blocks.answers) {
                let answer = question.blocks.answers[i];
                if (typeof answer == 'string') {
                    question.blocks.answers[i] = [answer];
                }
            }
        }
    }
}

/*
 * Desc:
 *      查询试题数据公共函数
 * Params:
 *      - comment
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-19
*/
function query_questions(cond, fields_type, device, out_of_limits, dbs, callback) {
    // 根据device选取不同的collection
    let coll = db.collection('question');
    // if ('mobile' === device) {
    //     // updateQuestionMobile(cond, device);
    //     coll = db.collection('question_mobile');
    // } else {
    //     coll = db.collection('question');
    // }
    let q_ids = cond['_id']['$in'];
    let ids_grp = ids_split(q_ids, 40);
    if (!dbs) {
        dbs = new Set(['basic']);
    }


    // 数据查询 ----------------------------
    Thenjs.each(ids_grp, function (cont, ids) {
        let _cond = cond;
        _cond['_id'] = { '$in': ids };
        // 提取出的字段
        let proj = {
            _id: 1,
            comment: 1,
            blocks: 1,
            description: 1,
            type: 1,
            knowledges: 1,
            from: 1,
            period: 1,
            subject: 1,
            difficulty: 1,
            refer_exampapers: 1,
            refer_times: 1,
            use_type: 1,
            core_knowledges: 1,
            elite: 1,
            reco_questions: 1,
            use_times: 1,
            ctime: 1,
            tags: 1,
            type_tags: 1,
            year: 1,
            elements: 1,
            attach: 1,
            inspect: 1,
            landmark_point_ids: 1,
            landmark_reco_questions: 1,
            dmp_cite: 1,
            dbs: 1,
        };
        // 查询
        coll.find(cond).project(proj).toArray(function (err, items) {
            if (err) {
                return cont(err);
            }
            items = (items || []).filter(ques => (ques.dbs || ['basic']).some(item => dbs.has(item)));
            return cont(null, items);
        });
    }).then(function (cont, result) {
        let d = {};
        for (let k in result) {
            let items = result[k];
            // 将查询到的数组转为Map, key:id, value:ques
            for (let i in items) {
                let item = items[i];
                let id = item._id;
                d[id] = item;
                utils.filter_fields(item, fields_type);
            }
        }
        // 生成返回数据
        result = [];
        let n = q_ids.length;
        for (let i in q_ids) {
            let id = q_ids[i];
            let ques = d[id];
            if (ques) {
                result.push(ques);
            }
            if (--n === 0) {
                return cont(null, result);
            }
        }
        return cont(null, result);
    }).then(function (cont, queses) {
        let know_ids = [];
        for (let i in queses) {
            let ques = queses[i];
            let knows = ques.knowledges;
            for (let j in knows) {
                know_ids.push(knows[j].id);
            }
            ques.year = ques.year || new Date(ques.ctime).getFullYear();
        }
        get_knowledges_videos(know_ids, function (err, know_videos) {
            if (err) {
                return cont(err);
            }
            for (let i in queses) {
                let ques = queses[i];
                let knows = ques.knowledges;
                for (let j in knows) {
                    let know_id = knows[j].id;
                    let kn = know_videos[know_id] || { videos: [], chance: 0, score: 0, name: '' };
                    knows[j].videos = kn.videos;
                    knows[j].chance = kn.chance;
                    knows[j].score = kn.score;
                    knows[j].name = kn.name;
                }
            }
            return cont(null, queses);
        });
    }).then(function (cont, result) {
        if (out_of_limits) {
            dirty_qs(result, device);
        }
        // 处理
        if ('desktop' === device) {
            desktopQuestions(result);
        }
        if ('mobile' !== device && 'desktop' !== device) {
            compatible_question(result);
        }
        return callback(null, result);
    }).fail(function (cont, error) {
        return callback(error);
    }).finally(function (cont, error) {
        Logger.error(error);
        // return responseWrapper.error('HANDLE_ERROR');
    });
}

function formatResult(result, q_ids, fields_type) {
    let questions = [];
    let d = {};
    for (let items of result) {
        // 将查询到的数组转为Map, key:id, value:ques
        for (let ques of items) {
            ques.year = ques.year || new Date(ques.ctime).getFullYear();
            d[ques._id] = ques;
            utils.filter_fields(ques, fields_type);
        }
    }
    // 生成返回数据
    for (let id of q_ids) {
        if (d[id]) {
            questions.push(d[id]);
        }
    }
    return questions;
}

const query_questions_lite = async (cond, fields_type, device, out_of_limits, dbs) => {
    // let collName = ('mobile' === device) ? 'question_mobile' : 'question';
    let collName = 'question';
    let coll = db.collection(collName);
    let q_ids = cond['_id']['$in'];
    let ids_grp = ids_split(q_ids, 30);
    if (!dbs) {
        dbs = new Set(['basic']);
    }
    // 数据查询
    let execArr = [];
    // 提取出的字段
    let proj = {
        _id: 1,
        comment: 1,
        blocks: 1,
        description: 1,
        type: 1,
        knowledges: 1,
        from: 1,
        period: 1,
        subject: 1,
        difficulty: 1,
        refer_exampapers: 1,
        refer_times: 1,
        use_type: 1,
        core_knowledges: 1,
        elite: 1,
        reco_questions: 1,
        use_times: 1,
        ctime: 1,
        dmp_video: 1,
        tags: 1,
        type_tags: 1,
        year: 1,
        elements: 1,
        attach: 1,
        inspect: 1,
        landmark_point_ids: 1,
        dmp_cite: 1,
        landmark_reco_questions: 1,
        dbs: 1,
    };
    // dmp精编题库用，特有字段
    if ('full_dmp' === fields_type) {
        proj.dmp_statis = 1;
        proj.dmp_cite = 1;
    }

    for (let i = 0; i < ids_grp.length; i++) {
        let ids = ids_grp[i];
        let _cond = cond;
        _cond['_id'] = { '$in': ids };
        let fun = coll.find(_cond, { readPreference: 'secondaryPreferred' }).project(proj).toArray();
        execArr.push(fun);
    }
    let result = await Promise.all(execArr);
    let questionArr = formatResult(result, q_ids, fields_type);
    // 试题权限过滤
    questionArr = (questionArr || []).filter(ques => (ques.dbs || ['basic']).some(item => dbs.has(item)));
    // 获取知识点视频
    let know_ids = utils.getKnowIds(questionArr);
    let know_videos = await get_knowledges_videos_by_id(know_ids);
    utils.setVideo(questionArr, know_videos);

    if (out_of_limits) {
        dirty_qs(questionArr, device);
    }
    // utils.knowNameNull(questionArr);

    if ('desktop' === device) {
        desktopQuestions(questionArr);
    }
    if ('mobile' !== device && 'desktop' !== device) {
        compatible_question(questionArr);
    }
    return questionArr;
};

/*
 * Detail:
 *         过滤试题的相关字段
 * Params:
 *         json - 试题对象
 *         fields_type - 0返回全部域；1仅返回题干域；2仅返回解答相关域；
 * Author:
 *         zhangjun
 * Date:
 *         2017-06-06
 */
function get_knowledges_videos(know_ids, callback) {
    let know_videos = {};
    for (let i in know_ids) {
        let know_id = know_ids[i];
        know_videos[know_id] = [];
    }
    // 数据查询 ----------------------------
    let ids_grp = ids_split(know_ids, 40);
    let coll = db.collection('knowledge');
    Thenjs.each(ids_grp, function (cont, ids) {
        // 查询条件
        let cond = { '_id': { '$in': ids } };
        // 提取出的字段
        let proj = { _id: 1, 'videos': 1, 'chance': 1, 'score': 1, 'name': 1 };
        coll.find(cond).project(proj).toArray(function (err, items) {
            if (err) {
                return cont(err);
            }
            return cont(null, items);
        });
    }).then(function (cont, result) {
        for (let k in result) {
            let items = result[k];
            // 将查询到的数组转为Map, key:id, value:k_info
            for (let i in items) {
                let item = items[i];
                let id = item._id;
                item.videos = item.videos ? item.videos : [];
                know_videos[id] = item;
            }
        }
        return callback(null, know_videos);
    }).fail(function (cont, err) {
        return callback(err);
    }).finally(function (cont, err) {
        return callback(err);
    });
}

// 查询知识点
const get_knowledges_videos_by_id = async (know_ids) => {
    let know_videos = {};
    let proj = { _id: 1, videos: 1, chance: 1, score: 1, name: 1 };
    let ids_grp = ids_split(know_ids, 40);
    let coll = db.collection('knowledge');
    let execFuArr = [];
    for (let i = 0; i < ids_grp.length; i++) {
        let ids = ids_grp[i];
        let cond = { _id: { '$in': ids } };
        let fun = coll.find(cond).project(proj).toArray();
        execFuArr.push(fun);
    }
    let result = await Promise.all(execFuArr);
    for (let items of result) {
        // 将查询到的数组转为Map, key:id, value:k_info
        for (let item of items) {
            item.videos = item.videos ? item.videos : [];
            know_videos[item._id] = item;
        }
    }
    return know_videos;
};

function asyncUpdateQuestionValidate(id) {
    let coll = db.collection('question');
    let cond = { _id: id };
    coll.findOne(cond, (err, question) => {
        if (err || !question) {
            return;
        }
        if (question.hasOwnProperty('invalid')) {
            coll.updateOne(cond, { $set: { invalid: 0 } }, () => {
            });
        }
    });
}
const updateQuestionLabel = async (id, params) => {
    try {
        let data = {
            status: 1,
            elements: params.elements,
            knowledges: params.knowledges,
            utime: new Date(),
            plat_status: 1
        };
        await db.collection('question_label').update({ _id: id }, { $set: data });
    } catch (e) {
        Logger.error(e);
    }
};
/*
 * 函数描述:
 *         修改试题内容
 * URL:
 *         http://kboe.yunxiao.com/kb_api/v2/question
 * Method:
 *         PUT
 * Author:
 *         zhangjun
 * Date:
 *         2016-10-24
 */
function modify_question(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);

    } catch (e) {
        Logger.error(e);
        return responseWrapper.error('PARAMETERS_ERROR', e.message || e);
    }
    const dbs = (req.query.dbs || 'basic').split(',');

    // 数据查询 ----------------------------
    Thenjs(function (cont) {
        let coll = db.collection('question');
        // 查询条件
        let cond = { '_id': params['question_id'], dbs: { $in: dbs } };
        // 设置内容
        let fields = params['fields'];
        // 仅 kbp 平台可以修改dbs
        if (!dbs.includes('kbp')) {
            delete fields.dbs;
        }
        if (fields.hasOwnProperty('difficulty')) {
            let difficulty = fields['difficulty'];
            if (!isNaN(difficulty) && difficulty >= 1 && difficulty <= 5) {
                let difficulty_arry = [0.9, 0.75, 0.6, 0.4, 0.1];
                difficulty = difficulty_arry[parseInt(difficulty) - 1];
            }
            fields['difficulty'] = difficulty;
            // let diff = (5 - fields['difficulty']) / 5;
            // diff = Math.round(parseFloat(diff) * 100) / 100;
            // fields['difficulty'] = diff;
        }
        fields['difficulty'] = fields['difficulty'] || 0.01;
        fields['has_modified'] = 1;
        fields['utime'] = new Date();

        // 校验相似题
        if (fields['reco_questions']) {
            let err = null;
            if (!_.isArray(fields['reco_questions'])) {
                err = '创建试题相似题 (reco_questions) 字段需为数组形式';
            } else {
                // 支持相似题列表存在 Object 的形式 - 从 Object 中尝试提取 ID
                fields['reco_questions'] = fields['reco_questions'].map(e => {
                    if (_.isObject(e)) return e.id;
                    return e;
                });
                fields['reco_questions'].forEach(id => {
                    if (!_.isNumber(id)) err = `创建试题相似题 (reco_questions) 数组中存在不为数字类型的ID ${id}`;
                });
            }
            if (err) {
                return responseWrapper.error('PARAMETERS_ERROR', err);
            }
        }
        getSubQuesCountAndOptionsCount(fields);
        _removeQuestionStyle(fields);
        if (fields['elite'] === -1) {
            delete fields['elite'];
        }
        // 过滤标签，补齐字段
        fields = filterQuestion(fields);
        let update_fields = {
            '$set': fields,
            '$unset': { inspect: true }
        };
        // 更新
        coll.updateOne(cond, update_fields, function (err, result) {
            if (err) {
                return cont(err);
            }
            asyncUpdateQuestionValidate(cond._id);
            if (result.result.n !== 1) {
                return responseWrapper.error('PARAMETERS_ERROR', '查不到该条记录');
            }
            //编辑试题之后会强制更新该试题的题干图片内容
            imageUtils.batchUpdateQuestionImges([cond._id], { force: 1 });
            updateQuestionLabel(params['question_id'], fields).then(() => { });
            return responseWrapper.succ({});
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}


/*
 * Desc:
 *      dirty questions
 * Params:
 *      qs - question list
 * Return:
 *
 * Author:
 *      zhangjun
 * Date:
 *      2017-04-06
*/
function dirty_qs(qs, device) {
    if (!qs || 0 === qs.length) {
        return;
    }
    if ('mobile' === device) {
        return;
    }
    for (let i in qs) {
        let q = qs[i];
        dirty_q(q);
    }

}

function dirty_q(q) {
    function disorder_opt(num, options) {
        let arr = [];
        for (let k in options) {
            let remainder = num & 0x7;
            num = num >> 2;
            arr.push([remainder, k]);
        }
        arr.sort();
        let _opts = {};
        let i = 0;
        for (let k in options) {
            let new_k = arr[i][1];
            _opts[k] = options[new_k];
            i++;
        }
        return _opts;
    }

    let blocks = q.blocks;
    // pollute options
    let magic_num = ((q.id & 8191) * 277) & 8191; // 277 is a prime number, 8192=2^13
    let stems = blocks.stems;
    for (let i in stems) {
        let options = stems[i].options;
        if (options) {
            stems[i].options = disorder_opt(magic_num, options);
        }
    }

    // pollute explanations, solutions, answers
    function pollute_fields(fields) {
        for (let i in fields) {
            let field = fields[i];
            if (field instanceof Array) {
                pollute_fields(field);
            } else {
                fields[i] = dirty_string(field, 3);
            }
        }
    }

    let keys = ['explanations', 'solutions', 'answers'];
    for (let i in keys) {
        let key = keys[i];
        let fields = blocks[key];
        pollute_fields(fields);
    }
    // pollute knowledges
    let kns = q.knowledges;
    for (let i in kns) {
        let kn = kns[i];
        kn.id = dirty_id(kn.id, 8191);  // 8192=2^13
        kn.name = dirty_string(kn.name, 3);
    }
}

function getQuestionStemImage(question_part) {
    return function (req, res) {
        let responseWrapper = new ResponseWrapper(res);
        let question_ids = req.params.question_ids.split(',');
        question_ids = _.map(question_ids, (id) => id * 1);
        question_ids = _.filter(question_ids);
        let cond = { _id: { $in: question_ids } };
        let questionCol = db.collection('question');
        questionCol.find(cond).toArray((err, questions) => {
            if (err) {
                return responseWrapper.error('HANLDE_ERROR', err.message);
            }
            let retArray = _.map(questions, function (question) {
                return {
                    id: question._id,
                    url: question[question_part] || ''
                };
            });
            retArray = _.filter(retArray, function (item) {
                return item.url;
            });
            responseWrapper.succ(retArray);
            //异步判断更新一下对应的字段
            imageUtils.batchUpdateQuestionImges(question_ids, { force: 0 });
        });
    };
}

/**
 * Author: YuLei
 * Description: 挑选出一些试题
 * @param {*} knowledges
 * @param {*} question
 * @param {*} num
 * @return {{qids: Array, totalNum: number}}
 * @private
 */
function _pickSomeQuestions(knowledges, question, num, changeTimes) {
    //获取所有符合条件的试题
    let knowToQuesArray = _.pluck(knowledges, 'questions');
    let allQuestions = knowToQuesArray[0];
    for (let index = 1; index < knowToQuesArray.length; index++)
        allQuestions = _.union(allQuestions, knowToQuesArray[index]);
    allQuestions = _.without(allQuestions, question._id);
    let tmpQuestions = [];
    for (let i = 0; i < allQuestions.length; i++) {
        if (allQuestions[i].id !== question._id)
            tmpQuestions.push(allQuestions[i]);
    }
    allQuestions = tmpQuestions;
    let total = allQuestions.length;
    let maxChangeTimes = Math.ceil(allQuestions.length / num);

    //根据更换次数获取试题
    let questions = [];
    if (changeTimes === 0 || changeTimes >= maxChangeTimes) {
        for (let a = 0; a < num; a++) {
            if (allQuestions.length > 0) {
                let random = Math.floor(Math.random() * allQuestions.length);
                if (question._id !== allQuestions[random].id)
                    questions.push(allQuestions[random].id);
                allQuestions.splice(random, 1);
            }
        }
    } else {
        let index = new Date().getSeconds() % 10;
        let startIndex = (changeTimes - 1) * 10;
        let endIndex = (changeTimes * 10 + index) > (total - 1) ? (total - 1) : (changeTimes * 10 + index);
        let rangeQuestions = allQuestions.slice(startIndex, endIndex);
        for (let a = 0; a < num; a++) {
            if (rangeQuestions.length > 0) {
                let random = Math.floor(Math.random() * rangeQuestions.length);
                if (question._id !== allQuestions[random].id)
                    questions.push(rangeQuestions[random].id);
                rangeQuestions.splice(random, 1);
            }
        }
    }
    return { totalNum: total, qids: questions };
}

function _pickAnotherQuestion(knowledges, question) {
    let knowToQuesArray = _.pluck(knowledges, 'questions');
    let interArray = knowToQuesArray[0];
    for (let index = 1; index < knowToQuesArray.length; ++index) {
        interArray = _.intersection(interArray, knowToQuesArray[index]);
    }
    interArray = _.without(interArray, question._id);
    if (interArray.length > 0) {
        return interArray[Math.floor(Math.random() * interArray.length)].id;
    }
    let unionArray = knowToQuesArray[0];
    for (let index = 1; index < knowToQuesArray.length; ++index) {
        unionArray = _.union(unionArray, knowToQuesArray[index]);
    }
    unionArray = _.without(unionArray, question._id);
    if (unionArray.length > 0) {
        return unionArray[Math.floor(Math.random() * unionArray.length)].id;
    }
    return -1;
}

function _getDiffRange(difficulty) {

    // 转换难度
    let _diff = 10 - parseInt(difficulty * 10);
    if (_diff > 9) {
        _diff = 9;
    }
    if (_diff < 1) {
        _diff = 1;
    }
    _diff = Math.round(_diff);
    let comparableDiff = (parseInt(_diff / 2) === 0) ? _diff + 1 : _diff - 1;
    return [_diff, comparableDiff];
}

function _getDiffRangeNew(diff) {
    let _diff = parseFloat(diff);
    if (_diff >= 0.0 && _diff < 0.35)
        return [5];
    else if (_diff >= 0.35 && _diff < 0.55)
        return [4];
    else if (_diff >= 0.55 && _diff < 0.7)
        return [3];
    else if (_diff >= 0.7 && _diff < 0.85)
        return [2];
    else if (_diff >= 0.85 && _diff < 1.0)
        return [1];
}

function _filterQuestions(knowledges, type, diffRange) {
    for (let index = 0; index < knowledges.length; index++) {
        let curKnowledge = knowledges[index];
        curKnowledge.questions = _.filter(curKnowledge.questions, function (question) {
            return ((question.type === type) && (diffRange.indexOf(question.diff) !== -1));
        });
    }
    return knowledges;
}

function changeAnotherQuestion(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    let knowledgeIds = null;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    const queryDbs = new Set((params.dbs || 'basic').split(','));

    Thenjs(function (cont) {
        let qid = params['question_id'];
        db.collection('question').findOne({ _id: qid }, function (err, question) {
            if (err) {
                return cont(err);
            }
            return cont(null, question);
        });
    }).then(function (cont, question) {
        if (!question) {
            return responseWrapper.error('PARAMETERS_ERROR', '没有找到这道题');
        }

        // 试题权限过滤
        if (!(question.dbs || ['basic']).some(item => queryDbs.has(item))) {
            return responseWrapper.error('PARAMETERS_ERROR', '没有找到这道题');
        }
        knowledgeIds = _.pluck(question.knowledges, 'id');
        if (knowledgeIds.length === 0) {
            return responseWrapper.error('NULL_ERROR', '该题没有知识点');
        }
        let diffRange = _getDiffRange(question.difficulty);
        db.collection('knowledge').find({ _id: { $in: knowledgeIds } }).toArray(function (err, knowledges) {
            if (err) {
                return cont(err);
            }
            // 知识点 权限过滤
            knowledges = (knowledges || []).filter(know => (know.dbs || ['basic']).some(item => queryDbs.has(item)));
            knowledges = _filterQuestions(knowledges, question.type, diffRange);
            return cont(null, knowledges, question);
        });

    }).then(function (cont, knowledges, question) {
        if (!knowledges || (knowledges.length === 0)) {
            return responseWrapper.eror('NULL_ERROR', '查找知识点出错');
        }
        let retQid = _pickAnotherQuestion(knowledges, question);
        if (retQid === -1) {

            let diffRange = _getDiffRange(question.difficulty);
            db.collection('knowledge').find({ _id: { $in: knowledgeIds } }).toArray(function (err, knowledges) {
                if (err) {
                    return cont(err);
                }
                // 知识点 权限过滤
                knowledges = (knowledges || []).filter(know => (know.dbs || ['basic']).some(item => queryDbs.has(item)));
                knowledges = _filterQuestions(knowledges, question.type, diffRange);
                let retQid = _pickAnotherQuestion(knowledges, question);
                return cont(null, retQid);
            });
        } else {
            return cont(null, retQid);
        }
    }).then(function (cont, retQid) {
        if (retQid === -1) {
            return responseWrapper.error('NULL_ERROR', '没有找到相似试题');
        }
        _requestQuestion(retQid, params.device, params.fields_type, function (err, result) {
            if (err || !result) {
                return responseWrapper.error('HANDLE_ERROR');
            }
            return responseWrapper.succ(result);
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

const _removeStyle = function (str) {
    if (!str) {
        str = '';
    }

    str = str.replace(/font-size:\w+;?/g, '');
    str = str.replace(/font-family:\w+;?/g, '');
    return str;
};

const _removeQuestionStyle = function (question) {
    question.comment = _removeStyle(question.comment);
    question.description = _removeStyle(question.description);

    let blocks = question.blocks;
    let stems = blocks.stems || [];
    for (let i = 0; i < stems.length; i++) {
        stems[i].stem = _removeStyle(stems[i].stem);
        if (stems[i].stem.options) {
            for (let key in stems[i].stem.options) {
                stems[i].stem.options[key] = _removeStyle(stems[i].stem.options[key]);
            }
        }
    }
    // 替换答案
    let answers = blocks.answers || [];
    for (let i = 0; i < answers.length; i++) {
        if (answers[i] instanceof Array) {
            for (let j = 0; j < answers[i].length; j++) {
                answers[i][j] = _removeStyle(answers[i][j]);
            }
        } else {
            answers[i] = _removeStyle(answers[i]);
        }
    }
    // blocks.answers = answers;
    // 替换解答
    let solutions = blocks.solutions;
    for (let i = 0; i < solutions.length; i++) {
        solutions[i] = _removeStyle(solutions[i]);
    }

    // 替换解析
    let explanations = blocks.explanations;
    for (let i = 0; i < explanations.length; i++) {
        explanations[i] = _removeStyle(explanations[i]);
    }
};

/**
 * Author: cuiyunfeng
 */
function addQuestion(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    if (!body) {
        return responseWrapper.error('HANDLE_ERROR');
    }

    let now = new Date();
    const needKeys = ['type', 'period', 'subject', 'from'];
    for (let key of needKeys) {
        if (!body[key]) {
            return responseWrapper.error('PARAMETERS_ERROR', key + '必填');
        }
    }
    let noVerifyKnowledge = ['xueke_zujuan', 'zwst', 'yjzhixue'];
    if (!noVerifyKnowledge.includes(body.from) && (!body.knowledges || !body.knowledges.length)) {
        return responseWrapper.error('PARAMETERS_ERROR', '创建试题请选择绑定知识点');
    }
    let data = {};
    data.id = body.id;
    // data.elite = body.elite || 0; mod: 取消创建试题默认 非精品（elite=0）设置，非 0|1 视为【未编题】，
    if (body.elite === 0 || body.elite === 1) {
        data.elite = body.elite;
    }
    data.description = body.description || '';
    data.type = body.type || '';
    data.comment = body.comment || '';
    data.difficulty = body.difficulty || 0.5;
    data.orig_diff = body.orig_diff || data.difficulty;
    data.region = body.region || '';
    data.period = body.period || '';
    data.subject = body.subject || '';
    data.source_url = body.source_url || '';
    data.ctime = now;
    data.utime = now;
    data.has_modified = body.has_modified || 1;
    data.from = body.from || 'from_course';
    data.year = body.year || now.getFullYear();
    data.quality = 1;
    data.refer_exampapers = [];
    data.dbs = body.dbs || ['basic'];
    // 校验相似题
    if (body.reco_questions) {
        let err = null;
        if (!_.isArray(body.reco_questions)) {
            err = '创建试题相似题 (reco_questions) 字段需为数组形式';
        } else {
            // 支持相似题列表存在 Object 的形式 - 从 Object 中尝试提取 ID
            body.reco_questions = body.reco_questions.map(e => {
                if (_.isObject(e)) return e.id;
                return e;
            });
            body.reco_questions.forEach(id => {
                if (!_.isNumber(id)) err = `创建试题相似题 (reco_questions) 数组中存在不为数字类型的ID ${id}`;
            });
        }
        if (err) {
            return responseWrapper.error('PARAMETERS_ERROR', err);
        }
        data.reco_questions = body.reco_questions;
    }
    data.refer_times = 0;
    data.knowledges = body.knowledges || [];
    data.elements = body.elements || [];
    data.blocks = body.blocks || {
        stems: [],
        types: [],
        answers: [],
        explanations: [],
        solutions: [],
        knowledges: [],
        core_knowledges: [],
        comments: []
    };
    if (body.type_tags) {
        data.type_tags = body.type_tags; // 试题标签
    }
    if (body.tags) {
        data.tags = body.tags; // 年级，小题数，选项数
    }
    getSubQuesCountAndOptionsCount(data);
    if (!body.difficulty) {
        data.difficulty = 0.6;
    }

    if (body.from === 'dmp') {
        data.dmp_elite = 1;
        delete data.elite;
    }
    if (body.from === 'zjw') {
        data.source_url = body.source_url;
    }

    // 替换是试题中可能由于老师输入带入的font-size和font-family
    _removeQuestionStyle(data);
    if (['简单', '中等', '困难'].indexOf(body.difficulty) >= 0) {
        if (body.difficulty === '简单') {
            data.difficulty = 0.9;
        } else if (body.difficulty === '困难') {
            data.difficulty = 0.3;
        } else {
            data.difficulty = 0.6;
        }
    }
    let diffs = ['容易', '较易', '中等', '较难', '困难'];
    if (diffs.includes(body.difficulty)) {
        body.difficulty = diffs.indexOf(body.difficulty) + 1;
    }
    body.difficulty = Number(body.difficulty);

    if (!isNaN(body.difficulty) && body.difficulty >= 1 && body.difficulty <= 5) {
        let difficulty_arry = [0.9, 0.75, 0.6, 0.4, 0.1];

        data.difficulty = difficulty_arry[parseInt(body.difficulty) - 1];
    }

    if (!isNaN(body.difficulty) && body.difficulty > 0 && body.difficulty < 1) {
        data.difficulty = body.difficulty;
    }
    //判断难度有没有修改成功，不成功则返回异常
    if (data.difficulty * 1 > 1 || data.difficulty * 1 < 0) {
        return responseWrapper.error('PARAMETERS_ERROR', '试题难度异常' + data.difficulty);
    }
    // orig diff
    // 新增时候会附加这个字段，和 difficulty 保持一致，范围0-1，之后的修改不会再动这个字段 [子韬确认]
    data.orig_diff = data.difficulty;
    //插入式题结构兼容
    let err = compatible_question([data]);
    if (err) {
        return responseWrapper.error('PARAMETERS_ERROR', err.message);
    }
    if (data.id && typeof data.id === 'number') {
        data._id = data.id;
        delete data.id;
        db.collection('question').insert(data, function (err, writeResult) {
            if (err) {
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }
            // //创建试题之后会更新该试题的题干图片内容
            // imageUtils.batchUpdateQuestionImges([data._id], { force: 1 });
            return responseWrapper.succ({
                question_id: data._id
            });
        });
    } else {
        delete data.id;
        counter.getNextSequenceValue('question_private', 1, function (err, docs) {
            if (err || docs.length === 0) {
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }
            data._id = docs[0];

            // 过滤标签，补齐字段
            data = filterQuestion(data);
            db.collection('question').insert(data, function (err, writeResult) {
                if (err) {
                    return responseWrapper.error('HANDLE_ERROR', err.message);
                }
                // //创建试题之后会更新该试题的题干图片内容
                // imageUtils.batchUpdateQuestionImges([data._id], { force: 1 });
                return responseWrapper.succ({
                    question_id: data._id
                });
            });
        });
    }

}

/*
 * Author: cuiyunfeng
 */
function updateQuestion(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    let questionId = Number(req.params.question_id);
    let api_key = req.query.api_key;
    const dbs = (req.query.dbs || 'basic').split(',');
    if (!questionId) {
        return responseWrapper.error('PARAMETERS_ERROR', '必须包含question id');
    }
    if (!body) {
        return responseWrapper.error('HANDLE_ERROR');
    }

    if (!dbs.includes('kbp')) {
        delete body.dbs;
    }

    let fieldNames = [
        'description', 'type', 'comment', 'region', 'subject', 'period',
        'source_url', 'from', 'quality', 'knowledges', 'blocks', 'elite', 'dmp_video', 'dbs'
    ];

    let data = {};
    if (!['iyunxiao_kb98913578', 'iyunxiao_tester'].includes(api_key)) {
        delete data.elite;
    }
    Object.keys(body).forEach(function (name) {
        if (fieldNames.indexOf(name) >= 0) {
            data[name] = body[name];
        }
    });
    data.has_modified = 1;

    if (['简单', '中等', '困难'].indexOf(body.difficulty) >= 0) {
        if (body.difficulty === '简单') {
            data.difficulty = 0.9;
        } else if (body.difficulty === '困难') {
            data.difficulty = 0.3;
        } else {
            data.difficulty = 0.6;
        }
    }

    body.difficulty = Number(body.difficulty);

    if (!isNaN(body.difficulty) && body.difficulty >= 1 && body.difficulty <= 5) {
        let difficulty_arry = [0.9, 0.75, 0.6, 0.4, 0.1];

        data.difficulty = difficulty_arry[parseInt(body.difficulty) - 1];
    }

    if (!isNaN(body.difficulty) && body.difficulty > 0 && body.difficulty < 1) {
        data.difficulty = body.difficulty;
    }

    data.difficulty = data.difficulty || 0.01;
    data.utime = new Date();
    let cond = { _id: questionId, dbs: { $in: dbs } };
    db.collection('question').findOne(cond, function (err, questionDoc) {
        if (err) {
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
        if (!questionDoc) {
            return responseWrapper.error('PARAMETERS_ERROR', '没有找到对应的试题');
        }

        // 知识点id没有修改，还使用原来的知识点信息
        let oldKnows = questionDoc.knowledges;
        utils.setOldKnow(data, oldKnows);
        utils.setOldElements(data, questionDoc); // 恢复原叠加要素

        // 过滤标签，补齐字段
        data = filterQuestion(data);
        db.collection('question').update(cond, { $set: data }, function (err, writeResult) {
            if (err) {
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }
            if (writeResult.result.n < 1) {
                return responseWrapper.error('PARAMETERS_ERROR', '没有找到对应的试题');
            }
            //编辑试题之后会强制更新该试题的题干图片内容
            imageUtils.batchUpdateQuestionImges([cond._id], { force: 1 });
            return responseWrapper.succ({ question_id: questionId });
        });
    });
}


// 设置相似题
const _setSimilarQuestions = async (questionId, newSimilarQuestionIds) => {
    let question = await db.collection('question').findOne({ _id: questionId });
    if (!question) throw `无对应 ID ${questionId} 试题`;

    let similarQuestionIds = _.isArray(question.reco_questions) ? question.reco_questions : [];
    let relatedSimilarQuestions = await db.collection('question').find({ _id: { $in: _.uniq([...similarQuestionIds, ...newSimilarQuestionIds]) } }).toArray();
    let appendIds = _.difference(newSimilarQuestionIds, similarQuestionIds);
    let removeIds = _.difference(similarQuestionIds, newSimilarQuestionIds);

    const isIdsEqual = (ids, targetIds) => ids.sort().join() === targetIds.sort().join();

    // 修改新增关联 相似题的相似题状态
    await Promise.all(appendIds.map(id => {
        let question = _.find(relatedSimilarQuestions, e => e._id === id);
        let similarQuestionIds = _.isArray(question.reco_questions) ? question.reco_questions : [];
        let newSimilarQuestionIds = _.uniq([...similarQuestionIds, questionId]).filter(_id => _id !== id);
        // 如果 相似题 ID 列表相同则不触发更新
        if (isIdsEqual(similarQuestionIds, newSimilarQuestionIds)) return question;
        return db.collection('question').updateOne(
            { _id: id },
            { $set: { reco_questions: newSimilarQuestionIds } }
        );
    }));

    // 修改移除关联 相似题的相似题状态
    await Promise.all(removeIds.map(id => {
        let question = _.find(relatedSimilarQuestions, e => e._id === id);
        if (!question) throw `需要解除关联的相似题 ${id} 不存在`;
        let similarQuestionIds = _.isArray(question.reco_questions) ? question.reco_questions : [];
        let newSimilarQuestionIds = similarQuestionIds.filter(_id => _id !== questionId);
        // 如果 相似题 ID 列表相同则不触发更新
        if (isIdsEqual(similarQuestionIds, newSimilarQuestionIds)) return question;
        return db.collection('question').updateOne(
            { _id: id },
            { $set: { reco_questions: newSimilarQuestionIds } }
        );
    }));

    await db.collection('question').updateOne(
        { _id: questionId },
        { $set: { reco_questions: newSimilarQuestionIds.filter(id => id !== questionId) } }
    );
};

// 新增相似题
async function addSimilarQuestions(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    let questionId = Number(req.params.id);
    let newSimilarQuestionIds = body.reco_questions;
    try {
        let question = await db.collection('question').findOne({ _id: questionId });
        if (!question) {
            responseWrapper.error('HANDLE_ERROR', '无对应 ID 试题');
            return;
        }
        let similarQuestionIds = _.isArray(question.reco_questions) ? question.reco_questions : [];
        await _setSimilarQuestions(questionId, _.uniq([...similarQuestionIds, ...newSimilarQuestionIds]));
        responseWrapper.succ({});
    } catch (e) {
        responseWrapper.error('HANDLE_ERROR', e);
    }
}

// 更新相似题
async function updateSimilarQuestions(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let body = req.body;
    let questionId = Number(req.params.id);
    let newSimilarQuestionIds = body.reco_questions;
    try {
        await _setSimilarQuestions(questionId, newSimilarQuestionIds);
    } catch (e) {
        responseWrapper.error('HANDLE_ERROR', e);
    }
    responseWrapper.succ({});
}

/**
 * Author: YuLei
 * Description: 换一批试题
 * @param {*} req
 * @param {*} res
 * @return {*}
 */
function changeQuestions(req, res) {
    let responseWrapper = new ResponseWrapper(res);
    let params = null;
    let knowledgeIds = null;
    let changeTimes = req.query.change_times ? Number(req.query.change_times) : 0;
    try {
        let fun_name = arguments.callee.name;
        params = params_utils.create_params(req, kb_api[fun_name]);
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR', e.message);
    }

    const queryDbs = new Set((req.query.dbs || 'basic').split(','));

    Thenjs(function (cont) {
        let qid = params['question_id'];
        db.collection('question').findOne({ _id: qid }, function (err, question) {
            if (err)
                return cont(err);
            return cont(null, question);
        });
    }).then(function (cont, question) {
        if (!question)
            return responseWrapper.error('PARAMETERS_ERROR', '没有找到这道题');
        // 试题 权限过滤
        if (!(question.dbs || ['basic']).some(item => queryDbs.has(item))) {
            return responseWrapper.error('PARAMETERS_ERROR', '没有找到这道题');
        }
        knowledgeIds = _.pluck(question.knowledges, 'id');
        if (knowledgeIds.length === 0) {
            return responseWrapper.error('NULL_ERROR', '该题没有知识点');
        }
        let diffRange = _getDiffRangeNew(question.difficulty);
        db.collection('knowledge').find({ _id: { $in: knowledgeIds } }).toArray(function (err, knowledges) {
            if (err) {
                return cont(err);
            }
            // 知识点 权限过滤
            knowledges = (knowledges || []).filter(knowledge => (knowledge.dbs || ['basic']).some(item => queryDbs.has(item)));
            knowledges = _filterQuestions(knowledges, question.type, diffRange);
            return cont(null, knowledges, question);
        });
    }).then(function (cont, knowledges, question) {
        if (!knowledges || (knowledges.length === 0)) {
            Logger.error('查找知识点出错');
            return responseWrapper.eror('NULL_ERROR', '查找知识点出错');
        }
        let resObj = _pickSomeQuestions(knowledges, question, 10, changeTimes);

        return cont(null, resObj.qids, resObj.totalNum);
    }).then(function (cont, retQids, totalNum) {
        if (retQids.length === 0) {
            return responseWrapper.error('NULL_ERROR', '没有找到相似试题');
        }
        _requestQuestions(retQids, params.device, params.fields_type, function (err, result) {
            if (err || !result) {
                return responseWrapper.error('HANDLE_ERROR');
            }
            return responseWrapper.succ({ total_num: totalNum, questions: result });
        });
    }).fail(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    }).finally(function (cont, error) {
        Logger.error(error);
        return responseWrapper.error('HANDLE_ERROR');
    });
}

/**
 * 更新试题题干图片，stem_img,answer_img,solution_img,explanation_img
 * @param {*} req
 * @param {*} res
 */
async function updateQuestionPartImages(req, res) {
    let rw = new ResponseWrapper(res);
    let ids = req.body.ids || [];
    let force = req.body.force || '';
    const option = {
        force
    };
    await imageUtils.batchUpdateQuestionImges(ids, option);
    rw.succ({});
}

async function setStemImageUrlToQuestion(req, res) {
    let question_id = req.params.question_id;
    let rw = new ResponseWrapper(res);
    question_id = Number(question_id);
    if (!question_id) {
        return rw.error('PARAMETERS_ERROR', '试题id必须是整数');
    }
    await imageUtils.batchUpdateQuestionImges([question_id]);
    return rw.succ({});
}

/**
 * 简单选择，试题管理首页展示
 * 可以指定按学段学科筛选
 * 查找范围试题数可以指定，默认：1000，如果其中指定的学科学段试题资源数量少于一页 limit，再按学科学段查询100道试题并缓存
 * 按试题创建时间降序排序：newsList
 * <AUTHOR>
 * @param {*} req
 * @param {*} res
 */
async function simpleSelectQuestions(req, res) {
    const resWrapper = new ResponseWrapper(res);
    const collection = await db.collection('question');
    let range = +req.query.range || 1000; // 指定查找范围，默认在最新创建的1000道试题中查找
    let offset = +req.query.offset || 0;
    let limit = +req.query.limit || 10;
    let elite = req.query.elite; //
    let queryDbs = req.query.dbs || 'basic';

    let sort = { ctime: -1 };

    let eliteCond = {
        dbs: { $in: queryDbs.split(',') }
    }
    if (elite) {
        eliteCond.elite = +elite === -1 ? { $exists: 0 } : +elite;
    }

    let project = {
        _id: 1,
        comment: 1,
        blocks: 1,
        description: 1,
        type: 1,
        knowledges: 1,
        period: 1,
        subject: 1,
        difficulty: 1,
        refer_exampapers: 1,
        refer_times: 1,
        use_type: 1,
        core_knowledges: 1,
        elite: 1,
        reco_questions: 1,
        use_times: 1,
        ctime: 1
    };
    let filter = {};
    if (req.query.period) filter.period = req.query.period;
    if (req.query.subject) filter.subject = req.query.subject;

    // 缓存1000道指定 elite 的试题ID
    let elite_cache_key = `kb_api:v2:questions:news:${queryDbs}:${process.env.NODE_PORT}`;
    if (elite) {
        elite_cache_key = `${elite_cache_key}:elite_${elite}`;
    }
    // 缓存 limit * 10 道指定学段学科的试题ID
    let period_cache_key = `${elite_cache_key}:${qs.stringify(filter, '&', '_')}`;
    try {
        let questions = [], total_num = 0;
        // 先读取学段学科缓存的试题，如果没有，再读取指定 elite 的缓存试题
        let cachedIds = await rediser.get(period_cache_key);
        if (cachedIds) {
            total_num = cachedIds.length;
            questions = await collection.find({ _id: { $in: cachedIds.slice(offset, offset + limit) } }).project(project).sort(sort).toArray();
        } else {
            let cachedObjects = await rediser.get(elite_cache_key);
            if (!cachedObjects) {
                questions = await collection.find(eliteCond).project({ _id: 1, period: 1, subject: 1 }).sort(sort).limit(range).toArray();
                cachedObjects = questions.map(e => ({ id: e._id, period: e.period, subject: e.subject }));
                rediser.set(elite_cache_key, cachedObjects, 60 * 30);
            }
            cachedObjects = _.filter(cachedObjects, filter);
            if (cachedObjects.length < limit) {
                questions = await collection.find({ ...eliteCond, ...filter }).project(project).sort(sort).limit(limit * 10).toArray();
                rediser.set(period_cache_key, questions.map(e => e._id), 60 * 60);
                total_num = questions.length;
                questions = questions.slice(offset, offset + limit);
            } else {
                total_num = cachedObjects.length;
                questions = await collection.find({ _id: { $in: cachedObjects.map(e => e.id).slice(offset, offset + limit) } }).project(project).sort(sort).toArray();
            }
        }
        questions.forEach(q => utils.filter_fields(q, 'full'));
        resWrapper.succ({ total_num, questions });
    } catch (err) {
        resWrapper.error('HANDLE_ERROR', err);
    }
}

const getEliteCount = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    const period = req.query.period;
    const subject = req.query.subject;

    if (!period) {
        return responseWrapper.error('PARAMETERS_ERROR', 'period不能为空');
    }

    if (!subject) {
        return responseWrapper.error('PARAMETERS_ERROR', 'subject不能为空');
    }

    const count = await db.collection('question').count({ elite: 1, subject: subject, period: period }, { hint: { elite: 1, subject: 1, period: 1 } });
    return responseWrapper.succ({ total_num: count });
};

/**
 * 获取drm试题数量
 * @param req
 * @param res
 * @returns {Promise<*>}
 */
const getDrmCount = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    const period = req.query.period;
    const subject = req.query.subject;
    if (!period) {
        return responseWrapper.error('PARAMETERS_ERROR', 'period不能为空');
    }
    if (!subject) {
        return responseWrapper.error('PARAMETERS_ERROR', 'subject不能为空');
    }
    let totalCond = { period: period, subject: subject, from: 'drm' };
    let todayCond = {
        period: period,
        subject: subject,
        from: 'drm',
        ctime: { $gt: new Date(new Date().setHours(0, 0, 0)) }
    };
    const totalNum = await db.collection('question').count(totalCond);
    const todayNum = await db.collection('question').count(todayCond);
    return responseWrapper.succ({ total_num: totalNum, today_num: todayNum });
};

// 查询出的字段
const getQuestionProject = () => {
    return {
        _id: 1,
        comment: 1,
        blocks: 1,
        description: 1,
        type: 1,
        knowledges: 1,
        period: 1,
        subject: 1,
        difficulty: 1,
        refer_exampapers: 1,
        refer_times: 1,
        use_type: 1,
        core_knowledges: 1,
        elite: 1,
        year: 1,
        reco_questions: 1,
        use_times: 1,
        type_tags: 1,
        tags: 1,
        ctime: 1,
        from: 1,
        elements: 1,
        attach: 1,
    };
};

const getPendingReviewQuestions = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    let period = req.query.period;
    let subject = req.query.subject;
    let limit = req.query.limit || 10;
    let offset = req.query.offset || 0;
    if (!period) {
        return responseWrapper.error('PARAMETERS_ERROR', 'period不能为空');
    }
    if (!subject) {
        return responseWrapper.error('PARAMETERS_ERROR', 'subject不能为空');
    }
    let cond = {
        period: period,
        subject: subject,
        status: 0,
    };
    try {
        let total_num = await db.collection('question_label').count(cond);
        let ids = await db.collection('question_label').find(cond).project({ _id: 1 }).skip(parseInt(offset)).limit(parseInt(limit)).toArray();
        let result = await db.collection('question').find({ _id: { $in: ids.map(item => item._id) } }).project(getQuestionProject()).toArray();
        for (let ques of result) {
            filter_fields(ques, 'stem');
        }
        responseWrapper.succ({
            total_num: total_num,
            questions: result
        });
    } catch (error) {
        Logger.error(error);
        responseWrapper.error('HANDLE_ERROR', error.message || error);
    }
};

const inspect = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    try {
        let question_id = parseInt(req.params.question_id);
        await db.collection('question').updateOne({ _id: question_id }, { $set: { inspect: true, utime: new Date() } });
        res.resMode = 'normal';
        responseWrapper.succ({});
    } catch (error) {
        Logger.error(error);
        responseWrapper.error('HANDLE_ERROR', error.message || error);
    }
};

// 批量获取试题练习数量
const postPracticeStatisticsByIds = async (req, res) => {
    const responseWrapper = new ResponseWrapper(res);
    let params = params_utils.create_params(req, kb_api['post_statistics']);
    let ids = params['ids'].slice(0, 200);
    let cond = { '$in': ids };
    let data = await db.collection('ques_stat_of_exercise').find({ _id: cond }).project({ utime: 0, ctime: 0 }).toArray();
    data.forEach((value) => {
        if (value) {
            value.id = value._id;
            delete value._id;
        }
    });
    if (ids.length !== data.length) {
        Logger.warn(`ids：${ids},返回id:length：${data.length}`);
    }
    const queryDbs = new Set((req.body.dbs || ['basic']));
    data = (data || []).filter(ques => (ques.dbs || ['basic']).some(item => queryDbs.has(item)));
    if (!data || data.length === 0) {
        return responseWrapper.error('PARAMETERS_ERROR', '没有找到对应的试题');
    }
    res.resMode = 'normal';
    return responseWrapper.succ(data);
};

module.exports = {
    query_questions: query_questions,
    knowledge_questions: _knowledge_questions,
    question: question,
    get_questions: get_questions,
    post_questions: post_questions,
    modify_question: modify_question,
    profile: profile,
    filters: filters,
    addQuestion: addQuestion,
    updateQuestion: updateQuestion,
    getQuestionStemImage: getQuestionStemImage,
    changeAnotherQuestion: changeAnotherQuestion,
    addSimilarQuestions,
    updateSimilarQuestions,
    changeQuestions: changeQuestions,
    updateQuestionPartImages: updateQuestionPartImages,
    setStemImageUrlToQuestion: setStemImageUrlToQuestion,
    simpleSelectQuestions,
    getEliteCount,
    getDrmCount,
    getPendingReviewQuestions,
    inspect,
    postPracticeStatisticsByIds
};
