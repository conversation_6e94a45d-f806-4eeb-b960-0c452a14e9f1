var Thenjs = require('thenjs');
var _ = require('underscore');
const assert = require("assert")
var Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
var params_utils = require('../../common/utils/params.js');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const rediser = require('../../common/utils/rediser');
var kb_api = require('../config.js').kb_api['v2'];
var config = require('config');
var loosen_tree = require('./utils.js').loosen_tree;

const _getChapterIdsInBook = require('./book')._getChapterIdsInBook;
const _assignChaptersToBook = require('./book')._assignChaptersToBook;
const changeKeyInBookStruct = require('./book').changeKeyInBookStruct;
const filterCatalog = require('./book').filterCatalog;
const _getChaptersInfo = require('./book')._getChaptersInfo;
const insertChapters = require('./book').insertChapters;
/*
 * 函数描述:
 * 		请求知识树信息（打包信息）	
 * URL: 
 * 		http://kboe.gotiku.com/kb_api/v2/knowledge_trees/
 * Method: 
 * 		GET
 * Author: 
 * 		zhangjun
 * Date:
 * 		2016-08-18
 */
function knowledge_trees(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	var params = null;
	try {
		var fun_name = arguments.callee.name;
		params = params_utils.create_params(req, kb_api[fun_name]);
	} catch (e) {
		Logger.error(e);
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		var cache_key = params['cache_key'] + ':' + process.env.NODE_PORT;
		try {
			if (config.get('kb_plat.appkey').indexOf(req.query.api_key) >= 0) {
				cache_key = cache_key + ':' + req.query.api_key;
			}
		} catch (err) {

		}
		rediser.get(cache_key, function (err, items) {
			if (!items) {
				var coll = db.collection('catalog');
				// 查询条件
				var cond = { _id: 'knowledge_tree' };

				// 查询
				coll.findOne(cond, function (err, catalog) {
					if (err) {
						return cont(err);
					}
					if (catalog) {
						try {
							try {
								if (config.get('kb_plat.appkey').indexOf(req.query.api_key) < 0) {
									var subjects = config.get('kb_plat.unique_subjects');
									for (var ix in catalog.periods) {
										var period = catalog.periods[ix];
										period.subjects = _.filter(period.subjects, function (x) {
											return subjects.indexOf(x.name) < 0;
										});
									}
								}
							} catch (err) { }
							catalog = filterCatalog(catalog, params['period'], params['subject']);
							var keys = {
								'periods': 'period',
								'subjects': 'subject',
								'trees': 'tree',
							};
							catalog['knowledge_tree'] = { 'periods': catalog.periods };
							delete catalog['periods'];
							loosen_tree(catalog['knowledge_tree'], keys);
						} catch (e) {
							return cont(e);
						}
						rediser.set(cache_key, catalog, 60 * 30);
						return cont(null, catalog);
					} else {
						return cont(null, null);
					}
				});
			} else {
				return cont(null, items);
			}
		});
	}).then(function (cont, result) {
		if (result) {
			// 过滤无权限的知识树
			const queryDbs = new Set((req.query.dbs || 'basic').split(','));
			result.knowledge_tree.children = (result.knowledge_tree.children || []).filter(period => {
				period.children = (period.children || []).filter(subject => {
					subject.children = (subject.children || []).filter(tree => {
						return (tree.dbs || ['basic']).some(item => queryDbs.has(item));
					});
					return subject.children.length > 0;
				});
				return period.children.length > 0;
			});
			if (result.knowledge_tree.children.length > 0) {
				return responseWrapper.succ(result);
			}
		}
		return responseWrapper.error('NULL_ERROR');
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

/*
 * Desc:
 *      请求某教材下所有章节及其知识点集
 * URL:
 *      http://kboe.yunxiao.com/kb_api/v2/knowledge_trees/{knowledge_tree_id}/
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-18
*/
function knowledge_tree(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	var params = null;
	try {
		var fun_name = arguments.callee.name;
		params = params_utils.create_params(req, kb_api[fun_name]);
		var cache_key = params['cache_key'] + ':' + process.env.NODE_PORT;
	} catch (e) {
		Logger.error(e);
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	// 数据查询 ----------------------------
	Thenjs(function (cont) {

		rediser.get(cache_key, function (err, tree) {
			if (tree) {
				// 过滤无权限的知识树
				const queryDbs = new Set((req.query.dbs || 'basic').split(','));
				if (!(tree.dbs || ['basic']).some(item => queryDbs.has(item))) {
					return responseWrapper.error('NULL_ERROR');
				}
				return responseWrapper.succ(tree);
			}
			return cont(null);
		});
	}).then(function (cont) {
		var coll = db.collection('knowledge_tree');
		// 查询条件
		var cond = {
			_id: params['knowledge_tree_id'],
		};
		// 提取出的字段
		var proj = {
			has_modified: 0,
		};
		// 查询
		coll.findOne(cond, proj, function (err, knowledge_tree) {
			if (err) {
				return cont(err);
			}
			if (!knowledge_tree) {
				return responseWrapper.error('NULL_ERROR');
			}
			return cont(null, knowledge_tree);
		});
	}).then(function (cont, knowledge_tree) {
		var chapterIds = [];
		_getChapterIdsInBook('nodes', knowledge_tree.nodes, chapterIds);
		var proj = {
			ctime: 0,
			utime: 0,
			path: 0
		}
		db.collection('knowledge_tree_chapter').find({ _id: { $in: chapterIds } }).project(proj).toArray(function (err, chapters) {
			if (err) {
				return cont(err);
			}
			return cont(null, knowledge_tree, chapters);
		});

	}).then(function (cont, knowledge_tree, chapters) {

		knowledge_tree = _assignChaptersToBook('knowledge_tree', knowledge_tree, chapters);
		knowledge_tree['id'] = knowledge_tree['_id'];
		delete knowledge_tree['_id'];

		knowledge_tree['knowledge_tree'] = {
			'nodes': knowledge_tree['nodes'],
		};
		delete knowledge_tree['nodes'];
		var keys = {
			'nodes': 'knowledge_node',
			'knowledges': 'knowledge',
		};
		loosen_tree(knowledge_tree['knowledge_tree'], keys);
		rediser.set(cache_key, knowledge_tree, 60 * 30);
		// 过滤无权限的知识树
		const queryDbs = new Set((req.query.dbs || 'basic').split(','));
		if (!(knowledge_tree.dbs || ['basic']).some(item => queryDbs.has(item))) {
			return responseWrapper.error('NULL_ERROR');
		}
		return responseWrapper.succ(knowledge_tree);
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

/*
 * Desc:
 *      请求某知识树下所有节点及其知识点集
 * URL:
 *      http://kboe.yunxiao.com/kb_api/v2/knowledge_trees/knowledge_nodes/knowledges
 * Return:
 * 		
 * Author:
 *      zhangjun
 * Date:
 *      2016-08-18
*/
function knowledge_tree_by_name(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	var params = null;
	try {
		var fun_name = arguments.callee.name;
		params = params_utils.create_params(req, kb_api[fun_name]);
	} catch (e) {
		Logger.error(e);
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		var cache_key = params['cache_key'] + ':' + process.env.NODE_PORT;
		rediser.get(cache_key, function (err, tree) {
			if (!tree) {
				var coll = db.collection('knowledge_tree');
				// 查询条件
				var cond = {
					period: params['period'],
					subject: params['subject'],
					name: params['name'],
				};
				// 提取出的字段
				var proj = {
					has_modified: 0,
				};
				// 查询
				coll.find(cond).project(proj).toArray(function (err, items) {
					if (err) {
						return cont(err);
					}
					var item = null;
					if (items.length > 0) {
						item = items[0];
						item['id'] = item['_id'];
						delete item['_id'];
						item['knowledge_tree'] = {
							'nodes': item['nodes'],
						};
						delete item['nodes'];
						var keys = {
							'nodes': 'knowledge_node',
							'knowledges': 'knowledge',
						};
						loosen_tree(item['knowledge_tree'], keys);
						rediser.set(cache_key, item, 60 * 30);
					}
					return cont(null, item);
				});
			} else {
				return cont(null, tree);
			}
		});
	}).then(function (cont, result) {
		if (result) {
			// 权限过滤
			const queryDbs = new Set((req.query.dbs || 'basic').split(','));
			if (!(result.dbs || ['basic']).some(item => queryDbs.has(item))) {
				return responseWrapper.error('NULL_ERROR');
			}
			return responseWrapper.succ(result);
		} else {
			return responseWrapper.error('NULL_ERROR');
		}
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

function knowledge_tree_struct(req, res) {
	var responseWrapper = new ResponseWrapper(res);

	try {
		var knowledge_tree_id = parseInt(req.params.knowledge_tree_id);
		var origin = req.query.origin;
		assert(knowledge_tree_id);
	} catch (e) {
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		var coll = db.collection('knowledge_tree');
		// 查询条件
		var cond = { '_id': knowledge_tree_id };

		coll.findOne(cond, function (err, tree) {
			if (err) {
				return cont(err);
			}
			if (!tree) {
				return responseWrapper.error('PARAMETERS_ERROR', '查不到该条记录');
			}
			if (origin) {
				return responseWrapper.succ(tree);
			}
			tree['id'] = tree['_id'];
			delete tree['_id'];
			let chapterIds = [];
			_getChapterIdsInBook('nodes', tree.nodes, chapterIds);
			tree['book'] = {
				'chapters': tree.nodes,
			};
			delete tree['nodes'];
			var keys = {
				'chapters': 'chapter',
				'nodes': 'chapter'
			};
			loosen_tree(tree['book'], keys);
			return cont(null, tree, chapterIds);
		});
	}).then(function (cont, tree, chapterIds) {

		var coll = db.collection('knowledge_tree_chapter');
		// 查询条件
		var cond = { '_id': { $in: chapterIds } };


		coll.find(cond).toArray(function (err, chapterInfos) {
			if (err) {
				return cont(err);
			}
			chapterInfos.forEach(function (chapterInfo) {
				chapterInfo['id'] = chapterInfo['_id'];
				delete chapterInfo['_id'];
			});
			return cont(null, tree, chapterInfos);
		});
	}).then(function (cont, tree, chapterInfos) {
		changeKeyInBookStruct(tree.book.children, chapterInfos);
		return responseWrapper.succ(tree);
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

// 判断是否有重复的教材
function checkTreeConflict(originBookId, period, subject, name, callback) {
	let cond = {
		'period': period,
		'subject': subject,
		'name': name,
		'invalid': { $ne: 1 }
	};
	db.collection('knowledge_tree').findOne(cond, function (err, tree) {
		if (err) {
			return callback(err);
		}
		if (tree) {
			//新添加教材
			if (originBookId === 0) {
				return callback('知识树名称重复');
			} else {
				if (tree._id === originBookId) {
					return callback(null);
				}
				return callback('知识树名称重复');
			}
		}
		return callback(null);
	});
}

//查询tree_chapter
function tree_chapter(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	var chapterIds;
	try {
		var idStr = req.params.tree_chapter_id;
		//多个id拼接
		chapterIds = idStr.split(',');
		chapterIds = _.map(chapterIds, function (x) { return parseInt(x) });
		assert(chapterIds);
	} catch (e) {
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		_getChaptersInfo(chapterIds, 'knowledge_tree', function (err, bookChapters) {
			if (err) {
				return cont(err);
			}
			return responseWrapper.succ(bookChapters);
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		if (typeof (error) === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

// 对外提供接口检查是否冲突
function check_tree_conflict(req, res) {
	//checkBookConflict	
	var responseWrapper = new ResponseWrapper(res);
	try {
		var tree_id = parseInt(req.query.tree_id);
		//assert(!isNaN(book_id));
		var toPeriod = req.query.period;
		assert(toPeriod !== '');
		var toSubject = req.query.subject;
		assert(toSubject != '');
		var toName = req.query.name;
		assert(toName != '');

	} catch (e) {
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		checkTreeConflict(tree_id, toPeriod, toSubject, toName, function (err) {
			if (err) {
				return cont(err);
			}
			return cont(null);
		});
	}).then(function (cont) {
		return responseWrapper.succ({});
	}).fail(function (cont, error) {
		Logger.error(error);
		if (typeof (error) === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
// 删除catalog 某一项
function deleteCatalogItem(catalog, id, period, subject, name) {
	let selectPeriodCatalog = _.find(catalog.periods, function (periodCatalog) {
		return periodCatalog.name === period;
	});
	let selectSubjectCatalog = _.find(selectPeriodCatalog.subjects, function (subjectCatalog) {
		return subjectCatalog.name === subject;
	});

	selectSubjectCatalog.trees = _.filter(selectSubjectCatalog.trees, function (tree) {
		return name !== tree.name;
	});
	// 当前 学科 需要删除此版本
	if (!selectSubjectCatalog.trees.length) {
		selectSubjectCatalog.subjects = _.filter(selectPeriodCatalog.subjects, function (subjectCatalog) {
			return subjectCatalog.name !== subject;
		});
	}

}
// 增加catalog 某一项
function addCatalogItem(catalog, id, period, subject, name) {

	let selectPeriodCatalog = _.find(catalog.periods, function (periodCatalog) {
		return periodCatalog.name === period;
	});
	if (!selectPeriodCatalog) {
		selectPeriodCatalog = { 'name': period, 'subjects': [] };
		catalog.periods.push(selectPeriodCatalog);
	}

	let selectSubjectCatalog = _.find(selectPeriodCatalog.subjects, function (subjectCatalog) {
		return subjectCatalog.name === subject;
	});
	if (!selectSubjectCatalog) {
		selectSubjectCatalog = { 'name': subject, 'trees': [] };
		selectPeriodCatalog.subjects.push(selectSubjectCatalog);
	}
	selectSubjectCatalog.trees.push({ 'id': id, 'name': name });

}
// curInfo body 
// originInfo (press_version, grade)
// opType  edit, new , delete
function changeTreeCatalog(curInfo, originInfo, opType, callback) {
	Thenjs(function (cont) {
		db.collection('catalog').findOne({ _id: 'knowledge_tree' }, function (err, catalog) {
			if (err) {
				return callback(err);
			}
			return cont(null, catalog);
		})
	}).then(function (cont, catalog) {
		let id = curInfo._id;
		let period = curInfo.period;
		let subject = curInfo.subject;
		let name = curInfo.name;
		if (opType === 'edit') {
			deleteCatalogItem(catalog, id, originInfo.period, originInfo.subject, originInfo.name);
			addCatalogItem(catalog, id, period, subject, name);
		} else if (opType === 'new') {
			addCatalogItem(catalog, id, period, subject, name);
		} else if (opType === 'delete') {
			deleteCatalogItem(catalog, id, originInfo.period, originInfo.subject, originInfo.name);
		}

	}).then(function (cont, catalog) {
		catalog['utime'] = new Date();
		db.collection('catalog').updateOne({ _id: 'knowledge_tree' }, { $set: catalog }, function (err, catalog) {
			if (err) {
				return callback(err);
			}
			return callback(null);
		})
	}).fail(function (cont, error) {
		Logger.error(error);
		return callback(error);
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback(error);
	});
}
// 同步教材结构信息
function post_tree_struct(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	try {

		var body = req.body;
		var tree_id = body._id;
		var curRootPath = [body.period, body.subject, body.name].join('_');
		var originTree;
		assert(body.period);
		assert(body.subject);
		assert(body.name);
		assert(body.hasOwnProperty('nodes'));
	} catch (e) {
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		checkTreeConflict(tree_id, body.period, body.subject, body.name, function (err) {
			if (err) {
				return cont(err);
			}
			return cont(null);
		});
	}).then(function (cont) {
		// 获取原教材结构
		db.collection('knowledge_tree').findOne({ _id: tree_id }, function (err, tree) {
			if (err) {
				return cont(err);
			}
			if (tree) {
				originTree = tree;
			}
			return cont(null);
		});
	}).then(function (cont) {
		if (originTree) {
			// 判断是否需要修改编目
			let originRootPath = [originTree.period, originTree.subject, originTree.name].join('_')
			if (originRootPath !== curRootPath) {
				let originTreeInfo = {
					'period': originTree.period, 'subject': originTree.subject,
					'name': originTree.name
				};
				changeTreeCatalog(body, originTreeInfo, 'edit', function (err) {
					if (err) {
						return cont(err);
					}
					return cont(null);
				});
			} else {
				return cont(null);
			}
		} else {

			changeTreeCatalog(body, null, 'new', function (err) {
				if (err) {
					return cont(err);
				}
				return cont(null);
			});
		}
	}).then(function (cont) {
		// 插入book表
		if (originTree) {
			body['ctime'] = new Date();
		}
		body['utime'] = new Date();
		body['has_modified'] = 1;
		var cond = { _id: body['_id'] };
		var opts = { upsert: true };

		db.collection('knowledge_tree').findAndModify(cond, [], { $set: body }, opts, function (err, item) {
			if (err) {
				return cont(err);
			}
			let cacheKeyPkg = 'kb_api:v2:knowledge_trees_pkg:*';
			let cacheKeyBook = 'kb_api:v2:knowledge_trees:' + body['_id'] + '*';
			rediser.getCache().keys(cacheKeyPkg, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			rediser.getCache().keys(cacheKeyBook, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			return responseWrapper.succ({});
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}
// 
// 批量插入更新章节
function post_tree_chapters(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	let params = {};
	try {
		assert(req.body.chapters);
		params['chapters'] = req.body.chapters;
		params['type'] = 'knowledge_tree';
	} catch (e) {
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	// 数据查询 ----------------------------
	Thenjs(function (cont) {
		insertChapters(params, function (err) {
			if (err) {
				return cont(err);
			}
			return responseWrapper.succ({});
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

// curInfo body 
// originInfo (press_version, grade)
// opType  edit, new , delete
function changeTreeCatalog(curInfo, originInfo, opType, callback) {
	Thenjs(function (cont) {
		db.collection('catalog').findOne({ _id: 'knowledge_tree' }, function (err, catalog) {
			if (err) {
				return callback(err);
			}
			return cont(null, catalog);
		})
	}).then(function (cont, catalog) {
		let id = curInfo._id;
		let period = curInfo.period;
		let subject = curInfo.subject;
		let name = curInfo.name
		if (opType === 'edit') {
			deleteCatalogItem(catalog, id, originInfo.period, originInfo.subject, originInfo.name);
			addCatalogItem(catalog, id, period, subject, name);
		} else if (opType === 'new') {
			addCatalogItem(catalog, id, period, subject, name);
		} else if (opType === 'delete') {
			deleteCatalogItem(catalog, id, originInfo.period, originInfo.subject, originInfo.name);
		}
		return cont(null, catalog);
	}).then(function (cont, catalog) {
		catalog['utime'] = new Date();
		db.collection('catalog').updateOne({ _id: 'knowledge_tree' }, { $set: catalog }, function (err, catalog) {
			if (err) {
				return callback(err);
			}
			return callback(null);
		})
	}).fail(function (cont, error) {
		Logger.error(error);
		return callback(error);
	}).finally(function (cont, error) {
		Logger.error(error);
		return callback(error);
	});
}
function delete_tree(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	try {
		var tree_id = parseInt(req.params.id);
		assert(!isNaN(tree_id));

	} catch (e) {
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	// 数据查询 ----------------------------
	Thenjs(function (cont) {

		// 获取原教材结构
		db.collection('knowledge_tree').findOne({ _id: tree_id }, function (err, tree) {
			if (err) {
				return cont(err);
			}
			if (!tree) {
				return responseWrapper.error('HANDLE_ERROR', '此id教材不存在');
			}
			return cont(null, tree);
		});
	}).then(function (cont, tree) {
		changeTreeCatalog({ 'id': tree_id }, tree, 'delete', function (err) {
			if (err) {
				return cont(err);
			}
			return cont(null)
		})
	}).then(function (cont) {
		// 获取原教材结构
		db.collection('knowledge_tree').updateOne({ _id: tree_id }, { $set: { 'invalid': 1 } }, function (err, book) {
			if (err) {
				return cont(err);
			}
			let cacheKeyPkg = 'kb_api:v2:knowledge_trees_pkg:*';
			let cacheKeyBook = 'kb_api:v2:knowledge_trees:' + tree_id + '*';
			rediser.getCache().keys(cacheKeyPkg, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			rediser.getCache().keys(cacheKeyBook, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			return responseWrapper.succ({});
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

function put_tree_catalog(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	let selectPeriodCatalog;
	try {
		assert(req.body.period);
		assert(req.body.subject);
		assert(req.body.trees);
	} catch (e) {
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	// 数据查询 ----------------------------
	Thenjs(function (cont) {

		db.collection('catalog').findOne({ _id: 'knowledge_tree' }, function (err, catalog) {
			if (err) {
				return cont(err);
			}
			if (!catalog) {
				return cont('catalog 为空');
			}
			return cont(null, catalog);
		})
	}).then(function (cont, catalog) {
		selectPeriodCatalog = _.find(catalog.periods, function (periodCatalog) {
			return periodCatalog.name === req.body.period;
		});
		if (!selectPeriodCatalog) {
			return cont('没有发现该学段')
		}
		let selectSubjectCatalog = _.find(selectPeriodCatalog.subjects, function (subjectCatalog) {
			return subjectCatalog.name === req.body.subject;
		});
		if (!selectSubjectCatalog) {
			return cont('没有发现该学科')
		}
		let localIdArray = [];
		_.each(selectSubjectCatalog.trees, function (tree) {
			let curItem = { 'id': tree.id, 'path': tree.name };
			localIdArray.push(curItem);
		});
		let postIdArray = [];
		_.each(req.body.trees, function (tree) {
			let curItem = { 'id': tree.id, 'path': tree.name };
			postIdArray.push(curItem);
		});
		return cont(null, localIdArray, postIdArray, catalog);
	}).then(function (cont, localIdArray, postIdArray, catalog) {
		if (localIdArray.length === postIdArray.length) {
			let isLegal = true;
			_.each(localIdArray, function (localInfo) {
				let findPostInfo = _.find(postIdArray, function (postInfo) {
					return postInfo.id === localInfo.id;
				});
				if (!findPostInfo) {
					isLegal = false;
				} else {
					if (findPostInfo.path !== localInfo.path) {
						isLegal = false;
					}
				}
			});
			if (!isLegal) {
				return cont('编目数据冲突')
			} else {
				return cont(null, catalog);
			}
		} else {
			return cont('编目数据冲突')
		}
	}).then(function (cont, catalog) {
		let i;
		for (i = 0; i < selectPeriodCatalog.subjects.length; ++i) {
			if (selectPeriodCatalog.subjects[i].name === req.body.subject) {
				break;
			}
		}
		selectPeriodCatalog.subjects[i].trees = req.body.trees;
		catalog['utime'] = new Date();
		db.collection('catalog').updateOne({ _id: 'knowledge_tree' }, catalog, function (err, result) {
			if (err) {
				return cont(err);
			}
			let cacheKeyPkg = 'kb_api:v2:knowledge_trees_pkg:*';
			rediser.getCache().keys(cacheKeyPkg, function (err, curkeys) {
				if (err) {
					Logger.error(err);
				}
				rediser.del(curkeys);
			});
			return responseWrapper.succ({});
		})

	}).fail(function (cont, error) {
		Logger.error(error);
		if (typeof error === 'string') {
			return responseWrapper.error('HANDLE_ERROR', error);
		}
		return responseWrapper.error('HANDLE_ERROR',);
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

// 获取某知识树下所有知识点列表
function getKnowledgesByKnowledgeTree(req, res) {
	var responseWrapper = new ResponseWrapper(res);
	var params = null;
	try {
		var fun_name = arguments.callee.name;
		params = params_utils.create_params(req, kb_api[fun_name]);
		var cache_key = params['cache_key'] + ':' + process.env.NODE_PORT;
	} catch (e) {
		Logger.error(e);
		return responseWrapper.error('PARAMETERS_ERROR', e.message);
	}

	const queryDbs = new Set((req.query.dbs || 'basic').split(','));

	// 数据查询 ----------------------------
	Thenjs(function (cont) {

		rediser.get(cache_key, function (err, tree) {
			if (tree) {
				// 过滤无权限的知识树
				if (!((tree.dbs || ['basic']).some(item => queryDbs.has(item)))) {
					return responseWrapper.error('NULL_ERROR');
				}
				const curKnowledges = _getKnowledge(tree.knowledge_tree.children);
				return responseWrapper.succ({
					total: curKnowledges.length,
					list: curKnowledges,
				});
			}
			return cont(null);
		});
	}).then(function (cont) {
		var coll = db.collection('knowledge_tree');
		// 查询条件
		var cond = {
			_id: params['knowledge_tree_id'],
		};
		// 提取出的字段
		var proj = {
			has_modified: 0,
		};
		// 查询
		coll.findOne(cond, proj, function (err, knowledge_tree) {
			if (err) {
				return cont(err);
			}
			if (!knowledge_tree) {
				return responseWrapper.error('NULL_ERROR');
			}
			return cont(null, knowledge_tree);
		});
	}).then(function (cont, knowledge_tree) {
		var chapterIds = [];
		_getChapterIdsInBook('nodes', knowledge_tree.nodes, chapterIds);
		var proj = {
			ctime: 0,
			utime: 0,
			path: 0
		}
		db.collection('knowledge_tree_chapter').find({ _id: { $in: chapterIds } }).project(proj).toArray(function (err, chapters) {
			if (err) {
				return cont(err);
			}
			return cont(null, knowledge_tree, chapters);
		});

	}).then(function (cont, knowledge_tree, chapters) {
		knowledge_tree = _assignChaptersToBook('knowledge_tree', knowledge_tree, chapters);
		knowledge_tree['id'] = knowledge_tree['_id'];
		delete knowledge_tree['_id'];

		knowledge_tree['knowledge_tree'] = {
			'nodes': knowledge_tree['nodes'],
		};
		delete knowledge_tree['nodes'];
		var keys = {
			'nodes': 'knowledge_node',
			'knowledges': 'knowledge',
		};
		loosen_tree(knowledge_tree['knowledge_tree'], keys);
		rediser.set(cache_key, knowledge_tree, 60 * 30);
		// 过滤无权限的知识树
		if (!((knowledge_tree.dbs || ['basic']).some(item => queryDbs.has(item)))) {
			return responseWrapper.error('NULL_ERROR');
		}
		const curKnowledges = _getKnowledge(knowledge_tree.knowledge_tree.children);
		return responseWrapper.succ({
			total: curKnowledges.length,
			list: curKnowledges,
		});
	}).fail(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	}).finally(function (cont, error) {
		Logger.error(error);
		return responseWrapper.error('HANDLE_ERROR');
	});
}

// helper
function _getKnowledge(data) {
	let knowledges = [];
	for (let info of data) {
		if (info.key === 'knowledge') {
			knowledges.push(info);
		} else if (info.children && info.children.length > 0) {
			const curKnowledges = _getKnowledge(info.children);
			knowledges = knowledges.concat(curKnowledges);
		}
	}
	return knowledges;
}

module.exports = {
	knowledge_trees: knowledge_trees,
	knowledge_tree: knowledge_tree,
	knowledge_tree_by_name: knowledge_tree_by_name,
	knowledge_tree_struct: knowledge_tree_struct,
	tree_chapter: tree_chapter,

	check_tree_conflict: check_tree_conflict,
	post_tree_struct: post_tree_struct,
	post_tree_chapters: post_tree_chapters,
	delete_tree: delete_tree,
	put_tree_catalog: put_tree_catalog,
	getKnowledgesByKnowledgeTree: getKnowledgesByKnowledgeTree,
}
