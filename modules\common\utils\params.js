
var int_reg = /^[0-9]+$/;
var bracket_reg = /({\w+?})/;
var bracket_cont_reg = /^{(\w+?)}$/;

function create_params(req, conf) {
	var params = {};
	for (var p in conf) {
		if ('cache_key' == p) {
			continue;
		}
		var desc = conf[p];
		var detail = desc['detail'];

		var position = desc['position'].toLowerCase();
		var required = desc['required'];
		var type = detail['type'];
		var def = detail['default'];
		var ranges = detail['ranges'];
		var post_process = detail['post_process'];

		var _v = null;
		_v = req[position][p];
		// 必输性
		if (required && _v == null) {
			throw (p + "不能为空！");
		}
		// 默认值
		if (!_v && _v !== 0) {
			if (null != def) {
				_v = def;
			} else {
				continue;
			}
		}
		// 类型
		if ('int' == type) {
			if (!int_reg.test(_v)) {
				throw (p + "不是整数！");
			}
			_v = parseInt(_v);
		} else if ('array' == type) {
			if (!(_v instanceof Array)) {
				throw (p + "不是数组！");
			}
		}
		// 有效值验证
		if (ranges instanceof Function) {
			var _t = false;
			try {
				_t = ranges(_v);
			} catch(e) {
				throw (p + e);
			}
			if (!_t) {
				throw (p + "不合法！");
			}
		} else if (ranges instanceof Array) {
			if (ranges.indexOf(_v) < 0) {
				throw (p + "不合法！");
			}
		}
		// 后处理
		if (post_process) {
			_v = post_process(_v);
		}
		params[p] = _v;
	}
	var tpl = conf['cache_key'];
	if (tpl) {
		var arr = tpl.split(bracket_reg);
		var cache_key = '';
		for (var i in arr) {
			var v = arr[i];
			if (bracket_cont_reg.test(v)) {
				var k = v.substring(1, v.length-1);
				cache_key += params[k];
			} else {
				cache_key += v;
			}
		}
	}
	params['cache_key'] = cache_key;
	return params;
}

module.exports = {
	create_params: create_params,
}
