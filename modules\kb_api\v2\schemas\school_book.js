const Ajv = require('ajv');
const ajv = new Ajv({ removeAdditional: 'all' });
const Logger = require('../../../common/utils/logger');

const period = {
    type: 'object',
    required: ['name', 'grades'],
    properties: {
        name: {
            type: 'string',
            minLength: 1
        },
        grades: {
            type: 'array',
            minItems: 1,
        }
    }
};


const postSchema = {
    type: 'object',
    additionalProperties: false,
    required: ['id', 'name', 'province', 'city', 'country', 'periods'],
    properties: {
        id: { type: 'integer', minimum: 1 },
        name: { type: 'string', minLength: 1 },
        province: { type: 'string', minLength: 1 },
        city: { type: 'string' },
        country: { type: 'string' },
        periods: {
            type: 'array',
            minItems: 1,
            items: period
        }
    }
};

const putSchema = {
    type: 'object',
    additionalProperties: false,
    required: ['name', 'province', 'city', 'country', 'periods'],
    properties: {
        name: { type: 'string', minLength: 1 },
        province: { type: 'string', minLength: 1 },
        city: { type: 'string' },
        country: { type: 'string' },
        periods: {
            type: 'array',
            minItems: 1,
            items: period
        }
    }
};

const putRegionSchema = {
    type: 'object',
    additionalProperties: false,
    required: ['province', 'city', 'country'],
    properties: {
        province: { type: 'string', minLength: 1 },
        city: { type: 'string' },
        country: { type: 'string' },
    }
};

const postValidate = (data) => {
    if (!ajv.validate(postSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('格式校验错误');
    }
};


const putValidate = (data) => {
    if (!ajv.validate(putSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('格式校验错误');
    }
};
const putRegionValidate = (data) => {
    if (!ajv.validate(putRegionSchema, data)) {
        Logger.error(ajv.errorsText());
        throw new Error('格式校验错误');
    }
};

module.exports = {
    postValidate,
    putValidate,
    putRegionValidate,
};
