const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const _ = require('lodash');
const ResponseWrapper = require('../../common/utils/response_wrapper');

const getKnowledgeAnalysis = async (req, res) => {
    let responseWrapper = new ResponseWrapper(res);
    // 获取所有试卷ID
    let exampaperIds = req.body.paper_ids || [];
    let fromLimit = req.body.from_limit || [];
    let exampaperArr = [];
    _.each(exampaperIds, function (exampaper) {
        exampaperArr.push(Number(exampaper));
    });
    // 获取所有试卷
    let proj = {'_id':1, 'blocks':1, 'from':1}
    let data = await db.collection('exampaper').find({ _id: { $in: exampaperArr } }).project(proj).toArray();

    // 获取所有试题及其所在的试卷
    let qids = [];
    let quesPaper = {}
    let paperNum = 0
    data.forEach(exampaper => {
        let paper_id = exampaper['_id']
        // 限制来源
        let paper_from = exampaper.from || ''
        if (!fromLimit.includes(paper_from)){
            paperNum = paperNum + 1
            let blocks = exampaper.blocks || [];
            for (let blk of blocks) {
                let qs = blk.questions || [];
                for (let q of qs) {
                    if (_.isObject(q) && q.id) {
                        // 所有试题
                        if (!qids.includes(q.id)){
                            qids.push(q.id);
                        }
                        // 试题试卷关系
                        let ques_paper = quesPaper[q.id] || [];
                        if (!ques_paper.includes(paper_id)){
                            ques_paper.push(paper_id)
                        }
                        quesPaper[q.id] = ques_paper 
                    }
                }
            }   
        }
    });

    // 获取试题详情
    let quesProj = {_id:1, knowledges: 1, type: 1, difficulty: 1}
    let allQuestions = await db.collection('question').find({ _id:{$in:qids}}).project(quesProj).toArray();
    
    let knowledgesDetail = {}
    allQuestions.forEach(question => {
        let ques_id = question._id
        let qtype = question.type || '';
        let knowledges = question.knowledges || [];
        let qdiff = question.difficulty || 0.5;
        
        let filterKnow = []
        knowledges.forEach(know => {
            let knowId = know.id;
            let knowName = know.name;
            if (!filterKnow.includes(knowId)){    
                filterKnow.push(knowId)       
                let oldKnow = knowledgesDetail[knowId] || {};
                // 知识点ID和名字
                oldKnow['id'] = knowId
                oldKnow['name'] = knowName

                // 试题类型
                let ques_type = oldKnow.ques_type || [];
                let type_sign = false
                let new_ques_type = []
                ques_type.forEach(type_dict => {
                    if (type_dict.name == qtype){
                        type_dict.num += 1
                        type_sign = true
                    }
                    new_ques_type.push(type_dict)
                })
                if (!type_sign){
                    new_ques_type.push({'name': qtype, 'num':1})
                }
                new_ques_type = _.reverse(_.sortBy(new_ques_type, function(it) {
                    return it.num
                }))
                oldKnow['ques_type'] = new_ques_type

                // 考察试卷
                let paperIds = oldKnow.papers || [];
                let quesPaperIds = quesPaper[ques_id] || [];
                quesPaperIds.forEach(one =>{
                    if (!paperIds.includes(one)){
                        paperIds.push(one)
                    }
                })
                let addNum = quesPaperIds.length

                // 考察试题
                let questions = oldKnow.questions || [];
                questions.push({'id': ques_id, 'type': qtype, 'times':addNum})
                questions = _.reverse(_.sortBy(questions, function(it) {
                    return it.type
                }))
                oldKnow['questions'] = questions

                // 平均难度
                let ques_num = oldKnow.ques_num || 0;
                let avg_diff = oldKnow.avg_diff || 0;
                let total_diff = (ques_num * avg_diff + addNum * qdiff) / (ques_num + addNum)
                oldKnow['avg_diff'] = Math.floor(total_diff * 100) / 100

                // 知识点试题数量
                oldKnow['ques_num'] = ques_num + addNum

                oldKnow['papers'] = paperIds
                oldKnow['exam_rate'] = Math.round(paperIds.length / paperNum * 100)
                knowledgesDetail[knowId] = oldKnow
            }
        })
    })
    knowledges = [];
    for(let know in knowledgesDetail){
        knowledges.push(knowledgesDetail[know])
    }
    let statisticResult = {
        'refer_paper_num': paperNum,
        'know_num': knowledges.length,
        'knowledges': knowledges
    }
    return responseWrapper.succ(statisticResult)
}

module.exports = {
    getKnowledgeAnalysis
}