const lodash = require('lodash');

/**
 * 试题难度小数转5级正数
 * kb.question.difficulty 是小数格式，一般平台展示是1～5
 * @param {number} difficulty 
 * @returns {number}
 */
const transferDiffDecimal2Five = (difficulty) => {
    if (difficulty >= 0.85) {
        return 1;
    } else if (difficulty >= 0.7) {
        return 2;
    } else if (difficulty >= 0.55) {
        return 3;
    } else if (difficulty >= 0.35) {
        return 4;
    }
    return 5;
};

/**
 * 过滤 & 加工试题字段 - 用于获取试题信息时
 * @param {*} question              - 试题对象
 * @param {string} [fields_type=]   - 0返回全部域；1仅返回题干域；2仅返回解答相关域；
 */
function filter_fields(question, fields_type) {
    // 过滤字段
    let blocks = question['blocks'];
    if ('solution' === fields_type) {
        delete blocks['stems'];
        delete blocks['types'];
    } else if ('stem' === fields_type) {
        delete blocks['solutions'];
        delete blocks['answers'];
        delete blocks['explanations'];
        let refer_exam = question['refer_exampapers'];
        if (refer_exam && refer_exam.length > 5) {
            question['refer_exampapers'] = refer_exam.slice(0, 5);
        }
    } else if ('stem2' === fields_type) {
        delete blocks['solutions'];
        delete blocks['answers'];
        delete blocks['explanations'];
    } else if ('answer' === fields_type) {
        delete blocks['stems'];
        delete blocks['types'];
        delete blocks['solutions'];
        delete blocks['explanations'];
        delete question['refer_exampapers'];
        delete question['description'];
        delete question['comment'];
        delete question['type'];
    }
    // 将 _id 转为 id
    if (question.hasOwnProperty('_id')) {
        question['id'] = question._id;
        delete question['_id'];
    }
    // 转换难度
    let _diff = Number(question['difficulty']);
    if (_diff < 0 || _diff >= 0 && _diff < 0.35) {
        _diff = 5;
    } else if (_diff >= 0.35 && _diff < 0.55) {
        _diff = 4;
    } else if (_diff >= 0.55 && _diff < 0.7) {
        _diff = 3;
    } else if (_diff >= 0.7 && _diff < 0.85) {
        _diff = 2;
    } else if (_diff >= 0.85 && _diff <= 1 || _diff > 1) {
        _diff = 1;
    }
    question['difficulty'] = _diff;
    question['year'] = question['year'] || new Date(question['ctime']).getFullYear();
}

// 保留原来的知识点
function setOldKnow(question, oldKnows) {
    let knowJson = {};
    let newKnows = question.knowledges;
    let intersectionKnow = lodash.intersectionBy(oldKnows, newKnows, 'id');
    if (intersectionKnow.length > 0) {
        for (let i = 0; i < intersectionKnow.length; i++) {
            if (intersectionKnow[i] && intersectionKnow[i].id) {
                knowJson[intersectionKnow[i].id] = intersectionKnow[i];
            }
        }

        if (question.blocks) {
            if (Array.isArray(question.blocks.knowledges) && question.blocks.knowledges.length > 0) {
                for (let i = 0; i < question.blocks.knowledges.length; i++) {
                    for (let j = 0; j < question.blocks.knowledges[i].length; j++) {
                        let know = question.blocks.knowledges[i][j];
                        if (know && know.id && knowJson[know.id]) {
                            question.blocks.knowledges[i][j] = knowJson[know.id];
                        }
                    }
                }
            }
            if (Array.isArray(question.blocks.core_knowledges) && question.blocks.core_knowledges.length > 0) {
                for (let i = 0; i < question.blocks.core_knowledges.length; i++) {
                    for (let j = 0; j < question.blocks.core_knowledges[i].length; j++) {
                        let know = question.blocks.core_knowledges[i][j];
                        if (know && know.id && knowJson[know.id]) {
                            question.blocks.core_knowledges[i][j] = knowJson[know.id];
                        }
                    }
                }
            }
        }
        if (Array.isArray(question.knowledges) && question.knowledges.length > 0) {
            for (let i = 0; i < question.knowledges.length; i++) {
                let know = question.knowledges[i];
                if (know && know.id && knowJson[know.id]) {
                    question.knowledges[i] = knowJson[know.id];
                }
            }
        }
        if (Array.isArray(question.core_knowledges) && question.core_knowledges.length > 0) {
            for (let i = 0; i < question.core_knowledges.length; i++) {
                let know = question.core_knowledges[i];
                if (know && know.id && knowJson[know.id]) {
                    question.core_knowledges[i] = knowJson[know.id];
                }
            }
        }
    }
}

// 保留原来的叠加要素
function setOldElements(question, oldQuestion) {
    if (Array.isArray(oldQuestion.elements)) {
        question.elements = oldQuestion.elements;
    }
    if (question.blocks && oldQuestion.blocks && Array.isArray(oldQuestion.blocks.elements)) {
        question.blocks.elements = oldQuestion.blocks.elements;
    }
}

// 获取知识点id
const getKnowIds = (questions) => {
    let know_ids = [];
    if (Array.isArray(questions)){
        for (let ques of questions) {
            let knows = ques.knowledges || [];
            for (let k of knows) {
                know_ids.push(k.id);
            }
        }
    }
    return know_ids;
};

// 设置知识点的 videos
const setVideo = (questions, know_videos) => {
    for (let ques of questions) {
        ques.knowledges = ques.knowledges || [];
        for (let k of ques.knowledges) {
            let kn = know_videos[k.id] || {videos: [], chance: 0, score: 0, name: ''};
            k.videos = kn.videos;
            k.chance = kn.chance;
            k.score = kn.score;
            k.name = kn.name || k.name;
        }
    }
};

// 知识点名字为空
const knowNameNull =(questionArr) => {
    if (Array.isArray(questionArr)) {
        for (let question of questionArr) {
            let knowsArr = question.knowledges || [];
            for (let know of knowsArr) {
                know.name = know.name || '';
            }
        }
    }
};

module.exports = {
    filter_fields,
    setOldKnow,
    setOldElements,
    transferDiffDecimal2Five,
    getKnowIds,
    setVideo,
    knowNameNull,
};
