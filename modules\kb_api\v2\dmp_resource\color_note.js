const logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const ObjectID = require('mongodb').ObjectID;

const resourceCol = db.collection('color_note');

/* 预习复习视频 */

// 获取知识点名字
const _getKnowledgesInfo = async (ids) => {
    return await db.collection('knowledge').find({_id: {$in: ids}}).project({
        name: 1, period: 1, subject: 1,
    }).toArray();
};

// 获取笔记资源
const _getResourcesByKnowledgeIds = async (ids, school_id) => {
    return await resourceCol.find({
        school_id,
        'knowledges.id': {$in: ids},
        is_del: {$ne: true}
    }).project({is_del: 0}).toArray();
};

// 设置笔记关联知识点时，知识点原关联的资源会被取关（被新的关联覆盖）
const _unsetRelateKnowledges = async (knowledgeIds, school_id) => {
    let records = await _getResourcesByKnowledgeIds(knowledgeIds, school_id);
    if (records.length) {
        let ops = [];
        for (let e of records) {
            let knowledges = e.knowledges.filter(k => !knowledgeIds.includes(k.id));
            ops.push({
                updateOne: {
                    filter: {_id: e._id},
                    update: {
                        $set: {knowledges},
                    }
                }
            });
        }
        if (ops.length) {
            return await resourceCol.bulkWrite(ops);
        }
    }
};

// 保存资源
const saveResource = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let body = req.body;
    let {name, url, size, hash, user_id, user_name, school_id} = body;
    if (!name || !url || !+school_id) {
        return resWrapper.error('PARAMETERS_ERROR', '文件名字、链接、和学校ID必填');
    }

    try {
        let now = new Date();
        let doc = {
            _id: ObjectID(),
            name,
            url,
            size: size || 0,
            hash: hash || '',
            user_id: user_id || 0,
            user_name: user_name || '',
            school_id: +school_id,
            is_del: false,
            ctime: now,
            utime: now,
        };
        let result = await resourceCol.insertOne(doc);
    
        if (result && result.insertedCount) {
            return resWrapper.succ({id: doc._id});
        }

        throw new Error('三色笔记资源保存失败');
    } catch (error) {
        logger.error(error);
        resWrapper.error('HANDLE_ERROR', error.message);
    }
};

/**
 * 设置关联知识点
 * 多个资源关联多个知识点，每个资源关联的知识点包含所有知识点，知识点原关联的资源会被取消关联
 * @param {*} req 
 * @param {*} res 
 */
const setKnowledges = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let body = req.body;
    let {resource_ids, knowledge_ids, school_id, period, subject} = body;
    if (!resource_ids || !resource_ids.length || !knowledge_ids || !knowledge_ids.length || !+school_id) {
        return resWrapper.error('PARAMETERS_ERROR', '资源、知识点和学校ID必填');
    }

    try {
        // 知识点已经关联的资源取消关联
        // await _unsetRelateKnowledges(knowledge_ids, school_id);
        
        let knowledges = await _getKnowledgesInfo(knowledge_ids);
        if (!knowledges.length) {
            return resWrapper.error('PARAMETERS_ERROR', '知识点不存在');
        }

        if (!period || !subject) {
            period = knowledges[0].period;
            subject = knowledges[0].subject;
        }

        knowledges = knowledges.map(e => ({id: e._id, name: e.name}));
        resource_ids = resource_ids.map(e => ObjectID(e));

        let result = await resourceCol.updateMany({
            _id: {$in: resource_ids},
        }, {
            $set: {
                period,
                subject,
                knowledges,
                update: new Date(),
            }
        });
        
        resWrapper.succ(result.result);
    } catch (error) {
        logger.error(error);
        resWrapper.error('HANDLE_ERROR');
    }
};

// 根据知识点获取资源
const getResources = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    
    let {knowledge_id, school_id} = req.query;
    knowledge_id = (knowledge_id || '').split(',').map(e => +e).filter(e => e);
    school_id = +school_id;

    if (!knowledge_id.length || !school_id) {
        return resWrapper.error('PARAMETERS_ERROR', '知识点和学校ID必填');
    }

    try {
        let resources = await _getResourcesByKnowledgeIds(knowledge_id, school_id);
        for (let e of resources) {
            e.id = e._id;
            delete e._id;
        }
    
        resWrapper.succ(resources);
    } catch (error) {
        logger.error(error);
        resWrapper.error('HANDLE_ERROR');
    }
};

// 统计知识点下的资源量
const countByKnowledges = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let {knowledge_ids, school_id} = req.body;

    let resources = await resourceCol.find({
        'knowledges.id': {$in: knowledge_ids},
        school_id,
        is_del: {$ne: true},
    }).project({'knowledges.id': 1}).toArray();

    let countObj = {};

    for (let e of resources) {
        for (let k of e.knowledges) {
            countObj[k.id] ? (countObj[k.id]++) : (countObj[k.id] = 1);
        }
    }

    resWrapper.succ(countObj);
};

// 修改三色笔记
const setNote = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    
    let {id, name} = req.body;
    if (!id || id.length !== 24) {
        return resWrapper.error('PARAMETERS_ERROR', '三色笔记ID必填');
    }
    let result = await resourceCol.updateOne(
        {
            _id: ObjectID(id)
        },
        {
            $set: { 
                name: name,
                utime: new Date()
            }
        }
    );


    return resWrapper.succ(result.result);
}

const deleteNote = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    
    let {resource_id, knowledge_id} = req.query;
    if (!resource_id || resource_id.length !== 24) {
        return resWrapper.error('PARAMETERS_ERROR', '三色笔记ID必填');
    }
    if (!+knowledge_id) {
        return resWrapper.error('PARAMETERS_ERROR', '三色笔记所属知识点ID必填');
    }
    let result = await resourceCol.updateOne(
        {
            _id: ObjectID(resource_id)
        },
        {
            $pull: { 
                knowledges: {id: +knowledge_id}
            }
        }
    );


    return resWrapper.succ(result.result);
}

module.exports = {
    saveResource,
    setKnowledges,
    getResources,
    countByKnowledges,
    setNote,
    deleteNote
};
