// dmp平台调用接口
/*
 * 根据单个知识点，筛选项，排序规则，获取试题
 * 现dmp 2.5 筛选项包括：*已编精品题、*精准匹配、?难度、?题型、*数量，排序规则：年份-热度（refer_times）-相似题数量
 * 针对单个知识点需要返回：例题example，习题exercise，作业题homework
 */

const _ = require('lodash');
const logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const _diff = require('./utils').transferDiffDecimal2Five;
const config = require('config');
const SE_API_SERVER = config.get('SE_API_SERVER');
const SEARCH_QUES_URL = `${SE_API_SERVER.protocol}://${SE_API_SERVER.hostname}:${SE_API_SERVER.port}/se_kb/v2/filter/questions?api_key=${SE_API_SERVER.appKey}`;
const axios = require('axios');
const { filter_fields } = require('./utils');

const knowledgeCol = db.collection('knowledge');
const questionCol = db.collection('question');

const _sortValues = (array) => array.sort((a, b) => a - b);

const DmpLogicCompare = function (a, b) {
    return (b.year - a.year) || (b.refer_times - a.refer_times) || (b.reco_len - a.reco_len);
};

//------------------------ 以下，通过查询知识点集合表筛选试题：by knowledge ------------------------//

/**
 * 知识点集合 knowledge 中 questions 的元素
 * @typedef {Object} Question
 * @property {number} id
 * @property {string} type
 * @property {number} diff - 试题难度：1 - 5，数字越大，难度越大
 * @property {number} refer_times - 引用试卷个数，与papers.length相同，dmp视为热度
 * @property {{year: number}[]} papers
 * @property {number} iselt - 是否精编：1，0，undefined/null
 * @property {[number]} kns - 关联知识点，dmp要求【精准匹配】，及 kns.length == 1
 * @property {number} [year] - 取papers首元素年份
 * @property {number} [reco_len] - 相似题数量
 */

/**
 * dmp自动化配置设置的例题/习题/作业题的推荐规则
 * dmp保存规则的数据结构可能与此不同，dmp调用kb推荐接口时需要转换规则为以下结构
 * 一个教材配置的【讲义题目和作业题目】推荐规则可分解为【一个或多个例题习题推荐Rule】和【一个或多个作业题推荐Rule】，即Array<Rule>
 * 试题量：dmp2.5推荐规则可分为【例题习题推荐】和【作业题推荐】，不共用筛选条件（如题型、难度），故加以区分
 * 例题习题推荐的筛选条件（如题型、难度）一致，只是题目数量有别
 * 作业题推荐的筛选条件只有难度和数量
 * @typedef {Object} Rule
 * @property {string} type - 试题类型
 * @property {string} difficulty - 试题难度
 * @property {number} limit - 试题量
 * @property {[number]} [elite] - 精品题状态，dmp2.5默认【已编精品题】；-1,0,1，可以指定多个
 */

/*
 * 难度映射（knowledge.questions.diff）分五个等级，1，2，3，4，5，'容易', '较易', '中等', '较难', '困难'
 * dmp2.5分3个等级，容易 | 普通 | 困难，传入参数difficulty指定
 * dmp平台可以灵活控制，
 * 如（推荐）：
 *     容易：difficulty='容易,较易', (0.71 ~ 0.99)（根据线上有2999道试题的知识点1992491007所做统计）
 *     普通：difficulty='中等,较难', (0.37 ~ 0.69)
 *     困难：difficulty='困难', (<0.33)
 * 或：
 *     容易：difficulty='容易',
 *     普通：difficulty='较易,中等,较难',
 *     困难：difficulty='困难',
 * 等等
 */

/*
 * 知识点详情里的试题列表包含试题的年份（第一个引用试卷的年份）和难度、题型、引用次数等信息，
 * 按规则【题型/难度】筛选过以后的【试题列表1】，按【年份和引用次数】排序得到的【有序数组】，
 * 此时取在【规则限定的题目数量】所对应的索引位置的试题的【年份和引用次数】，
 * 对【有序数组】截取包含此【年份+引用次数】的试题列表（应比按规则筛选过以后的【试题列表1】短少）
 */


// 搜索引擎获取知识点下精编试题
const searchEliteQuestionsByKnowledgeId = async (kid) => {
    let result = await axios.post(SEARCH_QUES_URL, {
        knowledges: `${kid}`,
        elite: 1,
        limit: 1000,
        offset: 0,
        sort_by: [{ year: -1 }, { ncite: -1 }] // 年份和引用次数
    });

    if (result.status === 200 && result.data.code === 0) {
        return result.data.data.datas;
    }

    return [];
};

// 根据试题ID获取试题信息：难度、年份、相似题数量、包含知识点数量、引用试卷区域、引用次数等
const getQuestionsByIds = async (ids, returnMap) => {
    let records = await questionCol.find({
        _id: { $in: ids },
    }, { readPreference: 'secondaryPreferred' }).project({
        elite: 1, difficulty: 1, type: 1, 'knowledges.id': 1,
        'refer_exampapers.province': 1, 'refer_exampapers.city': 1,
        reco_questions: 1, year: 1, region: 1, province: 1, city: 1
    }).toArray();

    let quesMap = {};
    for (let e of records) {
        e.id = e._id;
        delete e._id;
        e.diff = _diff(e.difficulty);
        delete e.difficulty;
        e.year = e.year || 0;
        e.reco_len = (e.reco_questions || []).length;
        delete e.reco_questions;
        e.kns = (e.knowledges || []).length;
        delete e.knowledges;
        e.iselt = e.elite;
        delete e.elite;

        e.refer_exampapers = e.refer_exampapers || [];
        e.refer_times = e.refer_exampapers.length;
        if (e.refer_exampapers.length) {
            e.regions = e.refer_exampapers.map(ele => ({
                province: ele.province,
                city: ele.city
            }));
        } else if (e.province || e.region) {
            e.regions = [{
                province: e.province || e.region,
                city: e.city || '',
            }];
        } else e.regions = [];

        delete e.refer_exampapers;
        delete e.region;
        delete e.province;
        delete e.city;

        if (returnMap) quesMap[e.id] = e;
    }
    return returnMap ? quesMap : records;
};

// 获取指定知识点的试题列表
const _getQuestionsByKnowledgeId = async ({ knowledgeId, elite, dbs = new Set(['basic']) }) => {
    let knowledge;
    if (elite === 1) {
        // 指定只限精品题目，单个知识点题量比较大的情况下，通过聚合过滤会较快（在kb测试库通过客户端测试）
        knowledge = await knowledgeCol.aggregate([{
            $match: { _id: knowledgeId },
        }, {
            $project: {
                questions: {
                    $filter: {
                        input: '$questions',
                        as: 'item',
                        cond: { $eq: ['$$item.iselt', 1] },
                    }
                }
            }
        }, {
            $project: {
                'questions.id': 1
            }
        }]).toArray();
        knowledge = knowledge[0];
        if (!knowledge || !((knowledge.dbs || ['basic']).some(item => dbs.has(item)))) return [];
        return await getQuestionsByIds(knowledge.questions.map(e => e.id));
    }
    knowledge = await knowledgeCol.findOne({ _id: +knowledgeId }, {
        fields: {
            'questions.id': 1,
            'questions.iselt': 1,
            'questions.diff': 1,
            'questions.type': 1,
            'questions.kns': 1,
            'questions.refer_times': 1,
        },
        readPreference: 'secondaryPreferred'
    });

    return (knowledge || {}).questions || [];
};

/**
 * 从试题表里获取试题信息
 * 附加相似题量信息和矫正年份信息
 * 附加区域信息，取最新引用试卷的province/city
 * 获取引用试卷地区信息
 * @param {*} questions 
 * @param {{region: boolean}} param1 - region区域筛选
 */
const _attachRecoLengthToQuestions = async (questions, { region } = {}) => {
    if (!questions || !questions.length) return [];
    let proj = { year: 1, reco_questions: 1, difficulty: 1 };
    if (region) {
        proj.refer_exampapers = 1;
        proj.region = 1;
    }

    let records = await questionCol.find({
        _id: { $in: questions.map(e => e.id) },
    }, { readPreference: 'secondaryPreferred' }).project(proj).toArray();

    let idMap = {};
    let ret = [];

    for (let i = 0; i < records.length; i++) {
        idMap[records[i]._id] = records[i];
    }
    for (let i = 0; i < questions.length; i++) {
        let record = idMap[questions[i].id];
        if (record) {
            questions[i].diff = _diff(record.difficulty);
            questions[i].year = record.year || 0;
            questions[i].reco_len = record.reco_questions ? record.reco_questions.length : 0;
            if (region) {
                record.refer_exampapers = record.refer_exampapers || [];
                questions[i].regions = record.refer_exampapers.map(e => ({
                    province: e.province,
                    city: e.city
                }));
            }
            ret.push(questions[i]);
        }
    }

    return ret;
};

/**
 * 处理知识点里的试题列表
 * 如添加year（取papers[0].year），按年份和引用次数排序，过滤已编精品和精准匹配，按题型和难度分组等
 * 1.map questions: [Question],
 * 2.filter questions(iselt=1, kns_len=1): [Question],
 * 3.sort by year: [Question],
 * 4.group by type/diff:
 * {
 *  questions: [Question]
 *  type: {
 *      '选择题': [number], // questions元素的索引值数组
 *      '填空题': [number],
 *      '多选题': [number],
 *  },
 *  diff: {
 *      '1': [number],
 *      '2': [number],
 *      '3': [number],
 *      '4': [number],
 *      '5': [number],
 *  }
 * }
 * @param {object} p0
 * @param {[Question]} p0.questions 
 * @param {[string]} p0.groups - 按 题型type、难度diff分组
 * @param {[number]} p0.excludes
 * @param {boolean} p0.unique - 唯一包含知识点，（精准匹配），为true表示精准匹配（question.kns.length = 1）
 * @returns {{questions: [Question], type: Object, diff: Object}}
 */
const _preProcessQuestions = async ({ questions, groups = [], excludes = [], elite, unique = true, region }) => {
    // 试题分组，key: type | diff，数组元素为试题的索引，因为试题列表已按年份和引用次数排序
    const _groupIndexBy = (questions, key) => {
        let ret = {};
        for (let i = 0, e; i < questions.length; i++) {
            questions[i].index = i;
            e = questions[i];
            if (e[key]) {
                ret[e[key]] ? (ret[e[key]].push(i)) : (ret[e[key]] = [i]);
            }
        }
        return ret;
    };

    let ret = { questions };

    let filter;
    if (excludes.length) _sortValues(excludes);

    // 指定精品状态，如：[1, 0, -1]，（指定多个精品状态的需求（智能练习）暂无用）
    if (_.isArray(elite) && elite.length) {
        if (excludes.length) {
            if (elite.length === 3) { // 不过滤精品状态
                if (unique) {
                    filter = function (e) {
                        return e.kns.length === 1 && _.sortedIndexOf(excludes, e.id) < 0;
                    };
                } else {
                    filter = (e) => (_.sortedIndexOf(excludes, e.id) < 0);
                }
            } else {
                if (unique) {
                    filter = function (e) {
                        return elite.indexOf(e.iselt) >= 0 && e.kns.length === 1 && _.sortedIndexOf(excludes, e.id) < 0;
                    };
                } else {
                    filter = function (e) {
                        return elite.indexOf(e.iselt) >= 0 && _.sortedIndexOf(excludes, e.id) < 0;
                    };
                }
            }
        } else {
            if (elite.length === 3) {
                if (unique) {
                    filter = function (e) {
                        return e.kns.length === 1;
                    };
                } // else filter = () => true;
            } else {
                if (unique) {
                    filter = function (e) {
                        return elite.indexOf(e.iselt) >= 0 && e.kns.length === 1;
                    };
                } else {
                    filter = function (e) {
                        return elite.indexOf(e.iselt) >= 0;
                    };
                }
            }
        }
    } else {
        let theElite = _.isNumber(elite) ? elite : 1;
        if (excludes.length) {
            if (unique) {
                filter = function (e) {
                    return e.iselt === theElite && e.kns.length === 1 && _.sortedIndexOf(excludes, e.id) < 0;
                };
            } else {
                filter = function (e) {
                    return e.iselt === theElite && _.sortedIndexOf(excludes, e.id) < 0;
                };
            }
        } else {
            if (unique) {
                filter = function (e) {
                    return e.iselt === theElite && e.kns.length === 1;
                };
            } else {
                filter = function (e) {
                    return e.iselt === theElite;
                };
            }
        }
    }

    if (filter) {
        let temp = [];
        for (let i = 0; i < ret.questions.length; i++) {
            if (filter(ret.questions[i])) {
                delete ret.questions[i].papers;
                ret.questions[i].kns = ret.questions[i].kns.length;
                temp.push(ret.questions[i]);
            }
        }
        ret.questions = temp;
    } else {
        for (let i = 0; i < ret.questions.length; i++) {
            delete ret.questions[i].papers;
            ret.questions[i].kns = ret.questions[i].kns.length;
        }
    }

    // 请求question表获取相似题数量信息、试题引用试卷信息等
    ret.questions = await _attachRecoLengthToQuestions(ret.questions, { region });
    let compare;

    // 指定精品状态，如：[1, 0, -1]，顺序有关系（指定多个精品状态需求（智能练习）暂无用）
    // 优先推荐精准匹配的题目
    if (_.isArray(elite) && elite.length) {
        if (unique) {
            compare = function (a, b) {
                return (elite.indexOf(a.iselt) - elite.indexOf(b.iselt)) || (b.year - a.year) || (b.refer_times - a.refer_times) || (b.reco_len - a.reco_len);
            };
        } else {
            compare = function (a, b) {
                return (a.kns - b.kns) || (elite.indexOf(a.iselt) - elite.indexOf(b.iselt)) || (b.year - a.year) || (b.refer_times - a.refer_times) || (b.reco_len - a.reco_len);
            };
        }
    } else {
        if (unique) {
            compare = DmpLogicCompare;
        } else {
            compare = function (a, b) {
                return (a.kns - b.kns) || (b.year - a.year) || (b.refer_times - a.refer_times) || (b.reco_len - a.reco_len);
            };
        }
    }

    ret.questions.sort(compare);
    ret.sorted = true;
    ret.compare = compare;

    if (groups.length) {
        for (let key of groups) {
            ret[key] = _groupIndexBy(ret.questions, key);
        }
    } else {
        for (let i = 0; i < ret.questions.length; i++) {
            ret.questions[i].index = i;
        }
    }

    return ret;
};

// 针对通过搜索引擎获取的精品试题，在匹配推荐规则前做过滤（excludes、unique）和排序（year refer_times reco_questions.length）
const _preFilterQuestions = ({ questions, groups = [], excludes = [], unique = true }) => {
    // 试题分组，key: type | diff，数组元素为试题的索引，因为试题列表已按年份和引用次数排序
    const _groupIndexBy = (questions, key) => {
        let ret = {};
        for (let i = 0, e; i < questions.length; i++) {
            questions[i].index = i;
            e = questions[i];
            if (e[key]) {
                ret[e[key]] ? (ret[e[key]].push(i)) : (ret[e[key]] = [i]);
            }
        }
        return ret;
    };

    let ret = { questions };

    let filter;
    if (excludes.length) _sortValues(excludes);

    if (excludes.length) {
        if (unique) {
            filter = function (e) {
                return e.iselt && e.kns.length === 1 && _.sortedIndexOf(excludes, e.id) < 0;
            };
        } else {
            filter = function (e) {
                return e.iselt && _.sortedIndexOf(excludes, e.id) < 0;
            };
        }
    } else {
        if (unique) {
            filter = function (e) {
                return e.iselt && e.kns.length === 1;
            };
        } else {
            filter = function (e) {
                return e.iselt;
            }
        }
    }

    if (filter) {
        ret.questions = ret.questions.filter(filter);
    }

    let compare;
    if (unique) {
        compare = DmpLogicCompare;
    } else {
        compare = function (a, b) {
            return (a.kns - b.kns) || (b.year - a.year) || (b.refer_times - a.refer_times) || (b.reco_len - a.reco_len);
        };
    }

    ret.questions.sort(compare);
    ret.sorted = true;
    ret.compare = compare;

    if (groups.length) {
        for (let key of groups) {
            ret[key] = _groupIndexBy(ret.questions, key);
        }
    } else {
        for (let i = 0; i < ret.questions.length; i++) {
            ret.questions[i].index = i;
        }
    }

    return ret;
};

// 分析规则，判断需要分组的字段，如规则限定题型则按题型分组，限定难度则按难度分组
const _getGroupKeysByRules = (rules) => {
    let keys = [];
    if (rules.find(e => e.type)) keys.push('type');
    if (rules.find(e => e.difficulty)) keys.push('diff');

    return keys;
};

/**
 * 难度转换
 * @param {[string]} difficulty - dmp传输过来的难度为字符串数组
 * @returns {[number]}
 */
const _mapDiffCond = (difficulty) => {
    if (typeof difficulty === 'string') {
        difficulty = difficulty.split(',').filter(e => e);
    }
    return _.uniq(difficulty.map(e => {
        let index = ['容易', '较易', '中等', '较难', '困难'].indexOf(e);
        if (index >= 0) return index + 1;
    }).filter(e => e));
};

// 从知识点里的试题列表筛选符合规则的试题
const _pickQuestionsByRule = (questionsWithGroups, rule, excludes = []) => {
    // 合并难度分组索引数组，因dmp指定的难度对应过来可能包含多个难度
    const _concatDiffGroup = (diffGroup, difficulty) => {
        let diffs = _mapDiffCond(difficulty);
        return _.flatten(diffs.map(e => diffGroup[e] || []));
    };

    let typeGroup = questionsWithGroups['type'];
    let diffGroup = questionsWithGroups['diff'];
    let indexes, questions;
    if (rule.type && rule.difficulty) {
        indexes = _.intersection(typeGroup[rule.type] || [], _concatDiffGroup(diffGroup, rule.difficulty));
        _sortValues(indexes);
    } else if (rule.type) {
        indexes = typeGroup[rule.type] || [];
    } else if (rule.difficulty) {
        indexes = _concatDiffGroup(diffGroup, rule.difficulty);
        _sortValues(indexes);
    }
    if (!indexes) {
        questions = questionsWithGroups.questions;
    } else if (!indexes.length) {
        return [];
    } else {
        questions = indexes.map(index => questionsWithGroups.questions[index]);
    }

    if (excludes.length) {
        _sortValues(excludes);
        questions = questions.filter(e => _.sortedIndexOf(excludes, e.id) < 0);
    }

    // 符合筛选条件的试题不足规则要求量
    if (questions.length <= rule.limit) return questions;
    // 试题已经year->refer_times->reco_len有序，直接截取要求的量
    if (questionsWithGroups.sorted) {
        return questions.slice(0, rule.limit);
    }
    // 试题year->refer_times有序，截取符合筛选条件的试题量，大于等于要求的量，会再按相似题量reco_len排序截取
    let limitQ = questions[rule.limit - 1];
    for (let i = rule.limit; i < questions.length; i++) {
        if (questions[i].iselt !== limitQ.iselt ||
            questions[i].year !== limitQ.year ||
            questions[i].refer_times !== limitQ.refer_times) {
            return questions.slice(0, i);
        }
    }
    return questions;
};

// 从知识点里的试题列表筛选符合规则的试题
const _pickQuestionsByRuleIgnoreLimit = (questionsWithGroups, rule, excludes = []) => {
    // 合并难度分组索引数组，因dmp指定的难度对应过来可能包含多个难度
    const _concatDiffGroup = (diffGroup, difficulty) => {
        let diffs = _mapDiffCond(difficulty);
        return _.flatten(diffs.map(e => diffGroup[e] || []));
    };

    let typeGroup = questionsWithGroups['type'];
    let diffGroup = questionsWithGroups['diff'];
    let indexes, questions;
    if (rule.type && rule.difficulty) {
        indexes = _.intersection(typeGroup[rule.type] || [], _concatDiffGroup(diffGroup, rule.difficulty));
        _sortValues(indexes);
    } else if (rule.type) {
        indexes = typeGroup[rule.type] || [];
    } else if (rule.difficulty) {
        indexes = _concatDiffGroup(diffGroup, rule.difficulty);
        _sortValues(indexes);
    }
    if (!indexes) {
        questions = questionsWithGroups.questions;
    } else if (!indexes.length) {
        return [];
    } else {
        questions = indexes.map(index => questionsWithGroups.questions[index]);
    }

    if (excludes.length) {
        _sortValues(excludes);
        questions = questions.filter(e => _.sortedIndexOf(excludes, e.id) < 0);
    }

    return questions;
};

/**
 * 按区域规则筛选试题
 * @param {[Rule]} rules 
 * @param {*} param1 
 */
const pickByRegions = (rules, { regions, limit, share = false }) => {
    // 【区域试题量】，不区分题型/难度，多个区域筛选项筛选试题：
    // 第一个区域筛选项，依次从符合规则的试题中筛选：
    // 符合第一个规则的试题筛选出符合区域的试题，获取的题量满足【区域题量】，结束筛选，
    // 否则，继续从符合第二个规则的试题中筛选符合区域的试题，加上已获取的区域试题，判断是否满足【区域题量】。。
    // 仍不满足，取第二个区域筛选项，再做筛选。。。
    if (!share) {
        const _pickByRegion = (rule, { province, city }, regionLimit) => {
            let questions = rule.unpicked || rule.questions;
            if (!questions.length) return [];
            let picked = [];
            let unpicked = [];
            if (regionLimit && regionLimit < questions.length) {
                let enough = false;
                for (let e of questions) {
                    if (!enough && e.regions.find(r => (r.province === province && (!city || city === r.city)))) {
                        picked.push(e);
                        if (picked.length >= regionLimit) {
                            enough = true;
                        }
                    } else unpicked.push(e);
                }
            } else {
                for (let e of questions) {
                    if (e.regions.find(r => (r.province === province && (!city || city === r.city)))) {
                        picked.push(e);
                    } else unpicked.push(e);
                }
            }
            rule.unpicked = unpicked;
            if (picked.length) {
                rule.picked = (rule.picked || []).concat(...picked);
            }
            return picked.length;
        };
        let regionPicked = 0;
        for (let region of regions) {
            for (let rule of rules) {
                let pickedCount = _pickByRegion(rule, region, Math.min(rule.limit, limit - regionPicked));
                if (pickedCount) {
                    regionPicked += pickedCount;
                    if (regionPicked >= limit) break;
                }
            }
            if (regionPicked >= limit) break;
        }
        // 所有区域筛选项都筛过以后，截取符合规则需求量的试题
        for (let rule of rules) {
            if (rule.questions.length <= rule.limit) {
                rule.no_more = true;
            } else {
                if (rule.picked && rule.picked.length) {
                    let needMore = rule.limit - rule.picked.length;
                    rule.picked.push(...rule.unpicked.slice(0, needMore));
                    rule.picked.sort((a, b) => (a.index - b.index));
                    rule.questions = rule.picked;
                } else {
                    rule.questions.splice(rule.limit);
                }
            }
            delete rule.unpicked;
            delete rule.picked;
        }
    } else {
        // share = ture
        // 区域题量均分到各个规则
        // todo
    }

};

/**
 * 
 * @param {*} questionsWithGroups 
 * @param {{suggest_type, region_rule: {regions: [{province, city}], limit}, rules}} suggestRule 
 * @param {*} excludes 
 * @returns {*}
 */
const pickQuestionsByRules = (questionsWithGroups, suggestRule, excludes) => {
    let rules = suggestRule.rules;
    if (suggestRule.region_rule && suggestRule.region_rule.limit) {
        for (let i = 0; i < rules.length; i++) {
            rules[i].questions = _pickQuestionsByRuleIgnoreLimit(questionsWithGroups, rules[i], excludes);
        }
        pickByRegions(rules, suggestRule.region_rule);
    } else {
        for (let i = 0; i < rules.length; i++) {
            rules[i].questions = _pickQuestionsByRule(questionsWithGroups, rules[i], excludes);
        }
    }

    return suggestRule.rules;
};

const _pickIds = (ruleWithQuestions) => {
    let ids = [];
    for (let i = 0; i < ruleWithQuestions.length; i++) {
        for (let j = 0; j < ruleWithQuestions[i].questions.length; j++) {
            ids.push(ruleWithQuestions[i].questions[j].id);
        }
    }
    return ids;
};

/**
 * 
 * @param {object} param0
 * @param {number} param0.knowledgeId 
 * @param {[{suggest_type, rules: [Rule], excludes}]} param0.ruleGroups - 推荐规则
 * @param {[number]} param0.excludes
 * @param {boolean} param0.exclusive - 为true，表示一个试题不可已被推荐到多个规则下。即上一个规则选中的试题自动添加到下个筛选规则的excludes中
 */
const suggestByKnowledgeIdAndRules = async ({ knowledgeId, ruleGroups, excludes = [], elite, detail, unique, dbs }) => {
    // 获取知识点里的questions
    let questions = await _getQuestionsByKnowledgeId({ knowledgeId, elite, dbs });
    let questionsWithGroups;
    let groups = _getGroupKeysByRules(_.flatten(ruleGroups.map(e => e.rules)));
    if (elite === 1) {
        questionsWithGroups = _preFilterQuestions({
            questions,
            groups,
            excludes,
            unique,
        });
    } else {
        // 预处理：筛选iselt=1 && kns.length=1，排序year && refer_times (&& reco_len)，分组type, diff
        questionsWithGroups = await _preProcessQuestions({
            questions,
            groups: _getGroupKeysByRules(_.flatten(ruleGroups.map(e => e.rules))),
            excludes,
            elite,
            detail,
            unique,
            region: !!ruleGroups.find(e => e.region_rule),
        });
    }

    excludes = [];
    let resultGroups = [];
    for (let i = 0; i < ruleGroups.length; i++) {
        // ruleGroups[i].rules 对应一个推荐列表
        let ruleWithQuestions = pickQuestionsByRules(questionsWithGroups, ruleGroups[i], excludes.concat(ruleGroups[i].excludes || []));
        delete ruleGroups[i].rules;
        resultGroups.push({
            ...ruleGroups[i],
            rule_questions: ruleWithQuestions
        });
        if (ruleGroups[i].exclusive) excludes.push(..._pickIds(ruleWithQuestions));
    }

    return resultGroups;
};

// 针对搜索引擎获取的精品题目推荐
const suggestByQuestionsAndRules = ({ questions, ruleGroups, excludes = [], unique }) => {

    let questionsWithGroups = _preFilterQuestions({
        questions,
        groups: _getGroupKeysByRules(_.flatten(ruleGroups.map(e => e.rules))),
        excludes,
        unique,
    });

    excludes = [];
    let resultGroups = [];
    for (let i = 0; i < ruleGroups.length; i++) {
        // ruleGroups[i].rules 对应一个推荐列表
        let ruleWithQuestions = pickQuestionsByRules(questionsWithGroups, ruleGroups[i], excludes.concat(ruleGroups[i].excludes || []));
        delete ruleGroups[i].rules;
        resultGroups.push({
            ...ruleGroups[i],
            rule_questions: ruleWithQuestions
        });
        if (ruleGroups[i].exclusive) excludes.push(..._pickIds(ruleWithQuestions));
    }

    return resultGroups;
};

/**
 * 筛选试题，根据知识点ID和推荐规则
 * @param {{body: {
 *  knowledge_excludes: [{knowledge_id, excludes, rule_groups}], 
 *  rule_groups: [[{type, difficulty, limit}]],
 *  elite: [number],
 *  detail: boolean}}} req 
 * @param {{body: {data: [{knowledge_id, rule_groups: [{rules: [{type, difficulty, limit, questions}], suggest_type, excludes}]}]}}} res 
 */
const suggestQuestions = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    // [{knowledge_id, excludes}]
    let knowledgeWithExcludes = req.body.knowledge_excludes || [];
    // [{rules:[{type, difficulty, limit}], suggest_type, excludes}]
    let ruleGroups = req.body.rule_groups || [];
    let elite = req.body.elite; // 默认已编精品
    let detail = req.body.detail; // 默认不响应试题详情
    let matchType = req.body.match_type || 'unique'; // 知识点精准匹配或包含，unique | multi
    const queryDbs = new Set(req.body.dbs || ['basic']);        // 权限管理

    try {
        let retval = await Promise.all(knowledgeWithExcludes.map(async e => {
            // e.rule_groups || ruleGroups: 支持每个知识点对应指定不同的规则组（即请求不同的试题列表）
            let unique = (e.match_type || matchType) === 'unique';
            let ret = await suggestByKnowledgeIdAndRules({
                knowledgeId: e.knowledge_id,
                ruleGroups: e.rule_groups || ruleGroups,
                excludes: e.excludes || [],
                elite: elite || 1,
                detail,
                unique,
                queryDbs,
            });
            return {
                knowledge_id: e.knowledge_id,
                rule_groups: ret
            };
        }));

        resWrapper.succ(retval);
    } catch (err) {
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * 通过知识点筛选试题，允许试题包含该知识点即可，v2.10之前的逻辑是精准匹配，即试题值包含指定的知识点；
 * 请求例题习题推荐，允许不同知识点推送的题目有重叠，在展示系统推荐时应去重处理，因为一个考法可以包含多个知识点，
 * 如果该考法包含的多个知识点推送的题目有重叠，展示该考法下系统推荐题目是应去重复处理；
 * 
 * 请求作业推荐，因作业题目总数是规则设定的，分配到每个知识点的题目数量是一定的，期望多个知识点推送的题目数量不应有重叠，
 * 若有重叠，总的推题数量就不能符合规则要求，
 * 
 * 针对作业推荐，首先获取每个知识点下的精品题目（通过搜索引擎接口），在查询试题表，获取：
 * 年份、引用试卷、相似题、难度、
 * 逐个知识点做推荐，当前知识点的推荐题目，如果有包含多个知识点的题目，作为下个知识点推题规则的excludes
 * @param {*} req 
 * @param {*} res 
 */
const suggestQuestionsForMultiSupport = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    try {
        let knowledges = req.body.knowledge_excludes || [];
        let kids = knowledges.map(e => e.knowledge_id);

        let quesGroup = await Promise.all(kids.map(async kid => {
            let quesIds = await searchEliteQuestionsByKnowledgeId(kid);
            return await getQuestionsByIds(quesIds);
        }));
        let kidMap = _.zipObject(kids, quesGroup);

        let unique = (req.body.match_type || 'unique') === 'unique';
        let ret = [];
        if (unique) {
            for (let kid of kids) {
                let questions = kidMap[kid];
                let item = knowledges.find(e => e.knowledge_id === kid);

                let result = suggestByQuestionsAndRules({
                    questions,
                    ruleGroups: item.rule_groups,
                    excludes: item.excludes || [],
                    unique,
                });

                ret.push({
                    knowledge_id: kid,
                    rule_groups: result,
                });
            }
        } else {
            let _excludes = [];
            for (let kid of kids) {
                let questions = kidMap[kid];
                let item = knowledges.find(e => e.knowledge_id === kid);

                let excludes = _excludes.concat(item.excludes || []);
                let result = suggestByQuestionsAndRules({
                    questions,
                    ruleGroups: item.rule_groups,
                    excludes,
                    unique,
                });

                // 允许试题包含多个知识点，如果多个知识点推荐的题目有重叠，需要去重，即下一个知识点推题的时候会排除上一个知识点已推荐的题目
                let haveManyKidsQuestionIds = [];
                for (let e of result) {
                    for (let r of e.rule_questions) {
                        for (let q of r.questions) {
                            if (q.kns > 1) haveManyKidsQuestionIds.push(q.id);
                        }
                    }
                }
                if (haveManyKidsQuestionIds.length) {
                    _excludes = _excludes.concat(haveManyKidsQuestionIds);
                }

                ret.push({
                    knowledge_id: kid,
                    rule_groups: result,
                });
            }
        }

        resWrapper.succ(ret);
    } catch (err) {
        logger.error(err);
        resWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getSeCond = (req) => {
    let { difficulty, type, exam_type, category, year, province, city, elite, offset, limit = 10, sortBy, dbs = ['basic'] } = req.body;
    offset = +offset || 0;
    limit = +limit || 10;
    year = +year;

    let seCond = {
        limit, offset
    };

    seCond.sort_by = _.isArray(sortBy) && sortBy.length ? sortBy : [{ year: -1 }, { ncite: -1 }];
    if (year) seCond.year = year;
    if (exam_type) seCond.exam_cate = exam_type;
    if (type) seCond.type = type;
    if (province) seCond.province = province;
    if (city) seCond.city = city;
    if (['容易', '较易', '中等', '较难', '困难'].includes(difficulty)) {
        seCond.difficulty = difficulty;
    }
    let ques_cate;
    if (category) {
        switch (category) {
            case '常考题':
                ques_cate = {
                    is_final: { '$ne': 1 },
                };
                break;
            case '压轴题':
                ques_cate = {
                    is_final: 1
                };
                break;
            case '名校精品题':
                seCond.elite = 1;
                break;
            default:
                break;
        }
        if (ques_cate) seCond.ques_cate = ques_cate;
    }
    if (elite) {
        seCond.elite = +elite;
    }
    seCond.dbs = dbs;
    return seCond;
};

// 通过搜索引擎接口筛选试题，供dmp/辅导添加试题调用
const searchQuestions = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let knowledgeIds = req.params.knowledge_ids;
    knowledgeIds = knowledgeIds.split(',');

    let { mult_know_use, fields_type = 'full', mobile_ready } = req.body;
    let seCond = getSeCond(req);

    let questionIds = [];
    let total_num;

    if (!mult_know_use || mult_know_use === 'intersection') {
        seCond.set_mode = {
            knowledges: 'intersection',
        };
    } else if (mult_know_use === 'union') {
        seCond.set_mode = {
            knowledges: 'union'
        };
    }

    let result = await axios.post(SEARCH_QUES_URL, { knowledges: knowledgeIds.join(','), ...seCond });
    if (result.status === 200 && result.data.code === 0) {
        questionIds = result.data.data.datas || result.data.data.docs || [];
        total_num = result.data.data.total || 0;
    } else {
        total_num = 0;
    }

    if (questionIds.length) {
        let mongoCond = {
            _id: { $in: questionIds }
        };

        let ret = {};
        let questionsArr = [];

        if (questionIds.length) {
            questionsArr = await db.collection('question').find(mongoCond, { readPreference: 'secondaryPreferred' }).project({
                _id: 1, comment: 1, blocks: 1, description: 1, type: 1, knowledges: 1,
                period: 1, subject: 1, difficulty: 1, refer_exampapers: 1, refer_times: 1,
                use_type: 1, core_knowledges: 1, elite: 1, year: 1, reco_questions: 1, use_times: 1,
                type_tags: 1, tags: 1, ctime: 1, from: 1, elements: 1,
            }).toArray();

            let idIndex = {};
            for (let i = 0; i < questionIds.length; i++) {
                idIndex[questionIds[i]] = i + 1;
            }
            questionsArr.sort((a, b) => idIndex[a._id] - idIndex[b._id]);
        }
        ret.total_num = total_num || questionsArr.length;

        for (let e of questionsArr) {
            filter_fields(e, fields_type);
        }

        // if ((mobile_ready === true || mobile_ready === 'true') && questionsArr.length) {
        //     let mobiles = await db.collection('question_mobile').find({
        //         _id: { $in: questionsArr.map(e => e.id) },
        //     }, {
        //         readPreference: 'secondaryPreferred',
        //         fields: { _id: 1 },
        //     }).toArray();
        //     let mobileIds = mobiles.map(e => e._id);
        //     for (let q of questionsArr) {
        //         q.mobile_ready = mobileIds.includes(q.id);
        //     }
        // }

        ret.questions = questionsArr;

        resWrapper.succ(ret);
    } else {
        resWrapper.succ({
            total_num: 0,
            questions: [],
        });
    }
};

/**
 * 根据知识点获取近N年中高考试卷试题
 * @param {*} p0 
 * @param {[number]} p0.knowledgeIds
 * @param {string} p0.fieldsType
 * @param {number} p0.n - 近N年
 */
const _getTrueQuestionsByKnowledgeIds = async ({ knowledgeIds, fieldsType, n = 5 }) => {
    let records = await db.collection('knowledge').find({
        _id: { $in: knowledgeIds }
    }).project({ period: 1, subject: 1, name: 1, exampapers: 1 }).toArray();

    let maxYear = new Date().getFullYear() - 1;
    for (let e of records) {
        if (e.exampapers && e.exampapers[0]) {
            maxYear = Math.max(maxYear, e.exampapers[0].to_year);
        }
    }
    let minYear = maxYear - n + 1;
    // 过滤近五年考试
    let quesBuf = {}, quesIds = [];
    for (let record of records) {
        record.id = record._id;
        delete record._id;
        record.examTimes = 0;

        let exampapers = record.exampapers || [];
        let _papers = [];
        if (!exampapers.length) {
            record.yearCount = 0;
            record.quesCount = 0;
            continue;
        }
        record.yearCount = new Set();
        record.quesCount = new Set();
        for (let paper of exampapers) {
            if (paper.to_year < minYear) break;
            record.examTimes++;
            record.yearCount.add(paper.to_year);

            paper.questions.sort((a, b) => a.index - b.index);
            paper.togetherScore = 0;
            for (let q of paper.questions) {
                quesBuf[q.id] ? quesBuf[q.id].push(q) : (quesBuf[q.id] = [q]);
                quesIds.push(q.id);
                paper.togetherScore += q.score || 0;
                record.quesCount.add(q.id);
            }
            _papers.push(paper);
        }
        record.exampapers = _papers;
        record.yearCount = record.yearCount.size;
        record.quesCount = record.quesCount.size;
    }

    let questions = [];
    if (quesIds.length) {
        let proj = fieldsType ? {
            _id: 1,
            comment: 1,
            blocks: 1,
            description: 1,
            type: 1,
            knowledges: 1,
            period: 1,
            subject: 1,
            difficulty: 1,
            refer_exampapers: 1,
            refer_times: 1,
            use_type: 1,
            core_knowledges: 1,
            elite: 1,
            year: 1,
            reco_questions: 1,
            use_times: 1,
            type_tags: 1,
            tags: 1,
            ctime: 1,
            from: 1,
            elements: 1,
        } : {
            'blocks.stems': 1, 'blocks.types': 1, description: 1
        };
        questions = await db.collection('question').find({ _id: { $in: quesIds } }).project(proj).toArray();
    }

    for (let q of questions) {
        let id = q._id;
        if (!quesBuf[q._id]) continue;
        if (fieldsType) {
            filter_fields(q, fieldsType);
        } else {
            delete q._id;
        }

        for (let e of quesBuf[id]) {
            Object.assign(e, q);
        }
    }

    records.sort((a, b) => b.examTimes - a.examTimes);

    return records;
};

/**
 * 获取知识点真卷考试的试题
 * 可指定近最小真卷年份
 * @param {*} req 
 * @param {*} res 
 */
const getTrueQuestionsByKnowledgeIds = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let { fieldsType, knowledgeIds, n } = req.body;

    n = +n ? +n : 5;

    let ret = [];
    if (knowledgeIds && knowledgeIds.length) {
        ret = await _getTrueQuestionsByKnowledgeIds({ knowledgeIds, fieldsType, n });
    }

    resWrapper.succ(ret);
};

module.exports = {
    suggestQuestions,
    searchQuestions,
    suggestQuestionsForMultiSupport,
    getTrueQuestionsByKnowledgeIds
};
