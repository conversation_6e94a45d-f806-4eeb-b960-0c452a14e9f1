const logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
const ObjectID = require('mongodb').ObjectID;

const resourceCol = db.collection('learning_video');
const SCHOOL_ID = 16633; // 默认学校ID（DMP平台区分学校ID，384106为测试学校，16633为正式学校）

/* 预习复习视频 */

// 获取知识点名字
const _getKnowledgesInfo = async (ids) => {
    return await db.collection('knowledge').find({_id: {$in: ids}}).project({
        name: 1, period: 1, subject: 1
    }).toArray();
};

// 获取知识点关联的资源
const _getResourcesByKnowledgeIds = async (ids, school_id) => {
    return await resourceCol.find({
        school_id,
        'knowledges.id': {$in: ids},
        is_del: {$ne: true}
    }).project({is_del: 0}).toArray();
};

const _getResourcesByHash = async (hash, school_id) => {
    return await resourceCol.find({
        school_id,
        hash,
        is_del: {$ne: true},
    }).toArray();
};

// 保存视频资源
const saveResource = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let body = req.body;
    let {name, url, size, hash, icon, duration, user_id, user_name, school_id, period, subject} = body;
    if (!name || !url || !school_id) {
        return resWrapper.error('PARAMETERS_ERROR', '文件名字、链接和学校ID必填');
    }

    try {
        let now = new Date();
        let doc = {
            _id: ObjectID(),
            name,
            url,
            size: size || 0,
            duration: duration || -1,
            hash: hash || '',
            icon: icon || '',
            period: period || '',
            subject: subject || '',
            user_id: user_id || 0,
            user_name: user_name || '',
            school_id: school_id || SCHOOL_ID,
            is_del: false,
            ctime: now,
            utime: now,
        };
        let result = await resourceCol.insertOne(doc);
    
        if (result && result.insertedCount) {
            return resWrapper.succ({id: doc._id});
        }
        throw new Error('学习视频资源保存失败');
    } catch (error) {
        logger.error(error);
        resWrapper.error('HANDLE_ERROR', error.message);
    }
};

// 设置关联知识点
const setKnowledges = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);
    /**
     * id,
     * name,
     * scenes,  学习场景[预习、复习]
     * teacher_name,    主讲老师名字
     * knowledge_ids
     */
    let body = req.body;
    let {id, show_name, hash, icon, period, subject, scenes, teacher_name, knowledge_ids, school_id} = body;
    if (!id || !show_name || !hash || !knowledge_ids || !knowledge_ids.length) {
        return resWrapper.error('PARAMETERS_ERROR', '学段、学科、资源ID、名字、文件Hash和知识点必填');
    }

    try {
        let knowledges = await _getKnowledgesInfo(knowledge_ids);

        if (!knowledges.length) {
            return resWrapper.error('PARAMETERS_ERROR', '知识点不存在');
        }

        if (!period || !subject) {
            period = knowledges[0].period;
            subject = knowledges[0].subject;
        }

        let kidMap = {};
        for (let e of knowledges) {
            kidMap[e._id] = e;
            e.id = e._id;
            delete e._id;
        }
        let rejectIds = new Set(); // 如果知识点已关联相同hash的资源，忽略该知识点（DMP）
        let sameHashRecords = await _getResourcesByHash(hash, school_id);
        for (let e of sameHashRecords) {
            if (!e.knowledges || !e.knowledges.length) continue;
            for (let k of e.knowledges) {
                if (kidMap[k.id]) {
                    rejectIds.add(k.id);
                }
            }
        }

        if (rejectIds.size) {
            knowledges = knowledges.filter(e => !rejectIds.has(e.id));
        }

        if (knowledges.length) {
            for (let e of knowledges) {
                e.show_name = show_name;
                e.scenes = scenes;
                e.teacher_name = teacher_name || '';
            }
            await resourceCol.updateOne({
                _id: ObjectID(id),
            }, {
                $set: {
                    period,
                    subject,
                    knowledges,
                    utime: new Date(),
                    ...(icon ? {icon} : {}),
                }
            });
        }

        return resWrapper.succ({
            rejectKnowledgeIds: [...rejectIds]
        });
    } catch (error) {
        logger.error(error);
        resWrapper.error('HANDLE_ERROR');
    }
};

// 根据知识点获取资源
const getResources = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let {school_id, knowledge_id} = req.query;
    knowledge_id = (knowledge_id || '').split(',').map(e => +e).filter(e => e);
    school_id = +school_id;
    if (!school_id || !knowledge_id.length) {
        return resWrapper.error('PARAMETERS_ERROR', '知识点和学校ID必填');
    }

    try {
        let resources = await _getResourcesByKnowledgeIds(knowledge_id, school_id);
        for (let e of resources) {
            e.id = e._id;
            delete e._id;
        }

        resWrapper.succ(resources);
    } catch (error) {
        logger.error(error);
        resWrapper.error('HANDLE_ERROR');
    }
};

// 编辑视频资源
const editResource = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let {show_name, knowledge_id, teacher_name, scenes, hash, school_id} = req.body;
    if (!show_name || !knowledge_id || !scenes || !scenes.length || !hash || !school_id) {
        return resWrapper.error('PARAMETERS_ERROR', '视频名字、知识点、场景、文件Hash和学校ID必填');
    }
    try {
        let resource = await resourceCol.findOne({
            'knowledges.id': knowledge_id,
            hash,
            school_id,
            is_del: {$ne: true},
        });

        if (!resource) {
            return resWrapper.error('NULL_ERROR');
        }

        for (let e of resource.knowledges) {
            if (e.id === knowledge_id) {
                e.show_name = show_name;
                e.scenes = scenes;
                e.teacher_name = teacher_name || '';
                break;
            }
        }
        let result = await resourceCol.updateOne({_id: resource._id}, {
            $set: {
                knowledges: resource.knowledges,
                utime: new Date(),
            }
        });

        resWrapper.succ(result.result);
    } catch (error) {
        logger.error(error);
        resWrapper.error('HANDLE_ERROR');
    }
};

// 删除指定资源与知识点的关联
const removeKnowledge = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let {knowledge_id, hash, school_id} = req.query;
    school_id = +school_id;
    knowledge_id = +knowledge_id;
    if (!knowledge_id || !hash || !school_id) {
        return resWrapper.error('PARAMETERS_ERROR', '文件Hash、知识点和学校ID必填');
    }
    try {
        let resource = await resourceCol.findOne({
            'knowledges.id': knowledge_id,
            hash,
            school_id,
            is_del: {$ne: true},
        });

        if (!resource) {
            return resWrapper.error('NULL_ERROR');
        }

        resource.knowledges = resource.knowledges.filter(e => e.id !== knowledge_id);
        let result = await resourceCol.updateOne({_id: resource._id}, {
            $set: {
                knowledges: resource.knowledges,
                utime: new Date(),
            }
        });

        resWrapper.succ(result.result);
    } catch (error) {
        logger.error(error);
        resWrapper.error('HANDLE_ERROR');
    }
};

// 统计知识点下的资源量
const countByKnowledges = async (req, res) => {
    const resWrapper = new ResponseWrapper(res);

    let {knowledge_ids, school_id} = req.body;

    let resources = await resourceCol.find({
        'knowledges.id': {$in: knowledge_ids},
        school_id,
        is_del: {$ne: true},
    }).project({'knowledges.id': 1}).toArray();

    let countObj = {};

    for (let e of resources) {
        for (let k of e.knowledges) {
            countObj[k.id] ? (countObj[k.id]++) : (countObj[k.id] = 1);
        }
    }

    resWrapper.succ(countObj);
};

module.exports = {
    saveResource,
    setKnowledges,
    getResources,
    editResource,
    removeKnowledge,
    countByKnowledges
};
