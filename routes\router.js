/*
 * Cuiyunfeng
 * 2018-11-29
 */
class Router {
    constructor(router, middleware) {
        this.router = router;
        this.middleware = middleware;
        // Only support 4 methods!
        let that = this;
        Object.values(['get', 'post', 'delete', 'put']).forEach( function (x) {
            that[x] = that.method(x);
        });
    }
    plugin(params) {
        // This statement does not throw an exception 
        // unless Node.js Version does not support ES6
        let args = Array.from(params);
        let path = args.shift();
        if (this.middleware) {
            args.unshift(this.middleware);
        }
        args.unshift(path);
        return args;
    }
    method(name) {
        return function () {
            let args = this.plugin(arguments);
            // Can't use arguments.callee!
            this.router[name].apply(this.router, args);
        };
    }
}

module.exports = Router;