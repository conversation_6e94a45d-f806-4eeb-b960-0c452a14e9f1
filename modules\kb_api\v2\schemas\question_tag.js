
const value = {
    type: 'object',
    required: ['name'],
    properties: {
        name: {
            type: 'string',
        },
        sub_values: {
            type: 'array',
            default: []
        }
    }
};

const putQuestionTag = {
    type: 'object',
    additionalProperties: false,
    required: ['subject', 'period', 'type', 'name', 'tag_type', 'values'],
    properties: {
        subject: {
            type: 'string',
        },
        period: {
            type: 'string',
        },
        type: {
            type: 'string',
        },
        name: {
            type: 'string',
        },
        tag_type: {
            type: 'string',
        },
        values: {
            type: 'array',
            minItems: 1,
            items: value
        }
    }
};

module.exports = {
    putQuestionTag,
};
