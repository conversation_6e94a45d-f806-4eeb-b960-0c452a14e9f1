/**
 * author: cuihovah
 * date: 2016-02-27
 * description: This script file is used to implement the volume function.
 */
var _ = require('underscore');
const mongodber = require('../../../common/utils/mongodber');
const db = mongodber.use('KB');
var Config = require('config');
var app_key = Config.get('app_keys')[0];
var Logger = require('../../../common/utils/logger');
const ResponseWrapper = require('../../../common/utils/response_wrapper');
const axios = require('axios');
const url = require('url');
const qs = require('qs');
const { ObjectID } = require('mongodb');

const getExampaperById = async (exampaper_id, dbs) => {
	if (!dbs) {
		dbs = 'basic';
	}
	var uri = url.format({
		protocol: 'http',
		hostname: Config.get('host_internal'),
		pathname: `/kb_api/v2/exampapers/${exampaper_id}`,
		search: qs.stringify({
			api_key: app_key,
			dbs
		})
	});
	let response = await axios.get(uri);
	return response && response.data;
}

const getQuestionsByIds = async (question_ids, dbs) => {
	var uri = url.format({
		protocol: 'http',
		hostname: Config.get('host_internal'),
		pathname: `/kb_api/v2/questions/`,
		search: qs.stringify({
			api_key: app_key,
		})
	});
	let response = await axios.post(uri, {
		question_ids: question_ids,
		fields_type: 'full',
		dbs: dbs
	});
	return response.data;
}


/**
 * 更新资源的查看次数
 * @param {*} ids 
 * @param {*} type 
 * @param {*} callback 
 */
function inscreaceDownTimes(ids, type, callback) {
	if (['question', 'exampaper'].indexOf(type) === -1) {
		return callback();
	}
	db.collection(type).updateMany(
		{ _id: { $in: ids } },
		{ '$inc': { 'download_times': 1 } },
		callback
	);
}
const tiku_exampaper_assembly = async (req, res) => {
	try {
		const instance = axios.create({
			baseURL: Config.get('kb_util_server').baseURI,
			timeout: 20000,
			responseType: 'stream'
		});
		//记录组卷次数
		inscreaceDownTimes(req.body.resource_ids, req.body.resource_type, () => { });
		let response = await instance.post('/utilbox_api/v1/exampapers/documents', req.body);
		res.set(response.headers);
		response.data.pipe(res);
	} catch (err) {
		Logger.error(err.stack);
		Logger.error(err.message);
		return res.status(500).end();
	}
}

const downloadExampaper = async (req, res) => {
	try {
		let exampaperId = Number(req.params.exampaper_id);
		let data = await getExampaperById(exampaperId, req.query.dbs);
		if (!data) {
			return res.status(500).end();
		}
		const instance = axios.create({
			baseURL: Config.get('kb_util_server').baseURI,
			timeout: 20000,
			responseType: 'stream'
		});
		let uri = '/utilbox_api/v1/exampapers/documents?type=json';
		if (req.query.fields_type === 'full') {
			uri = [uri, 'fields_type=full'].join('&');
		}
		let response = await instance.post(uri, data);
		await db.collection('exampaper').update(
			{ _id: exampaperId },
			{ '$inc': { "download_times": 1 } });
		res.set(response.headers);
		response.data.pipe(res);
	} catch (err) {
		return res.status(500).end();
	}
}

const downloadQuestions = async (req, res) => {
	try {
		let question_ids = _.map(req.params.question_ids.split(','), (x) => {
			return Number(x);
		});
		let queryDbs = (req.query.dbs || 'basic').split(',');
		let data = await getQuestionsByIds(question_ids, queryDbs);
		const instance = axios.create({
			baseURL: Config.get('kb_util_server').baseURI,
			timeout: 20000,
			responseType: 'stream'
		});
		let uri = '/utilbox_api/v1/exampapers/documents?type=json';
		if (req.query.fields_type === 'full') {
			uri = [uri, 'fields_type=full'].join('&');
		}
		let response = await instance.post(uri, {
			name: String(Date.now()),
			blocks: [{
				title: '',
				questions: data
			}]
		});
		res.set(response.headers);
		response.data.pipe(res);
	} catch (err) {
		console.log('err', err);
		return res.status(500).end();
	}
}

const updateQuestionsUseTimes = async (req, res) => {
	let resWrap = new ResponseWrapper(res);
	try {
		let questionIds = req.body;
		if (questionIds.length > 0) {
			for (let i = 0; i < questionIds.length; i++) {
				let qId = Number(questionIds[i]);
				let question = await db.collection('question').find({ _id: qId }).project({ _id: 1, use_times: 1 }).toArray();
				let useTimes = 1;
				if (question && question[0].use_times)
					useTimes += Number(question[0].use_times);
				await db.collection('question').update({ _id: qId }, { $set: { use_times: useTimes } });
			}
		}
		return resWrap.succ({});
	} catch (err) {
		Logger.error(err);
		return resWrap.error('HANDLE_ERROR');
	}
};

const getAlbumList = async (req, res) => {
	let resWrap = new ResponseWrapper(res);
	let period = req.query.period;
	let press_version = req.query.press_version;
	let subject = req.query.subject;
	let year = req.query.year;
	let album_type = req.query.album_type;
	let offset = Number(req.query.offset || 0);
	let limit = Number(req.query.limit || 10);
	const queryDbs = (req.query.dbs || 'basic').split(',');

	let cond = {};
	if (!period || !subject) {
		return resWrap.error('PARAMETERS_ERROR', 'period 和 subject 为必传字段');
	}
	if (press_version) {
		cond['press_version'] = press_version;
	}
	if (year) {
		cond['cyear'] = parseInt(year);
	}
	if (album_type) {
		cond['album_type'] = album_type;
	}
	cond['period'] = period;
	cond['subject'] = subject;
	cond['dbs'] = { $in: queryDbs };
	try {
		cond['chapter_exam'] = {
			$not: { $size: 0 }
		};
		let list = await db.collection('album').find(cond).limit(limit).skip(offset).sort({ utime: -1 }).toArray();
		let total_num = await db.collection('album').find(cond).count();
		for (const value of list) {
			if (value) {
				value.id = value._id.toString();
				delete value._id;
				value.chapter_exam = value.chapter_exam.filter(i => Object.values(i).filter(i => i).length > 0);
			}
		}
		return resWrap.succ({ list: list, total_num: total_num });
	} catch (err) {
		Logger.error(err);
		return resWrap.error('HANDLE_ERROR');
	}
};

const getAlbumInfoById = async (req, res) => {
	let resWrap = new ResponseWrapper(res);
	let id = req.params.id.toString();
	let type = req.query.type;
	if (!id) {
		return resWrap.error('PARAMETERS_ERROR', 'id 为必传字段');
	}
	const queryDbs = new Set((req.query.dbs || 'basic').split(','));
	try {
		let result;
		if (type === 'category_id') {
			result = await db.collection('album').findOne({ book_id: parseInt(id) });
		} else {
			result = await db.collection('album').findOne({ _id: ObjectID(id) });
			if (result) {
				await db.collection('album').updateOne({ _id: ObjectID(id) }, {
					$inc: { view_times: 1 }
				});
				result.view_times = result.view_times + 1;
			}
		}
		if (result) {
			if (!(result.dbs || ['basic']).some(item => queryDbs.has(item))) {
				resWrap.error('PARAMETERS_ERROR', '没有权限访问该资源');
			}
			result.id = result._id.toString();
			delete result._id;
			result.chapter_exam = result.chapter_exam.filter(i => Object.values(i).filter(i => i).length > 0);
		}
		return resWrap.succ(result);
	} catch (err) {
		Logger.error(err);
		return resWrap.error('HANDLE_ERROR');
	}
};

module.exports = {
	assembleExampaper: tiku_exampaper_assembly,
	downloadExampaper: downloadExampaper,
	downloadQuestions: downloadQuestions,
	updateQuestionsUseTimes: updateQuestionsUseTimes,
	getAlbumList: getAlbumList,
	getAlbumInfoById: getAlbumInfoById
}
