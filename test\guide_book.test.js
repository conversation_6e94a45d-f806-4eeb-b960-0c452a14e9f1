/* eslint-disable no-undef */
const config = require('config');
//const cookies = require('./config.js').config;
//const DMPSERVER = config.get('KB_API_SERVER');
const expect = require('chai').expect;
const superagent = require('superagent');
const url = require('url');

const port = process.env.NODE_PORT || 80;

const host = url.format({
    protocol: 'http',
    hostname: config.get('host'),
    port: port
});
const apikey = config.get('app_keys_private')[0];
const apiKeyStr = `app_key=${apikey}`;

var guideBookId = 0;
var guideChapterId = 0;

describe('KB_API:获取课程打包信息', function () {


    it('正例测试：获取天天练习教辅课程打包信息', function (done) {
        let url = [host, '/kb_api/v2/guide_books', '?', apiKeyStr, '&', 'app_name=', encodeURIComponent('天天练习')].join('');
        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.guide_books.children).to.be.an('array');
                if (ret.data.guide_books.children.length > 0) {
                    for (let index = 0; index < ret.data.length; ++index) {
                        expect(ret.data.guide_books.children[index]).to.have.keys('key', 'name', 'children');
                    }
                }
                guideBookId = ret.data.guide_books.children[0].children[0].children[0].children[0].children[0].children[0].id;

                done();
            });
    });
    it('正例测试：获取超级听写教辅课程打包信息', function (done) {
        let url = [host, '/kb_api/v2/guide_books', '?', apiKeyStr, '&', 'app_name=', encodeURIComponent('超级听写')].join('');
        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.guide_books.children).to.be.an('array');
                if (ret.data.guide_books.children.length > 0) {
                    for (let index = 0; index < ret.data.length; ++index) {
                        expect(ret.data.guide_books.children[index]).to.have.keys('key', 'name', 'children');
                    }
                }


                done();
            });
    });
});


describe('KB_API:根据教辅id获取教辅内容', function () {

    it('正例测试：根据教辅id获取教辅内容', function (done) {
        let url = [host, `/kb_api/v2/guide_books/${guideBookId}`, '?', apiKeyStr].join('');
        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.guide_book.children).to.be.an('array');
                if (ret.data.guide_book.children.length > 0) {

                    guideChapterId = ret.data.guide_book.children[0].children[0].id
                }
                done();
            });
    });
});

describe('KB_API:根据教辅章节id获取其中试题', function () {

    it('正例测试：根据教辅章节id获取其中试题', function (done) {
        let url = [host, `/kb_api/v2/guide_chapters/${guideChapterId}`, '?', apiKeyStr].join('');
        console.log(url);
        superagent
            .get(url)
            .end(function (err, res) {
                expect(err).to.be.an('null');
                expect(res).to.not.be.an('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.questions).to.be.an('array');

                done();
            });
    });
});