/**
 * 接口功能：查询和修改数据，传入表名，mongo条件
 */
const ObjectID = require('mongodb').ObjectID;
const Logger = require('../../common/utils/logger');
const ResponseWrapper = require('../../common/utils/response_wrapper');
const params_utils = require('../../common/utils/params.js');
const mongodber = require('../../common/utils/mongodber');
const db = mongodber.use('KB');
const kb_api = require('../config.js').kb_api['v2'];

/*
 * 函数描述:
 * 		基于上送条件，请求基础资源库中的内容
 * URL: 
 * 		http://kboe.yunxiao.com/kb_api/v2/resources/
 * Method: 
 * 		GET
 * Author: 
 * 		zhangjun
 * Date:
 * 		2016-11-01
 */
async function resources(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	try {
		let fun_name = arguments.callee.name;
		let params = params_utils.create_params(req, kb_api[fun_name]);
		let coll_name = params['collection'];
		let coll = db.collection(coll_name);
		let query = params['query'];
		if (query && query._id && 'string' === typeof query._id && 24 === query._id.length) {
			query._id = ObjectID(query._id);
		}
		let proj = params['project'];
		let limit = params['limit'];
		let offset = params['offset'];
		let result = await coll.find(query).project(proj).skip(offset).limit(limit).toArray();
		responseWrapper.succ(result);
	} catch (err) {
		Logger.error(err);
		responseWrapper.error('NULL_ERROR');
	}
}

/*
 * 函数描述:
 * 		基于上送条件，修改基础资源库中的内容
 * URL: 
 * 		http://kboe.yunxiao.com/kb_api/v2/resources/
 * Method: 
 * 		POST
 * Author: 
 * 		zhangjun
 * Date:
 * 		2016-11-25
 */
async function put_resources(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	try {
		let fun_name = arguments.callee.name;
		let params = params_utils.create_params(req, kb_api[fun_name]);
		let coll_name = params['collection'];
		let coll = db.collection(coll_name);
		let query = params['query'];
		if (query && query._id && 'string' === typeof query._id && 24 === query._id.length) {
			query._id = ObjectID(query._id);
		}
		let update = params['update'];
		let options = params['options'];
		let result = await coll.updateMany(query, update, options);
		let obj = {
			matchedCount: result.matchedCount,
			modifiedCount: result.modifiedCount,
		};
		return responseWrapper.succ(obj);
	} catch (err) {
		Logger.error(err);
		return responseWrapper.error('HANDLE_ERROR', err);
	}
}

/**
 * 获取索引
 * @param {Object} req 
 * @param {Object} res 
 * @returns {Object}
 */
async function get_resources_indexes(req, res) {
	let responseWrapper = new ResponseWrapper(res);
	try {
		let fun_name = arguments.callee.name;
		let params = params_utils.create_params(req, kb_api[fun_name]);
		let coll_name = params['collection'];
		let obj = await db.collection(coll_name).indexes();
		return responseWrapper.succ(obj);
	} catch (err) {
		Logger.error(err);
		return responseWrapper.error('HANDLE_ERROR', err);
	}
}

module.exports = {
	resources: resources,
	put_resources: put_resources,
	get_resources_indexes
};
